#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار الإصلاحات الجديدة
Test New Fixes
"""

import os
import sys
import tempfile
import pandas as pd

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_beneficiaries_import():
    """اختبار استيراد المستفيدين"""
    print("🧪 اختبار استيراد المستفيدين...")
    
    try:
        # إنشاء بيانات تجريبية
        test_data = [
            {
                'الاسم': 'مستفيد اختبار 1',
                'الرقم العام': 'TEST001',
                'الرتبة': 'نقيب',
                'الإدارة': 'إدارة الاختبار',
                'الوحدة': 'وحدة الاختبار'
            }
        ]
        
        # إنشاء ملف Excel مؤقت
        temp_file = tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False)
        temp_path = temp_file.name
        temp_file.close()
        
        df = pd.DataFrame(test_data)
        df.to_excel(temp_path, index=False)
        
        print(f"📁 تم إنشاء ملف الاختبار: {temp_path}")
        
        # اختبار دالة الاستيراد
        from utils.excel_import_manager import ExcelImportManager
        
        def progress_callback(progress, message):
            print(f"   📊 {progress:.1f}% - {message}")
        
        def cancel_check():
            return False
        
        # تنفيذ الاستيراد
        result = ExcelImportManager.import_beneficiaries_from_excel(
            temp_path,
            progress_callback=progress_callback,
            cancel_check=cancel_check
        )
        
        print(f"📊 نتائج الاستيراد:")
        print(f"   ✅ نجح: {result.success_count}")
        print(f"   🔄 مكرر: {result.duplicate_count}")
        print(f"   ❌ أخطاء: {result.error_count}")
        
        # تنظيف الملف المؤقت
        os.unlink(temp_path)
        
        # تنظيف البيانات التجريبية
        from database import db_manager
        db_manager.execute_query("DELETE FROM beneficiaries WHERE number LIKE 'TEST%'")
        
        return result.success_count > 0
        
    except Exception as e:
        print(f"❌ خطأ في اختبار استيراد المستفيدين: {e}")
        import traceback
        print(f"تفاصيل الخطأ: {traceback.format_exc()}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار الإصلاحات الجديدة")
    print("=" * 50)
    
    # اختبار استيراد المستفيدين
    ben_success = test_beneficiaries_import()
    
    print("\n" + "=" * 50)
    print("📊 ملخص نتائج الاختبار")
    print("=" * 50)
    
    print(f"👥 استيراد المستفيدين: {'✅ نجح' if ben_success else '❌ فشل'}")
    
    if ben_success:
        print("\n🎉 الاختبار نجح!")
        print("✅ إصلاح استيراد المستفيدين يعمل بشكل صحيح")
    else:
        print("\n⚠️ الاختبار فشل")
        print("❌ مشكلة في استيراد المستفيدين")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
