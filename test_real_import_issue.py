#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مشكلة الاستيراد الفعلية في الجدول التنظيمي
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import db_manager
from models import OrganizationalChart
import pandas as pd
import tempfile
import time

def create_test_excel_file():
    """إنشاء ملف Excel تجريبي للاختبار"""
    test_data = {
        'اسم الصنف': ['اختبار استيراد 1', 'اختبار استيراد 2', 'اختبار استيراد 3'],
        'رقم الصنف': ['IMPORT001', 'IMPORT002', 'IMPORT003'],
        'اسم المعدة': ['معدة استيراد 1', 'معدة استيراد 2', 'معدة استيراد 3'],
        'الكمية': [5, 10, 15],
        'الملاحظات': ['ملاحظة 1', 'ملاحظة 2', 'ملاحظة 3']
    }
    
    df = pd.DataFrame(test_data)
    
    # حفظ الملف مؤقتاً
    with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as temp_file:
        temp_file_path = temp_file.name
        df.to_excel(temp_file_path, index=False, engine='openpyxl')
    
    return temp_file_path

def check_database_before_after(operation_name):
    """فحص قاعدة البيانات قبل وبعد العملية"""
    print(f"\n📊 فحص قاعدة البيانات {operation_name}:")
    
    try:
        # فحص إجمالي البيانات
        total_result = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart")
        total_count = total_result[0] if total_result else 0
        
        # فحص البيانات النشطة
        active_result = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart WHERE is_active = 1")
        active_count = active_result[0] if active_result else 0
        
        # فحص البيانات غير النشطة
        inactive_result = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart WHERE is_active = 0")
        inactive_count = inactive_result[0] if inactive_result else 0
        
        print(f"   📦 إجمالي البيانات: {total_count}")
        print(f"   ✅ البيانات النشطة: {active_count}")
        print(f"   ❌ البيانات غير النشطة: {inactive_count}")
        
        # فحص البيانات التجريبية
        test_data = db_manager.fetch_all("""
            SELECT id, sequence_number, item_name, item_code, is_active, created_at 
            FROM organizational_chart 
            WHERE item_code LIKE 'IMPORT%'
            ORDER BY created_at DESC
        """)
        
        if test_data:
            print(f"   🧪 البيانات التجريبية ({len(test_data)} عنصر):")
            for item in test_data:
                status = "نشط" if item['is_active'] else "غير نشط"
                print(f"      • ID: {item['id']}, التسلسل: {item['sequence_number']}, الاسم: {item['item_name']}, الرقم: {item['item_code']}, الحالة: {status}")
        else:
            print("   🧪 لا توجد بيانات تجريبية")
        
        return {
            'total': total_count,
            'active': active_count,
            'inactive': inactive_count,
            'test_items': len(test_data) if test_data else 0
        }
        
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {e}")
        return None

def test_model_get_all():
    """اختبار دالة get_all في النموذج"""
    print("\n🔍 اختبار دالة OrganizationalChart.get_all():")
    
    try:
        # اختبار get_all مع active_only=True (الافتراضي)
        active_items = OrganizationalChart.get_all(active_only=True)
        print(f"   ✅ العناصر النشطة: {len(active_items)}")
        
        # اختبار get_all مع active_only=False
        all_items = OrganizationalChart.get_all(active_only=False)
        print(f"   📦 جميع العناصر: {len(all_items)}")
        
        # البحث عن العناصر التجريبية
        test_items = [item for item in active_items if item.item_code and item.item_code.startswith('IMPORT')]
        print(f"   🧪 العناصر التجريبية النشطة: {len(test_items)}")
        
        if test_items:
            print("   📋 تفاصيل العناصر التجريبية النشطة:")
            for item in test_items:
                print(f"      • {item.item_name} (رقم: {item.item_code}, تسلسل: {item.sequence_number})")
        
        return len(test_items)
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النموذج: {e}")
        import traceback
        print(f"تفاصيل الخطأ: {traceback.format_exc()}")
        return 0

def simulate_ui_import():
    """محاكاة عملية الاستيراد من الواجهة"""
    print("\n🎭 محاكاة عملية الاستيراد من الواجهة:")
    
    try:
        # إنشاء ملف Excel تجريبي
        excel_file = create_test_excel_file()
        print(f"📁 تم إنشاء ملف Excel: {excel_file}")
        
        # تنظيف البيانات التجريبية السابقة
        db_manager.execute_query("DELETE FROM organizational_chart WHERE item_code LIKE 'IMPORT%'")
        print("🧹 تم تنظيف البيانات التجريبية السابقة")
        
        # فحص الحالة قبل الاستيراد
        before_status = check_database_before_after("قبل الاستيراد")
        
        # استيراد البيانات باستخدام نفس الطريقة المستخدمة في الواجهة
        print("\n📥 بدء عملية الاستيراد...")
        from utils.organizational_chart_import import import_organizational_chart_from_excel
        
        # محاكاة progress_callback
        def progress_callback(progress, message):
            print(f"   📊 {progress:.1f}% - {message}")
        
        # محاكاة cancel_check
        def cancel_check():
            return False
        
        # تنفيذ الاستيراد
        result = import_organizational_chart_from_excel(
            excel_file, 
            progress_callback=progress_callback,
            cancel_check=cancel_check
        )
        
        print(f"\n📊 نتائج الاستيراد:")
        print(f"   ✅ نجح: {result.success_count}")
        print(f"   🔄 مكرر: {result.duplicate_count}")
        print(f"   ❌ أخطاء: {result.error_count}")
        print(f"   ⏱️ الوقت: {result.processing_time:.2f} ثانية")
        
        if result.errors:
            print("   📝 تفاصيل الأخطاء:")
            for error in result.errors:
                print(f"      • {error}")
        
        # فحص الحالة بعد الاستيراد مباشرة
        after_status = check_database_before_after("بعد الاستيراد مباشرة")
        
        # محاكاة الإصلاح الإضافي الذي يحدث في الواجهة
        print("\n🔧 تطبيق الإصلاح الإضافي (محاكاة الواجهة):")
        try:
            activated_result = db_manager.execute_query("""
                UPDATE organizational_chart
                SET is_active = 1
                WHERE is_active = 0
                AND created_at > datetime('now', '-2 minutes')
            """)
            activated_count = activated_result.rowcount if activated_result else 0
            print(f"✅ تم تفعيل {activated_count} عنصر إضافي")
        except Exception as e:
            print(f"❌ فشل في الإصلاح الإضافي: {e}")
        
        # فحص الحالة بعد الإصلاح
        final_status = check_database_before_after("بعد الإصلاح الإضافي")
        
        # اختبار النموذج
        model_test_count = test_model_get_all()
        
        # تنظيف البيانات التجريبية
        print("\n🧹 تنظيف البيانات التجريبية...")
        db_manager.execute_query("DELETE FROM organizational_chart WHERE item_code LIKE 'IMPORT%'")
        
        # حذف الملف المؤقت
        try:
            os.unlink(excel_file)
        except:
            pass
        
        # تحليل النتائج
        print(f"\n📋 تحليل النتائج:")
        print(f"   📥 عناصر مستوردة: {result.success_count}")
        print(f"   💾 عناصر في قاعدة البيانات: {final_status['test_items'] if final_status else 0}")
        print(f"   👁️ عناصر مرئية في النموذج: {model_test_count}")
        
        success = (result.success_count > 0 and 
                  final_status and final_status['test_items'] > 0 and 
                  model_test_count > 0)
        
        if success:
            print("✅ الاستيراد نجح - البيانات مرئية")
        else:
            print("❌ الاستيراد فشل - البيانات غير مرئية")
            
            # تشخيص المشكلة
            if result.success_count == 0:
                print("   🔍 السبب: فشل في استيراد البيانات")
            elif not final_status or final_status['test_items'] == 0:
                print("   🔍 السبب: البيانات لم تُحفظ في قاعدة البيانات")
            elif model_test_count == 0:
                print("   🔍 السبب: البيانات محفوظة لكن غير مرئية في النموذج")
        
        return success
        
    except Exception as e:
        print(f"❌ خطأ في محاكاة الاستيراد: {e}")
        import traceback
        print(f"تفاصيل الخطأ: {traceback.format_exc()}")
        return False

def main():
    """تشغيل الاختبار الشامل"""
    print("🚀 بدء اختبار مشكلة الاستيراد الفعلية")
    print("=" * 70)
    
    # فحص الحالة الحالية
    print("📊 فحص الحالة الحالية:")
    current_status = check_database_before_after("الحالة الحالية")
    
    # محاكاة عملية الاستيراد
    print("\n" + "="*50)
    success = simulate_ui_import()
    
    print("\n" + "="*70)
    if success:
        print("🎉 الاختبار نجح - لا توجد مشكلة في الاستيراد")
        print("💡 المشكلة قد تكون في:")
        print("   • تحديث الواجهة بعد الاستيراد")
        print("   • ملف Excel المستخدم")
        print("   • إعدادات النظام")
    else:
        print("❌ الاختبار فشل - المشكلة موجودة")
        print("🔍 يجب فحص:")
        print("   • دالة الاستيراد")
        print("   • حفظ البيانات")
        print("   • تفعيل البيانات")
    
    return success

if __name__ == "__main__":
    main()
