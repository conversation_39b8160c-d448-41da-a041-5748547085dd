#!/usr/bin/env python3
"""
إصلاح مشاكل الترميز في التطبيق
Fix Encoding Issues in Application
"""

import os
import re
from pathlib import Path

def remove_emoji_from_file(file_path):
    """إزالة الرموز التعبيرية من ملف"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إزالة الرموز التعبيرية
        emoji_pattern = re.compile(
            "["
            "\U0001F600-\U0001F64F"  # emoticons
            "\U0001F300-\U0001F5FF"  # symbols & pictographs
            "\U0001F680-\U0001F6FF"  # transport & map symbols
            "\U0001F1E0-\U0001F1FF"  # flags (iOS)
            "\U00002702-\U000027B0"
            "\U000024C2-\U0001F251"
            "]+", flags=re.UNICODE)
        
        # استبدال الرموز التعبيرية بنص
        emoji_replacements = {
            '🏪': '[متجر]',
            '📦': '[صندوق]',
            '🚀': '[صاروخ]',
            '✅': '[نجح]',
            '❌': '[فشل]',
            '📊': '[رسم بياني]',
            '💾': '[حفظ]',
            '🔧': '[أدوات]',
            '📁': '[مجلد]',
            '📄': '[ملف]',
            '⚠️': '[تحذير]',
            '💡': '[فكرة]',
            '🎯': '[هدف]',
            '🎉': '[احتفال]',
            '🔄': '[تحديث]',
            '🗑️': '[حذف]',
            '📈': '[نمو]',
            '📋': '[قائمة]',
            '🧪': '[اختبار]',
            '🛠️': '[إصلاح]',
            '💻': '[كمبيوتر]',
            '📝': '[كتابة]',
            '🔐': '[أمان]',
            '⏱️': '[وقت]',
            '🎨': '[فن]'
        }
        
        # استبدال الرموز المعروفة
        for emoji, replacement in emoji_replacements.items():
            content = content.replace(emoji, replacement)
        
        # إزالة أي رموز متبقية
        content = emoji_pattern.sub('', content)
        
        # كتابة الملف المحدث
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        return True
        
    except Exception as e:
        print(f"خطأ في معالجة {file_path}: {e}")
        return False

def fix_encoding_in_project():
    """إصلاح مشاكل الترميز في المشروع"""
    print("إصلاح مشاكل الترميز...")
    print("=" * 40)
    
    # الملفات المراد معالجتها
    files_to_fix = [
        "main.py",
        "database.py",
        "utils/logger.py",
        "async_operations.py",
        "performance_optimizer.py"
    ]
    
    for file_path in files_to_fix:
        if Path(file_path).exists():
            if remove_emoji_from_file(file_path):
                print(f"✓ تم إصلاح {file_path}")
            else:
                print(f"✗ فشل في إصلاح {file_path}")
        else:
            print(f"- {file_path} غير موجود")
    
    print("\nتم الانتهاء من إصلاح مشاكل الترميز")

if __name__ == "__main__":
    fix_encoding_in_project()