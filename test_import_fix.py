#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح مشكلة الاستيراد في الجدول التنظيمي
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import db_manager
from models import OrganizationalChart
import pandas as pd
import tempfile

def test_import_and_display():
    """اختبار الاستيراد وعرض البيانات"""
    print("🧪 اختبار إصلاح مشكلة الاستيراد...")
    
    try:
        # إنشاء بيانات تجريبية للاستيراد
        test_data = {
            'اسم الصنف': ['اختبار الإصلاح 1', 'اختبار الإصلاح 2'],
            'رقم الصنف': ['FIX001', 'FIX002'],
            'اسم المعدة': ['معدة الإصلاح 1', 'معدة الإصلاح 2'],
            'الكمية': [10, 20],
            'الملاحظات': ['ملاحظة 1', 'ملاحظة 2']
        }
        
        df = pd.DataFrame(test_data)
        
        # حفظ الملف مؤقتاً
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as temp_file:
            temp_file_path = temp_file.name
            df.to_excel(temp_file_path, index=False, engine='openpyxl')
        
        print(f"📁 تم إنشاء ملف Excel: {temp_file_path}")
        
        # تنظيف البيانات التجريبية السابقة
        db_manager.execute_query("DELETE FROM organizational_chart WHERE item_code LIKE 'FIX%'")
        print("🧹 تم تنظيف البيانات التجريبية السابقة")
        
        # فحص الحالة قبل الاستيراد
        before_count = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart WHERE is_active = 1")[0]
        print(f"📊 البيانات النشطة قبل الاستيراد: {before_count}")
        
        # تنفيذ الاستيراد
        print("\n📥 بدء عملية الاستيراد...")
        from utils.organizational_chart_import import import_organizational_chart_from_excel
        
        def progress_callback(progress, message):
            print(f"   📊 {progress:.1f}% - {message}")
        
        def cancel_check():
            return False
        
        result = import_organizational_chart_from_excel(
            temp_file_path, 
            progress_callback=progress_callback,
            cancel_check=cancel_check
        )
        
        print(f"\n📊 نتائج الاستيراد:")
        print(f"   ✅ نجح: {result.success_count}")
        print(f"   🔄 مكرر: {result.duplicate_count}")
        print(f"   ❌ أخطاء: {result.error_count}")
        
        # تطبيق الإصلاحات الجديدة
        print("\n🔧 تطبيق الإصلاحات الجديدة...")
        
        # إصلاح 1: تفعيل البيانات المستوردة حديثاً
        activated_count1 = db_manager.execute_query("""
            UPDATE organizational_chart
            SET is_active = 1
            WHERE is_active = 0
            AND created_at > datetime('now', '-5 minutes')
        """).rowcount
        
        # إصلاح 2: تفعيل البيانات بناءً على أرقام التسلسل الحديثة
        max_sequence = db_manager.fetch_one("SELECT MAX(sequence_number) FROM organizational_chart WHERE is_active = 1")
        if max_sequence and max_sequence[0]:
            min_sequence = max(1, max_sequence[0] - result.success_count)
            activated_count2 = db_manager.execute_query("""
                UPDATE organizational_chart
                SET is_active = 1
                WHERE sequence_number >= ? AND is_active = 0
            """, (min_sequence,)).rowcount
        else:
            activated_count2 = 0
        
        # إصلاح 3: تفعيل آخر البيانات المضافة
        activated_count3 = db_manager.execute_query("""
            UPDATE organizational_chart
            SET is_active = 1
            WHERE id IN (
                SELECT id FROM organizational_chart 
                WHERE is_active = 0 
                ORDER BY id DESC 
                LIMIT ?
            )
        """, (result.success_count,)).rowcount
        
        total_activated = activated_count1 + activated_count2 + activated_count3
        print(f"✅ تم تفعيل {total_activated} عنصر (طريقة 1: {activated_count1}, طريقة 2: {activated_count2}, طريقة 3: {activated_count3})")
        
        # فحص الحالة بعد الإصلاح
        after_count = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart WHERE is_active = 1")[0]
        print(f"📊 البيانات النشطة بعد الإصلاح: {after_count}")
        
        # اختبار النموذج
        print("\n🔍 اختبار عرض البيانات من النموذج...")
        items = OrganizationalChart.get_all(active_only=True)
        test_items = [item for item in items if item.item_code and item.item_code.startswith('FIX')]
        
        print(f"✅ يمكن عرض {len(items)} عنصر إجمالي من النموذج")
        print(f"🧪 العناصر التجريبية المرئية: {len(test_items)}")
        
        if test_items:
            print("📋 تفاصيل العناصر التجريبية:")
            for item in test_items:
                print(f"   • {item.item_name} (رقم: {item.item_code}, تسلسل: {item.sequence_number}, نشط: {item.is_active})")
        
        # تنظيف البيانات التجريبية
        print("\n🧹 تنظيف البيانات التجريبية...")
        db_manager.execute_query("DELETE FROM organizational_chart WHERE item_code LIKE 'FIX%'")
        
        # حذف الملف المؤقت
        try:
            os.unlink(temp_file_path)
        except:
            pass
        
        # تحليل النتائج
        success = (result.success_count > 0 and len(test_items) > 0)
        
        print(f"\n📋 تحليل النتائج:")
        print(f"   📥 عناصر مستوردة: {result.success_count}")
        print(f"   👁️ عناصر مرئية في النموذج: {len(test_items)}")
        print(f"   🔧 عناصر تم تفعيلها: {total_activated}")
        
        if success:
            print("✅ الإصلاح نجح - البيانات مرئية بعد الاستيراد")
        else:
            print("❌ الإصلاح فشل - البيانات لا تزال غير مرئية")
        
        return success
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        print(f"تفاصيل الخطأ: {traceback.format_exc()}")
        return False

def main():
    """تشغيل الاختبار"""
    print("🚀 بدء اختبار إصلاح مشكلة الاستيراد")
    print("=" * 60)
    
    success = test_import_and_display()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 الإصلاح نجح!")
        print("💡 الآن يجب أن تظهر البيانات بعد الاستيراد من Excel")
    else:
        print("❌ الإصلاح لم ينجح")
        print("🔍 يحتاج إلى مزيد من التحقيق")
    
    return success

if __name__ == "__main__":
    main()
