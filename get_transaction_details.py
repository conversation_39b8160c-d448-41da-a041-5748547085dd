#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import sys
import os

# إضافة مسار المشروع
sys.path.append('.')

try:
    from database import db_manager
    
    print('🔍 تفاصيل المعاملة التجريبية...')
    print('=' * 50)

    # الحصول على تفاصيل المعاملة
    try:
        transaction = db_manager.fetch_one('''
            SELECT *
            FROM transactions
            WHERE id = 2
        ''')

        if transaction:
            print('📋 تفاصيل المعاملة:')
            for key in transaction.keys():
                print(f'  📄 {key}: {transaction[key]}')
        else:
            print('❌ لم يتم العثور على المعاملة')
    except Exception as e:
        print(f'❌ خطأ في الحصول على تفاصيل المعاملة: {e}')

    # الحصول على تفاصيل المستفيد
    try:
        beneficiary = db_manager.fetch_one('''
            SELECT *
            FROM beneficiaries
            WHERE id = 3
        ''')

        if beneficiary:
            print('\n👤 تفاصيل المستفيد:')
            for key in beneficiary.keys():
                print(f'  📄 {key}: {beneficiary[key]}')
        else:
            print('❌ لم يتم العثور على المستفيد')
    except Exception as e:
        print(f'❌ خطأ في الحصول على تفاصيل المستفيد: {e}')

    # الحصول على أصناف المعاملة
    try:
        items = db_manager.fetch_all('''
            SELECT ti.*, ai.item_name, ai.item_number
            FROM transaction_items ti
            LEFT JOIN added_items ai ON ti.item_id = ai.id
            WHERE ti.transaction_id = 2
        ''')

        if items:
            print(f'\n📦 أصناف المعاملة ({len(items)}):')
            for item in items:
                print(f'  📄 معرف الصنف: {item["item_id"]}')
                print(f'  📦 رقم الصنف: {item["item_number"]}')
                print(f'  📝 اسم الصنف: {item["item_name"]}')
                print(f'  📊 الكمية: {item["quantity"]}')
                print('-' * 20)
        else:
            print('❌ لم يتم العثور على أصناف للمعاملة')
    except Exception as e:
        print(f'❌ خطأ في الحصول على أصناف المعاملة: {e}')

    # البحث عن حركات المخزون المرتبطة
    try:
        movements = db_manager.fetch_all('''
            SELECT im.*, ai.item_name
            FROM inventory_movements_new im
            LEFT JOIN added_items ai ON im.item_number = ai.item_number
            WHERE im.movement_type = 'صرف'
            ORDER BY im.id DESC
            LIMIT 10
        ''')

        if movements:
            print(f'\n📦 حركات الصرف الأخيرة ({len(movements)}):')
            for mov in movements:
                print(f'  🆔 معرف الحركة: {mov["id"]}')
                print(f'  📦 رقم الصنف: {mov["item_number"]} - {mov["item_name"]}')
                print(f'  📊 الكمية: {mov["quantity"]}')
                print(f'  🏢 الجهة: {mov["organization_name"]}')
                print(f'  📅 التاريخ: {mov["movement_date"]}')
                print(f'  📝 الملاحظات: {mov["notes"]}')
                print('-' * 20)
        else:
            print('❌ لم يتم العثور على حركات صرف')
    except Exception as e:
        print(f'❌ خطأ في البحث عن حركات المخزون: {e}')

    print('\n🗑️ هل تريد حذف هذه المعاملة؟')
    print('المعاملة رقم: TR-000001')
    print('المستفيد ID: 3')

except Exception as e:
    print(f'❌ خطأ عام: {e}')
    import traceback
    traceback.print_exc()
