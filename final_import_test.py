#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار نهائي لإصلاحات استيراد Excel
Final Test for Excel Import Fixes
"""

import os
import sys
import tempfile
import pandas as pd
import time

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import db_manager

def test_import_display_fix():
    """اختبار إصلاح عرض البيانات بعد الاستيراد"""
    print("🧪 اختبار إصلاح عرض البيانات بعد الاستيراد")
    print("=" * 60)
    
    try:
        # إنشاء بيانات تجريبية للجدول التنظيمي
        org_data = [
            {
                'اسم الصنف': 'جهاز تجريبي 1',
                'رقم الصنف': 'TEST001',
                'اسم المعدة': 'معدة تجريبية 1',
                'الكمية': 10,
                'ملاحظات': 'للاختبار'
            },
            {
                'اسم الصنف': 'جهاز تجريبي 2',
                'رقم الصنف': 'TEST002',
                'اسم المعدة': 'معدة تجريبية 2',
                'الكمية': 5,
                'ملاحظات': 'للاختبار'
            }
        ]
        
        # إنشاء ملف Excel للجدول التنظيمي
        temp_org_file = tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False)
        temp_org_path = temp_org_file.name
        temp_org_file.close()
        
        df_org = pd.DataFrame(org_data)
        df_org.to_excel(temp_org_path, index=False)
        
        print(f"📁 تم إنشاء ملف الجدول التنظيمي: {temp_org_path}")
        
        # تنظيف البيانات التجريبية السابقة
        db_manager.execute_query("DELETE FROM organizational_chart WHERE item_code LIKE 'TEST%'")
        
        # فحص الحالة قبل الاستيراد
        before_org_count = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart WHERE is_active = 1")[0]
        print(f"📊 عناصر الجدول التنظيمي قبل الاستيراد: {before_org_count}")
        
        # تنفيذ استيراد الجدول التنظيمي
        print("\n📥 بدء استيراد الجدول التنظيمي...")
        from utils.organizational_chart_import import import_organizational_chart_from_excel
        
        def progress_callback(progress, message):
            print(f"   📊 {progress:.1f}% - {message}")
        
        def cancel_check():
            return False
        
        org_result = import_organizational_chart_from_excel(
            temp_org_path, 
            progress_callback=progress_callback,
            cancel_check=cancel_check
        )
        
        print(f"📊 نتائج استيراد الجدول التنظيمي:")
        print(f"   ✅ نجح: {org_result.success_count}")
        print(f"   🔄 مكرر: {org_result.duplicate_count}")
        print(f"   ❌ أخطاء: {org_result.error_count}")
        
        # فحص الحالة بعد الاستيراد
        after_org_count = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart WHERE is_active = 1")[0]
        print(f"📊 عناصر الجدول التنظيمي بعد الاستيراد: {after_org_count}")
        print(f"📈 الزيادة: {after_org_count - before_org_count}")
        
        # التحقق من البيانات المستوردة
        imported_org_items = db_manager.fetch_all("""
            SELECT item_name, item_code, quantity, is_active 
            FROM organizational_chart 
            WHERE item_code IN ('TEST001', 'TEST002')
            ORDER BY item_code
        """)
        
        print(f"\n📋 عناصر الجدول التنظيمي المستوردة ({len(imported_org_items)} عنصر):")
        for item in imported_org_items:
            status = "نشط" if item[3] else "غير نشط"
            print(f"   • {item[0]} ({item[1]}) - كمية: {item[2]} - حالة: {status}")
        
        # تنظيف الملف المؤقت
        os.unlink(temp_org_path)
        
        # اختبار المستفيدين
        print("\n" + "=" * 60)
        print("📥 بدء اختبار استيراد المستفيدين...")
        
        # التأكد من وجود إدارة ووحدة للاختبار
        from models import Department, Unit
        
        test_dept = Department(name="إدارة اختبار نهائي", is_active=True)
        test_dept.save()
        
        test_unit = Unit(name="وحدة اختبار نهائي", is_active=True)
        test_unit.save()
        
        # إنشاء بيانات تجريبية للمستفيدين
        ben_data = [
            {
                'الاسم': 'مستفيد تجريبي 1',
                'الرقم العام': 'TESTBEN001',
                'الرتبة': 'نقيب',
                'الإدارة': 'إدارة اختبار نهائي',
                'الوحدة': 'وحدة اختبار نهائي'
            },
            {
                'الاسم': 'مستفيد تجريبي 2',
                'الرقم العام': 'TESTBEN002',
                'الرتبة': 'ملازم',
                'الإدارة': 'إدارة اختبار نهائي',
                'الوحدة': 'وحدة اختبار نهائي'
            }
        ]
        
        # إنشاء ملف Excel للمستفيدين
        temp_ben_file = tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False)
        temp_ben_path = temp_ben_file.name
        temp_ben_file.close()
        
        df_ben = pd.DataFrame(ben_data)
        df_ben.to_excel(temp_ben_path, index=False)
        
        print(f"📁 تم إنشاء ملف المستفيدين: {temp_ben_path}")
        
        # تنظيف البيانات التجريبية السابقة
        db_manager.execute_query("DELETE FROM beneficiaries WHERE number LIKE 'TESTBEN%'")
        
        # فحص الحالة قبل الاستيراد
        before_ben_count = db_manager.fetch_one("SELECT COUNT(*) FROM beneficiaries WHERE is_active = 1")[0]
        print(f"📊 المستفيدين قبل الاستيراد: {before_ben_count}")
        
        # تنفيذ استيراد المستفيدين
        from utils.excel_import_manager import ExcelImportManager
        
        ben_result = ExcelImportManager.import_beneficiaries_from_excel(
            temp_ben_path, 
            progress_callback=progress_callback,
            cancel_check=cancel_check
        )
        
        print(f"📊 نتائج استيراد المستفيدين:")
        print(f"   ✅ نجح: {ben_result.success_count}")
        print(f"   🔄 مكرر: {ben_result.duplicate_count}")
        print(f"   ❌ أخطاء: {ben_result.error_count}")
        
        # فحص الحالة بعد الاستيراد
        after_ben_count = db_manager.fetch_one("SELECT COUNT(*) FROM beneficiaries WHERE is_active = 1")[0]
        print(f"📊 المستفيدين بعد الاستيراد: {after_ben_count}")
        print(f"📈 الزيادة: {after_ben_count - before_ben_count}")
        
        # التحقق من البيانات المستوردة
        imported_beneficiaries = db_manager.fetch_all("""
            SELECT name, number, rank, is_active 
            FROM beneficiaries 
            WHERE number IN ('TESTBEN001', 'TESTBEN002')
            ORDER BY number
        """)
        
        print(f"\n📋 المستفيدين المستوردين ({len(imported_beneficiaries)} مستفيد):")
        for ben in imported_beneficiaries:
            status = "نشط" if ben[3] else "غير نشط"
            print(f"   • {ben[0]} ({ben[1]}) - رتبة: {ben[2]} - حالة: {status}")
        
        # تنظيف الملف المؤقت
        os.unlink(temp_ben_path)
        
        # تنظيف البيانات التجريبية
        db_manager.execute_query("DELETE FROM beneficiaries WHERE number LIKE 'TESTBEN%'")
        db_manager.execute_query("DELETE FROM organizational_chart WHERE item_code LIKE 'TEST%'")
        db_manager.execute_query("DELETE FROM departments WHERE name = 'إدارة اختبار نهائي'")
        db_manager.execute_query("DELETE FROM units WHERE name = 'وحدة اختبار نهائي'")
        
        print("\n🧹 تم تنظيف البيانات التجريبية")
        
        # النتائج النهائية
        print("\n" + "=" * 60)
        print("📊 ملخص نتائج الاختبار النهائي")
        print("=" * 60)
        
        org_success = org_result.success_count > 0
        ben_success = ben_result.success_count > 0
        
        print(f"🏢 الجدول التنظيمي: {'✅ نجح' if org_success else '❌ فشل'}")
        print(f"👥 المستفيدين: {'✅ نجح' if ben_success else '❌ فشل'}")
        
        if org_success and ben_success:
            print("\n🎉 جميع الاختبارات نجحت!")
            print("✅ إصلاحات استيراد Excel تعمل بشكل صحيح")
            print("✅ البيانات المستوردة تظهر فوراً في الشاشات")
            print("✅ تم تفعيل جميع البيانات المستوردة")
        else:
            print("\n⚠️ بعض الاختبارات فشلت")
        
        return org_success and ben_success
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار النهائي: {e}")
        import traceback
        print(f"تفاصيل الخطأ: {traceback.format_exc()}")
        return False

def main():
    """الدالة الرئيسية للاختبار النهائي"""
    print("🚀 بدء الاختبار النهائي لإصلاحات استيراد Excel")
    print("=" * 60)
    
    success = test_import_display_fix()
    
    print("\n" + "=" * 60)
    if success:
        print("🎯 الاختبار النهائي نجح - الإصلاحات جاهزة للاستخدام!")
    else:
        print("❌ الاختبار النهائي فشل - يحتاج مراجعة إضافية")
    print("=" * 60)

if __name__ == "__main__":
    main()
