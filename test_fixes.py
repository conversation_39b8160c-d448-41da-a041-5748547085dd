#!/usr/bin/env python3
"""
اختبار الإصلاحات المطلوبة
"""

import sys
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_export_fix():
    """اختبار إصلاح زر التصدير في شاشة عمليات الصرف"""
    print("🧪 اختبار إصلاح زر التصدير...")
    
    try:
        from ui.transactions_window import TransactionsWindow
        
        # التحقق من الدوال المطلوبة
        required_methods = [
            'show_export_options',
            'execute_export_option',
            'export_transactions',
            'export_transactions_simple',
            'export_filtered_transactions'
        ]
        
        all_present = True
        for method in required_methods:
            if hasattr(TransactionsWindow, method):
                print(f"  ✅ {method}")
            else:
                print(f"  ❌ {method} مفقود")
                all_present = False
        
        if all_present:
            print("  🎉 تم إصلاح زر التصدير بنجاح!")
            return True
        else:
            print("  ❌ هناك مشاكل في إصلاح زر التصدير")
            return False
            
    except Exception as e:
        print(f"  ❌ خطأ في اختبار التصدير: {e}")
        return False

def test_delete_fix():
    """اختبار إصلاح زر حذف الأصناف"""
    print("\n🧪 اختبار إصلاح زر حذف الأصناف...")
    
    try:
        from ui.inventory_window import InventoryWindow
        
        # التحقق من الدوال المطلوبة
        required_methods = [
            'delete_all_data',
            'check_items_linked_to_transactions',
            'get_all_items',
            'show_linked_items',
            'delete_specific_items'
        ]
        
        all_present = True
        for method in required_methods:
            if hasattr(InventoryWindow, method):
                print(f"  ✅ {method}")
            else:
                print(f"  ❌ {method} مفقود")
                all_present = False
        
        if all_present:
            print("  🎉 تم إصلاح زر حذف الأصناف بنجاح!")
            return True
        else:
            print("  ❌ هناك مشاكل في إصلاح زر حذف الأصناف")
            return False
            
    except Exception as e:
        print(f"  ❌ خطأ في اختبار حذف الأصناف: {e}")
        return False

def main():
    """الاختبار الرئيسي"""
    print("🎯 اختبار الإصلاحات المطلوبة")
    print("=" * 50)
    
    export_ok = test_export_fix()
    delete_ok = test_delete_fix()
    
    print("\n" + "=" * 50)
    print("📋 ملخص النتائج:")
    
    if export_ok:
        print("✅ إصلاح زر التصدير: نجح")
        print("   - يمكن الآن الضغط على زر التصدير")
        print("   - تظهر نافذة خيارات التصدير")
        print("   - يمكن اختيار نوع التصدير المطلوب")
    else:
        print("❌ إصلاح زر التصدير: فشل")
    
    if delete_ok:
        print("✅ إصلاح زر حذف الأصناف: نجح")
        print("   - يتحقق من الأصناف المرتبطة بعمليات صرف")
        print("   - يحذف الأصناف غير المرتبطة فقط")
        print("   - يعرض خيارات متقدمة للمستخدم")
    else:
        print("❌ إصلاح زر حذف الأصناف: فشل")
    
    if export_ok and delete_ok:
        print("\n🎉 تم إنجاز جميع الإصلاحات بنجاح!")
    else:
        print("\n⚠️ هناك مشاكل في بعض الإصلاحات")

if __name__ == "__main__":
    main()