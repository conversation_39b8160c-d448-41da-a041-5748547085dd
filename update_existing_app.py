#!/usr/bin/env python3
"""
تحديث التطبيق الموجود - إضافة التحسينات الجديدة
Update Existing App - Add New Performance Improvements
"""

import shutil
from pathlib import Path
import os

def update_existing_executable():
    """تحديث الملف التنفيذي الموجود"""
    print("🔄 تحديث التطبيق الموجود...")
    print("=" * 50)
    
    # مسارات المجلدات
    source_dir = Path("dist/نظام_إدارة_المخازن")
    target_dir = Path("Desktop_Stores_Management_Portable")
    
    if not source_dir.exists():
        print("❌ مجلد التطبيق المبني غير موجود!")
        return False
    
    if not target_dir.exists():
        print("❌ مجلد التطبيق المحمول غير موجود!")
        return False
    
    # نسخ الملف التنفيذي الجديد
    source_exe = source_dir / "نظام_إدارة_المخازن.exe"
    target_exe = target_dir / "نظام_إدارة_المخازن.exe"
    
    if source_exe.exists():
        # إنشاء نسخة احتياطية من الملف القديم
        if target_exe.exists():
            backup_exe = target_dir / "نظام_إدارة_المخازن_backup.exe"
            shutil.copy2(target_exe, backup_exe)
            print("✅ تم إنشاء نسخة احتياطية من الملف القديم")
        
        # نسخ الملف الجديد
        shutil.copy2(source_exe, target_exe)
        print("✅ تم تحديث الملف التنفيذي")
    
    # نسخ مجلد _internal الجديد
    source_internal = source_dir / "_internal"
    target_internal = target_dir / "_internal"
    
    if source_internal.exists():
        if target_internal.exists():
            shutil.rmtree(target_internal)
        
        shutil.copytree(source_internal, target_internal)
        print("✅ تم تحديث ملفات النظام")
    
    # نسخ الملفات الجديدة
    new_files = [
        "async_operations.py",
        "performance_optimizer.py"
    ]
    
    for file_name in new_files:
        source_file = Path(file_name)
        if source_file.exists():
            target_file = target_dir / file_name
            shutil.copy2(source_file, target_file)
            print(f"✅ تم نسخ {file_name}")
    
    # تحديث ملف معلومات الإصدار
    version_info = f"""نظام إدارة المخازن والمستودعات
Desktop Stores Management System

الإصدار: 1.2.1 (محدث)
تاريخ التحديث: {time.strftime('%Y-%m-%d %H:%M:%S')}
نوع البناء: Portable Package (Updated)

التحسينات الجديدة:
- حل مشاكل التعليق في استيراد Excel
- تحسين أداء حفظ المعاملات
- عمليات غير متزامنة لتحسين الاستجابة
- تحسينات قاعدة البيانات
- معالجة أفضل للأخطاء

المحتويات:
- نظام_إدارة_المخازن.exe: الملف التنفيذي الرئيسي (محدث)
- تشغيل_البرنامج.bat: ملف تشغيل سريع
- data/: مجلد البيانات
- reports/: مجلد التقارير
- backups/: مجلد النسخ الاحتياطية
- logs/: مجلد السجلات
- _internal/: ملفات النظام (محدث)

بيانات الدخول الافتراضية:
اسم المستخدم: admin
كلمة المرور: admin

ملاحظات:
- تم حل مشاكل التعليق في استيراد البيانات
- تحسين سرعة حفظ المعاملات
- واجهة أكثر استجابة
- يمكن نقل هذا المجلد بالكامل إلى أي جهاز آخر
- لا يحتاج إلى تثبيت Python أو أي مكتبات إضافية

للدعم الفني:
- راجع ملف اقرأني.md
- تحقق من ملفات السجل في مجلد logs/
"""
    
    version_file = target_dir / "معلومات_الإصدار.txt"
    version_file.write_text(version_info, encoding='utf-8')
    print("✅ تم تحديث ملف معلومات الإصدار")
    
    print("\n" + "=" * 50)
    print("🎉 تم تحديث التطبيق بنجاح!")
    print("📁 المجلد: Desktop_Stores_Management_Portable")
    print("🚀 التطبيق جاهز للاستخدام مع التحسينات الجديدة")
    print("=" * 50)
    
    return True

if __name__ == "__main__":
    import time
    update_existing_executable()