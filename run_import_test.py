#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
تشغيل شاشة اختبار استيراد المستفيدين
Run Beneficiaries Import Test Window
"""

import os
import sys

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    """تشغيل شاشة اختبار الاستيراد"""
    print("🚀 بدء تشغيل شاشة اختبار استيراد المستفيدين...")
    
    try:
        from test_beneficiaries_import_window import main as test_main
        test_main()
    except Exception as e:
        print(f"❌ خطأ في تشغيل شاشة الاختبار: {e}")
        import traceback
        print(f"📋 تفاصيل الخطأ: {traceback.format_exc()}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
