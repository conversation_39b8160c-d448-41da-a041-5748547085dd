#!/usr/bin/env python3
"""
أدوات النوافذ
Window Utilities

مجموعة من الدوال المساعدة لإدارة النوافذ وتوسيطها
"""

import tkinter as tk

def center_window(window, width=None, height=None, parent=None):
    """
    توسيط النافذة في وسط الشاشة أو وسط النافذة الأصلية
    
    Args:
        window: النافذة المراد توسيطها
        width: عرض النافذة (اختياري)
        height: ارتفاع النافذة (اختياري)
        parent: النافذة الأصلية للتوسيط بالنسبة إليها (اختياري)
    """
    try:
        # تحديث النافذة للحصول على الأبعاد الصحيحة
        window.update_idletasks()
        
        # الحصول على أبعاد النافذة
        if width is None:
            width = window.winfo_width()
        if height is None:
            height = window.winfo_height()
        
        # إذا لم نحصل على أبعاد صحيحة، استخدم أبعاد افتراضية
        if width <= 1:
            width = window.winfo_reqwidth()
        if height <= 1:
            height = window.winfo_reqheight()
        
        if parent:
            # التوسيط بالنسبة للنافذة الأصلية
            parent_x = parent.winfo_x()
            parent_y = parent.winfo_y()
            parent_width = parent.winfo_width()
            parent_height = parent.winfo_height()
            
            x = parent_x + (parent_width - width) // 2
            y = parent_y + (parent_height - height) // 2
        else:
            # التوسيط بالنسبة للشاشة
            screen_width = window.winfo_screenwidth()
            screen_height = window.winfo_screenheight()
            
            x = (screen_width - width) // 2
            y = (screen_height - height) // 2
        
        # التأكد من أن النافذة داخل حدود الشاشة
        if x < 0:
            x = 0
        if y < 0:
            y = 0
        
        screen_width = window.winfo_screenwidth()
        screen_height = window.winfo_screenheight()
        
        if x + width > screen_width:
            x = screen_width - width
        if y + height > screen_height:
            y = screen_height - height
        
        # تطبيق الموضع
        window.geometry(f"{width}x{height}+{x}+{y}")
        
    except Exception as e:
        print(f"خطأ في توسيط النافذة: {e}")
        # في حالة الخطأ، استخدم توسيط بسيط
        try:
            screen_width = window.winfo_screenwidth()
            screen_height = window.winfo_screenheight()
            x = screen_width // 4
            y = screen_height // 4
            window.geometry(f"+{x}+{y}")
        except:
            pass

def bring_to_front(window):
    """
    جعل النافذة في المقدمة
    
    Args:
        window: النافذة المراد جعلها في المقدمة
    """
    try:
        window.lift()
        window.attributes('-topmost', True)
        window.after(100, lambda: window.attributes('-topmost', False))
        window.focus_force()
    except Exception as e:
        print(f"خطأ في جعل النافذة في المقدمة: {e}")

def setup_modal_window(window, parent, title, width, height):
    """
    إعداد نافذة منبثقة (modal) مع التوسيط والإعدادات الأساسية
    
    Args:
        window: النافذة المراد إعدادها
        parent: النافذة الأصلية
        title: عنوان النافذة
        width: عرض النافذة
        height: ارتفاع النافذة
    """
    try:
        # إعداد النافذة الأساسي
        window.title(title)
        window.geometry(f"{width}x{height}")
        window.resizable(False, False)
        window.transient(parent)
        window.grab_set()
        
        # توسيط النافذة
        center_window(window, width, height)
        
        # جعل النافذة في المقدمة
        bring_to_front(window)
        
    except Exception as e:
        print(f"خطأ في إعداد النافذة المنبثقة: {e}")

def delayed_center(window, delay=50):
    """
    توسيط النافذة بعد تأخير (مفيد بعد تحميل المحتوى)
    
    Args:
        window: النافذة المراد توسيطها
        delay: التأخير بالمللي ثانية
    """
    def do_center():
        try:
            center_window(window)
        except:
            pass
    
    window.after(delay, do_center)

def get_screen_center(window):
    """
    الحصول على إحداثيات وسط الشاشة
    
    Args:
        window: النافذة للحصول على معلومات الشاشة
        
    Returns:
        tuple: (x, y) إحداثيات وسط الشاشة
    """
    try:
        screen_width = window.winfo_screenwidth()
        screen_height = window.winfo_screenheight()
        return screen_width // 2, screen_height // 2
    except:
        return 500, 300  # قيم افتراضية

def is_window_on_screen(window):
    """
    التحقق من أن النافذة داخل حدود الشاشة
    
    Args:
        window: النافذة المراد فحصها
        
    Returns:
        bool: True إذا كانت النافذة داخل الشاشة
    """
    try:
        x = window.winfo_x()
        y = window.winfo_y()
        width = window.winfo_width()
        height = window.winfo_height()
        
        screen_width = window.winfo_screenwidth()
        screen_height = window.winfo_screenheight()
        
        # التحقق من أن النافذة داخل حدود الشاشة
        if x < 0 or y < 0:
            return False
        if x + width > screen_width or y + height > screen_height:
            return False
        
        return True
    except:
        return False

def ensure_window_visible(window):
    """
    التأكد من أن النافذة مرئية داخل حدود الشاشة
    
    Args:
        window: النافذة المراد التأكد من رؤيتها
    """
    try:
        if not is_window_on_screen(window):
            center_window(window)
    except:
        pass

# دالة مساعدة للاستخدام السهل
def quick_center(window, width=None, height=None):
    """
    توسيط سريع للنافذة مع جعلها في المقدمة
    
    Args:
        window: النافذة المراد توسيطها
        width: عرض النافذة (اختياري)
        height: ارتفاع النافذة (اختياري)
    """
    center_window(window, width, height)
    bring_to_front(window)
    delayed_center(window, 100)  # توسيط إضافي بعد تأخير قصير
