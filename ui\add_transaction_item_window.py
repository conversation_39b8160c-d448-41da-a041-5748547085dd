"""
نافذة إضافة صنف جديد لعملية الصرف - تطبيق إدارة المخازن
Add Transaction Item Window - Desktop Stores Management System
"""

import tkinter as tk
from tkinter import ttk, messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
import threading

from config import APP_CONFIG, UI_CONFIG, get_message
from models import AddedItem, TransactionItem
from database import db_manager


class AddTransactionItemWindow:
    """نافذة إضافة صنف جديد لعملية الصرف"""
    
    def __init__(self, parent, transaction_id, main_window=None, refresh_callback=None):
        self.parent = parent
        self.transaction_id = transaction_id
        self.main_window = main_window
        self.refresh_callback = refresh_callback
        self.add_window = None
        
        # متغيرات النموذج
        self.item_number_var = tk.StringVar()
        self.item_name_var = tk.StringVar()
        self.quantity_var = tk.StringVar()
        self.notes_var = tk.StringVar()
        
        # قوائم البيانات
        self.items = []
        self.filtered_items = []
        
        # متغيرات البحث التلقائي
        self.search_after_id = None
        self.selected_item = None
        
        # تحميل البيانات
        self.load_data()
        
        # إعداد النافذة
        self.setup_window()
    
    def load_data(self):
        """تحميل البيانات من قاعدة البيانات"""
        try:
            # تحميل الأصناف المتاحة
            self.items = AddedItem.get_all()
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل البيانات: {e}")
    
    def setup_window(self):
        """إعداد النافذة"""
        self.add_window = tk.Toplevel(self.parent)
        self.add_window.title("إضافة صنف جديد للعملية")
        self.add_window.geometry("600x600")  # زيادة الارتفاع قليلاً
        self.add_window.resizable(False, False)
        self.add_window.minsize(600, 600)  # حد أدنى للحجم

        # توسيط النافذة
        self.add_window.transient(self.parent)
        self.add_window.grab_set()

        # حساب موقع التوسيط
        x = self.parent.winfo_x() + (self.parent.winfo_width() // 2) - 300
        y = self.parent.winfo_y() + (self.parent.winfo_height() // 2) - 300
        self.add_window.geometry(f"600x600+{x}+{y}")

        # تسجيل النافذة في مراقبة النشاط (معطل)
        # try:
        #     from activity_monitor import register_window_for_activity_monitoring
        #     register_window_for_activity_monitoring(self.add_window)
        # except Exception as e:
        #     print(f"تحذير: فشل في تسجيل النافذة في مراقبة النشاط: {e}")

        # إعداد المحتوى
        self.setup_content()
    
    def setup_content(self):
        """إعداد محتوى النافذة"""
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.add_window)
        main_frame.pack(fill=BOTH, expand=True, padx=15, pady=15)

        # العنوان
        title_label = ttk_bs.Label(
            main_frame,
            text="➕ إضافة صنف جديد للعملية",
            bootstyle="primary",
            font=("Arial", 14, "bold")
        )
        title_label.pack(pady=(0, 15))

        # نموذج البيانات
        self.create_form(main_frame)

        # أزرار العمليات
        self.create_buttons(main_frame)
    
    def create_form(self, parent):
        """إنشاء نموذج البيانات"""
        form_frame = ttk_bs.LabelFrame(parent, text="بيانات الصنف", bootstyle="info")
        form_frame.pack(fill=X, pady=(0, 15))

        # إطار داخلي
        inner_frame = ttk_bs.Frame(form_frame)
        inner_frame.pack(fill=X, padx=15, pady=15)
        
        # رقم الصنف مع البحث التلقائي
        item_number_frame = ttk_bs.Frame(inner_frame)
        item_number_frame.pack(fill=X, pady=10)
        
        ttk_bs.Label(item_number_frame, text="رقم الصنف", font=("Arial", 10, "bold")).pack(anchor="e", pady=(0, 5))
        
        # إطار للبحث التلقائي
        search_frame = ttk_bs.Frame(item_number_frame)
        search_frame.pack(fill=X)
        
        self.item_number_entry = ttk_bs.Entry(
            search_frame,
            textvariable=self.item_number_var,
            font=("Arial", 10),
            width=50
        )
        self.item_number_entry.pack(fill=X)
        
        # ربط أحداث البحث التلقائي
        self.item_number_var.trace('w', self.on_item_number_change)
        self.item_number_entry.bind('<KeyRelease>', self.on_item_number_keyrelease)
        self.item_number_entry.bind('<FocusOut>', self.on_item_number_focus_out)
        
        # إطار ثابت لقائمة البحث التلقائي
        self.search_container = ttk_bs.Frame(search_frame)
        self.search_container.pack(fill=X, pady=(5, 0))
        
        # قائمة البحث التلقائي
        self.search_listbox = tk.Listbox(
            self.search_container,
            height=0,  # مخفية في البداية
            font=("Arial", 9),
            selectmode=tk.SINGLE
        )
        self.search_listbox.pack(fill=X)
        self.search_listbox.bind('<Double-Button-1>', self.on_search_item_select)
        self.search_listbox.bind('<Button-1>', self.on_search_item_click)
        self.search_listbox.bind('<Return>', self.on_search_item_select)
        self.search_listbox.bind('<KeyRelease>', self.on_search_listbox_key)
        
        # متغير لتتبع حالة القائمة
        self.search_visible = False
        
        # الصنف (للعرض فقط)
        item_name_frame = ttk_bs.Frame(inner_frame)
        item_name_frame.pack(fill=X, pady=10)
        
        ttk_bs.Label(item_name_frame, text="الصنف", font=("Arial", 10, "bold")).pack(anchor="e", pady=(0, 5))
        
        self.item_name_entry = ttk_bs.Entry(
            item_name_frame,
            textvariable=self.item_name_var,
            font=("Arial", 10),
            width=50
        )
        # جعل الحقل للقراءة فقط بعد الإنشاء
        self.item_name_entry.configure(state="readonly")
        self.item_name_entry.pack(fill=X)
        
        # الكمية
        quantity_frame = ttk_bs.Frame(inner_frame)
        quantity_frame.pack(fill=X, pady=10)
        
        ttk_bs.Label(quantity_frame, text="الكمية", font=("Arial", 10, "bold")).pack(anchor="e", pady=(0, 5))
        
        self.quantity_entry = ttk_bs.Entry(
            quantity_frame,
            textvariable=self.quantity_var,
            font=("Arial", 10)
        )
        self.quantity_entry.pack(fill=X)
        
        # ربط التحقق من الأرقام فقط
        self.quantity_entry.bind('<KeyPress>', self.validate_number_input)
        
        # الملاحظات
        notes_frame = ttk_bs.Frame(inner_frame)
        notes_frame.pack(fill=X, pady=10)

        ttk_bs.Label(notes_frame, text="ملاحظات", font=("Arial", 10, "bold")).pack(anchor="e", pady=(0, 5))

        self.notes_text = tk.Text(
            notes_frame,
            height=3,
            font=("Arial", 10),
            wrap=tk.WORD
        )
        self.notes_text.pack(fill=X)
    
    def create_buttons(self, parent):
        """إنشاء أزرار العمليات"""
        # إطار للأزرار بدون LabelFrame لتوفير مساحة
        buttons_frame = ttk_bs.Frame(parent)
        buttons_frame.pack(fill=X, pady=15)

        # خط فاصل
        separator = ttk_bs.Separator(buttons_frame, orient='horizontal')
        separator.pack(fill=X, pady=(0, 10))

        # إطار داخلي للأزرار
        inner_buttons = ttk_bs.Frame(buttons_frame)
        inner_buttons.pack()

        # زر الحفظ
        save_btn = ttk_bs.Button(
            inner_buttons,
            text="💾 حفظ",
            command=self.save_item,
            bootstyle="success",
            width=15
        )
        save_btn.pack(side=LEFT, padx=5)

        # زر الإلغاء
        cancel_btn = ttk_bs.Button(
            inner_buttons,
            text="❌ إلغاء",
            command=self.close_window,
            bootstyle="danger",
            width=15
        )
        cancel_btn.pack(side=LEFT, padx=5)
    
    def validate_number_input(self, event):
        """التحقق من إدخال الأرقام الصحيحة فقط"""
        char = event.char
        if char.isdigit() or char in ['\b', '\x7f']:  # أرقام صحيحة، backspace، delete فقط
            return True
        return "break"
    
    def on_item_number_change(self, *args):
        """معالج تغيير رقم الصنف للبحث التلقائي"""
        if self.search_after_id:
            self.add_window.after_cancel(self.search_after_id)
        
        # تأخير البحث لتجنب البحث المستمر أثناء الكتابة
        self.search_after_id = self.add_window.after(300, self.perform_search)
    
    def on_item_number_keyrelease(self, event):
        """معالج تحرير المفاتيح في حقل رقم الصنف"""
        if event.keysym in ['Up', 'Down']:
            self.navigate_search_results(event.keysym)
        elif event.keysym == 'Return':
            self.select_first_search_result()
        elif event.keysym == 'Escape':
            self.hide_search_results()
    
    def on_item_number_focus_out(self, event):
        """معالج فقدان التركيز من حقل رقم الصنف"""
        # تأخير إخفاء النتائج للسماح بالنقر على القائمة
        self.add_window.after(200, self.hide_search_results)
    
    def perform_search(self):
        """تنفيذ البحث التلقائي"""
        search_term = self.item_number_var.get().strip()
        
        if not search_term:
            self.hide_search_results()
            self.clear_item_details()
            return
        
        # البحث في أرقام الأصناف
        self.filtered_items = []
        for item in self.items:
            if search_term.lower() in item.item_number.lower():
                self.filtered_items.append(item)
        
        print(f"🔍 البحث عن: '{search_term}' - وجد {len(self.filtered_items)} نتائج")
        
        # عرض النتائج
        if self.filtered_items:
            self.show_search_results()
            
            # إذا كان هناك تطابق تام، اختر الصنف تلقائياً
            exact_match = next((item for item in self.filtered_items 
                              if item.item_number.lower() == search_term.lower()), None)
            if exact_match:
                print(f"✅ تطابق تام وجد: {exact_match.item_number} - {exact_match.item_name}")
                self.select_item(exact_match)
                self.hide_search_results()
            else:
                # إذا كان هناك نتيجة واحدة فقط، اختارها تلقائياً
                if len(self.filtered_items) == 1:
                    print(f"✅ نتيجة واحدة فقط: {self.filtered_items[0].item_number} - {self.filtered_items[0].item_name}")
                    self.select_item(self.filtered_items[0])
                    self.hide_search_results()
        else:
            self.hide_search_results()
            self.clear_item_details()
    
    def show_search_results(self):
        """عرض نتائج البحث"""
        self.search_listbox.delete(0, tk.END)
        
        for item in self.filtered_items[:5]:  # عرض أول 5 نتائج فقط
            display_text = f"{item.item_number} - {item.item_name}"
            self.search_listbox.insert(tk.END, display_text)
        
        # تعيين ارتفاع القائمة بناءً على عدد النتائج
        results_count = min(len(self.filtered_items), 5)
        self.search_listbox.configure(height=results_count)
        self.search_visible = True
        
        # تحديد أول عنصر
        if self.search_listbox.size() > 0:
            self.search_listbox.selection_set(0)
    
    def hide_search_results(self):
        """إخفاء نتائج البحث"""
        self.search_listbox.configure(height=0)
        self.search_listbox.delete(0, tk.END)
        self.search_visible = False
    
    def navigate_search_results(self, direction):
        """التنقل في نتائج البحث بالأسهم"""
        if not self.search_visible or self.search_listbox.size() == 0:
            return
        
        current_selection = self.search_listbox.curselection()
        if not current_selection:
            self.search_listbox.selection_set(0)
            return
        
        current_index = current_selection[0]
        
        if direction == 'Down' and current_index < self.search_listbox.size() - 1:
            self.search_listbox.selection_clear(current_index)
            self.search_listbox.selection_set(current_index + 1)
        elif direction == 'Up' and current_index > 0:
            self.search_listbox.selection_clear(current_index)
            self.search_listbox.selection_set(current_index - 1)
    
    def select_first_search_result(self):
        """اختيار أول نتيجة بحث"""
        if self.search_listbox.winfo_viewable() and self.search_listbox.size() > 0:
            self.search_listbox.selection_set(0)
            self.on_search_item_select()
    
    def on_search_item_select(self, event=None):
        """معالج اختيار صنف من نتائج البحث"""
        selection = self.search_listbox.curselection()
        if selection and self.filtered_items:
            selected_index = selection[0]
            if selected_index < len(self.filtered_items):
                selected_item = self.filtered_items[selected_index]
                self.select_item(selected_item)
                self.hide_search_results()
    
    def on_search_item_click(self, event=None):
        """معالج النقر على صنف في نتائج البحث"""
        # تأخير قصير للسماح بتحديد العنصر
        self.add_window.after(100, self.on_search_item_select)
    
    def on_search_listbox_key(self, event):
        """معالج المفاتيح في قائمة البحث"""
        if event.keysym == 'Return':
            self.on_search_item_select()
    
    def select_item(self, item):
        """اختيار صنف وعرض تفاصيله"""
        print(f"🎯 اختيار الصنف: {item.item_number} - {item.item_name}")
        
        self.selected_item = item
        
        # تحديث حقل رقم الصنف
        self.item_number_var.set(item.item_number)
        
        # تحديث حقل اسم الصنف (تمكين التحرير مؤقتاً)
        self.item_name_entry.configure(state="normal")
        self.item_name_var.set(item.item_name)
        self.item_name_entry.configure(state="readonly")
        
        # إجبار تحديث الواجهة
        self.add_window.update_idletasks()
        
        print(f"✅ تم تعيين الصنف المختار: {self.selected_item.item_name if self.selected_item else 'لا يوجد'}")
        print(f"📝 حقل رقم الصنف: '{self.item_number_var.get()}'")
        print(f"📝 حقل اسم الصنف: '{self.item_name_var.get()}'")
        
        # التركيز على حقل الكمية
        self.add_window.after(100, lambda: self.quantity_entry.focus_set())
    
    def clear_item_details(self):
        """مسح تفاصيل الصنف"""
        self.selected_item = None
        
        # مسح حقل اسم الصنف (تمكين التحرير مؤقتاً)
        self.item_name_entry.configure(state="normal")
        self.item_name_var.set("")
        self.item_name_entry.configure(state="readonly")
    
    def save_item(self):
        """حفظ الصنف الجديد"""
        try:
            # التحقق من صحة البيانات
            print(f"🔍 فحص الصنف المختار: {self.selected_item.item_name if self.selected_item else 'لا يوجد'}")
            
            if not self.selected_item:
                # محاولة البحث عن الصنف بناءً على رقم الصنف المدخل
                item_number = self.item_number_var.get().strip()
                if item_number:
                    found_item = next((item for item in self.items if item.item_number == item_number), None)
                    if found_item:
                        self.selected_item = found_item
                        self.item_name_var.set(found_item.item_name)
                        print(f"✅ تم العثور على الصنف: {found_item.item_name}")
                    else:
                        messagebox.showwarning("تحذير", f"لم يتم العثور على صنف برقم: {item_number}")
                        self.item_number_entry.focus_set()
                        return
                else:
                    messagebox.showwarning("تحذير", "يرجى إدخال رقم الصنف أو اختيار الصنف")
                    self.item_number_entry.focus_set()
                    return
            
            if not self.quantity_var.get():
                messagebox.showwarning("تحذير", "يرجى إدخال الكمية")
                self.quantity_entry.focus_set()
                return
            
            try:
                quantity = int(self.quantity_var.get())
                if quantity <= 0:
                    messagebox.showwarning("تحذير", "يجب أن تكون الكمية أكبر من صفر")
                    self.quantity_entry.focus_set()
                    return
            except ValueError:
                messagebox.showwarning("تحذير", "يرجى إدخال كمية صحيحة")
                self.quantity_entry.focus_set()
                return
            
            # التحقق من عدم وجود الصنف مسبقاً في العملية
            check_query = """
                SELECT COUNT(*) FROM transaction_items 
                WHERE transaction_id = ? AND item_id = ?
            """
            result = db_manager.fetch_one(check_query, (self.transaction_id, self.selected_item.id))
            if result[0] > 0:
                messagebox.showwarning("تحذير", "هذا الصنف موجود بالفعل في العملية")
                self.item_number_entry.focus_set()
                return
            
            # الحصول على الملاحظات
            notes = self.notes_text.get(1.0, tk.END).strip()
            
            # إضافة الصنف للعملية
            insert_query = """
                INSERT INTO transaction_items (transaction_id, item_id, quantity, notes, created_at)
                VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
            """
            db_manager.execute_query(insert_query, (self.transaction_id, self.selected_item.id, quantity, notes))
            
            # عرض رسالة نجاح محسنة
            self.show_success_message(f"تم إضافة الصنف '{self.selected_item.item_name}' بنجاح!")
            
            # تحديث الجدول في النافذة الأصلية
            if self.refresh_callback:
                self.refresh_callback()
            
            # تحديث الشاشة الرئيسية
            if hasattr(self.main_window, 'refresh_all_data'):
                self.main_window.refresh_all_data()
            
            # إغلاق النافذة بعد تأخير قصير
            self.add_window.after(1500, self.close_window)
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ الصنف: {e}")
    
    def show_success_message(self, message):
        """عرض رسالة نجاح محسنة تختفي تلقائياً"""
        # إنشاء إطار للرسالة
        success_frame = ttk_bs.Frame(self.add_window, bootstyle="success")
        success_frame.place(relx=0.5, rely=0.1, anchor="center")
        
        # النص
        success_label = ttk_bs.Label(
            success_frame,
            text=f"✅ {message}",
            bootstyle="success-inverse",
            font=("Arial", 12, "bold"),
            padding=15
        )
        success_label.pack()
        
        # إخفاء الرسالة بعد 3 ثوانٍ
        def hide_message():
            try:
                success_frame.destroy()
            except:
                pass
        
        self.add_window.after(3000, hide_message)
        
        # إخفاء الرسالة عند النقر في أي مكان
        def on_click(event):
            try:
                success_frame.destroy()
            except:
                pass
        
        self.add_window.bind("<Button-1>", on_click)
        success_frame.bind("<Button-1>", on_click)
        success_label.bind("<Button-1>", on_click)
    
    def close_window(self):
        """إغلاق النافذة"""
        self.add_window.destroy()
