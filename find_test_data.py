#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import sys
import os

# إضافة مسار المشروع
sys.path.append('.')

try:
    from database import db_manager
    
    print('🔍 البحث عن البيانات التجريبية...')
    print('=' * 50)

    # البحث في حركات المخزون عن العمليات التجريبية
    try:
        print('🔍 البحث في حركات المخزون:')
        print('=' * 50)
        movements = db_manager.fetch_all('''
            SELECT im.id, im.item_number, im.movement_type, im.quantity, 
                   im.organization_name, im.notes, im.movement_date, im.user_id,
                   ai.item_name,
                   u.username, u.full_name
            FROM inventory_movements_new im
            LEFT JOIN added_items ai ON im.item_number = ai.item_number
            LEFT JOIN users u ON im.user_id = u.id
            WHERE im.movement_type = 'صرف'
            ORDER BY im.movement_date DESC
            LIMIT 20
        ''')

        if movements:
            print(f'📦 تم العثور على {len(movements)} حركة صرف:')
            for mov in movements:
                print(f'  🆔 معرف الحركة: {mov["id"]}')
                print(f'  📅 التاريخ: {mov["movement_date"]}')
                print(f'  📦 الصنف: {mov["item_number"]} - {mov["item_name"]}')
                print(f'  📊 الكمية: {mov["quantity"]}')
                print(f'  🏢 الجهة: {mov["organization_name"]}')
                print(f'  📝 الملاحظات: {mov["notes"]}')
                print(f'  👤 المستخدم: {mov["username"]} ({mov["full_name"]})')
                print('-' * 30)
        else:
            print('❌ لم يتم العثور على حركات صرف')
    except Exception as e:
        print(f'❌ خطأ في البحث في inventory_movements_new: {e}')

    # البحث في المعاملات
    try:
        print('\n🔍 البحث في المعاملات:')
        print('=' * 50)
        transactions = db_manager.fetch_all('''
            SELECT t.id, t.transaction_number, t.transaction_date, t.notes,
                   b.name as beneficiary_name, b.general_number,
                   u.username, u.full_name
            FROM transactions t
            LEFT JOIN beneficiaries b ON t.beneficiary_id = b.id
            LEFT JOIN users u ON t.user_id = u.id
            ORDER BY t.created_at DESC
            LIMIT 10
        ''')

        if transactions:
            print(f'📋 تم العثور على {len(transactions)} معاملة:')
            for trans in transactions:
                print(f'  🆔 معرف المعاملة: {trans["id"]}')
                print(f'  📄 رقم المعاملة: {trans["transaction_number"]}')
                print(f'  📅 التاريخ: {trans["transaction_date"]}')
                print(f'  👤 المستفيد: {trans["beneficiary_name"]} ({trans["general_number"]})')
                print(f'  📝 الملاحظات: {trans["notes"]}')
                print(f'  👤 المستخدم: {trans["username"]} ({trans["full_name"]})')
                
                # البحث عن أصناف هذه المعاملة
                items = db_manager.fetch_all('''
                    SELECT ti.quantity, ai.item_number, ai.item_name
                    FROM transaction_items ti
                    LEFT JOIN added_items ai ON ti.item_id = ai.id
                    WHERE ti.transaction_id = ?
                ''', [trans["id"]])
                
                if items:
                    print(f'    📦 الأصناف ({len(items)}):')
                    for item in items:
                        print(f'      - {item["item_number"]}: {item["item_name"]} (كمية: {item["quantity"]})')
                
                print('-' * 30)
        else:
            print('❌ لم يتم العثور على معاملات')
    except Exception as e:
        print(f'❌ خطأ في البحث في transactions: {e}')

    # البحث عن المستفيدين التجريبيين
    try:
        print('\n🔍 البحث عن المستفيدين التجريبيين:')
        print('=' * 50)
        beneficiaries = db_manager.fetch_all('''
            SELECT id, name, general_number, unit, department
            FROM beneficiaries
            WHERE name LIKE '%خالد%' OR name LIKE '%تجرب%' OR name LIKE '%test%'
            ORDER BY created_at DESC
        ''')

        if beneficiaries:
            print(f'👥 تم العثور على {len(beneficiaries)} مستفيد تجريبي:')
            for ben in beneficiaries:
                print(f'  🆔 معرف المستفيد: {ben["id"]}')
                print(f'  👤 الاسم: {ben["name"]}')
                print(f'  📄 الرقم العام: {ben["general_number"]}')
                print(f'  🏢 الوحدة: {ben["unit"]}')
                print(f'  🏛️ القسم: {ben["department"]}')
                print('-' * 30)
        else:
            print('❌ لم يتم العثور على مستفيدين تجريبيين')
    except Exception as e:
        print(f'❌ خطأ في البحث في beneficiaries: {e}')

except Exception as e:
    print(f'❌ خطأ عام: {e}')
    import traceback
    traceback.print_exc()
