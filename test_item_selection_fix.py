#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# إضافة مسار المشروع
sys.path.append('.')

try:
    from database import db_manager
    
    print('🔍 اختبار إصلاح مشكلة اختيار الصنف في شاشة إضافة حركة المخزون...')
    print('=' * 70)

    # فحص الأصناف التي قد تسبب مشكلة في الاختيار
    try:
        print('📊 فحص الأصناف التي قد تسبب تداخل في الاختيار:')
        print('-' * 50)
        
        # البحث عن أصناف أرقامها قد تتداخل
        items = db_manager.fetch_all('''
            SELECT item_number, item_name
            FROM added_items
            WHERE is_active = 1
            ORDER BY CAST(item_number AS INTEGER)
            LIMIT 20
        ''')
        
        print('📦 أول 20 صنف (مرتبة حسب الرقم):')
        for i, item in enumerate(items, 1):
            print(f'{i:2d}. 📦 {item["item_number"]} - {item["item_name"]}')
        
        # فحص الأصناف التي قد تسبب مشكلة
        print('\n🔍 فحص التداخل المحتمل:')
        potential_conflicts = []
        
        for item in items:
            item_num = str(item["item_number"])
            # البحث عن أصناف أخرى تبدأ بنفس الرقم
            conflicting_items = [
                other for other in items 
                if str(other["item_number"]).startswith(item_num) and str(other["item_number"]) != item_num
            ]
            
            if conflicting_items:
                potential_conflicts.append({
                    'main_item': item,
                    'conflicts': conflicting_items
                })
        
        if potential_conflicts:
            print('⚠️ تم العثور على تداخلات محتملة:')
            for conflict in potential_conflicts[:5]:  # أول 5 تداخلات
                main = conflict['main_item']
                print(f'  📦 الصنف الأساسي: {main["item_number"]} - {main["item_name"]}')
                for conf in conflict['conflicts']:
                    print(f'    ↳ يتداخل مع: {conf["item_number"]} - {conf["item_name"]}')
                print()
        else:
            print('✅ لا توجد تداخلات محتملة في أرقام الأصناف')
            
    except Exception as e:
        print(f'❌ خطأ في فحص التداخلات: {e}')

    # اختبار منطق الاختيار الجديد
    try:
        print('\n🧪 اختبار منطق الاختيار الجديد:')
        print('-' * 40)
        
        # محاكاة قائمة الأصناف
        sample_items = [
            "1 - قلم حبر انبوبي الشكل اسود",
            "10 - اقلام تحديد فسفورية (هايلات )",
            "11 - قلم تحديد اسود",
            "12 - قلم تحديد ازرق",
            "2 - قلم حبر انبوبي الشكل ازرق",
            "20 - ورق A4 ابيض",
            "21 - ورق A4 ملون"
        ]
        
        def test_selection_logic(target_item_number, item_list):
            """اختبار منطق الاختيار الجديد"""
            print(f'🎯 البحث عن الصنف رقم: {target_item_number}')
            
            # المنطق القديم (خاطئ)
            old_result = None
            for item_text in item_list:
                if item_text.startswith(str(target_item_number)):
                    old_result = item_text
                    break
            
            # المنطق الجديد (صحيح)
            new_result = None
            for item_text in item_list:
                if " - " in item_text:
                    item_number_in_list = item_text.split(" - ")[0]
                    if item_number_in_list == str(target_item_number):
                        new_result = item_text
                        break
            
            print(f'  📊 المنطق القديم: {old_result}')
            print(f'  ✅ المنطق الجديد: {new_result}')
            
            if old_result != new_result:
                print(f'  🔧 تم إصلاح المشكلة! ✅')
            else:
                print(f'  ✅ لا توجد مشكلة في هذا الصنف')
            print()
        
        # اختبار عدة حالات
        test_cases = ["1", "2", "10", "11", "20"]
        
        for test_case in test_cases:
            test_selection_logic(test_case, sample_items)
            
    except Exception as e:
        print(f'❌ خطأ في اختبار منطق الاختيار: {e}')

    # فحص الأصناف الفعلية في النظام
    try:
        print('\n📋 اختبار على البيانات الفعلية:')
        print('-' * 35)
        
        # اختبار على أول 5 أصناف
        test_items = db_manager.fetch_all('''
            SELECT item_number, item_name
            FROM added_items
            WHERE is_active = 1
            ORDER BY CAST(item_number AS INTEGER)
            LIMIT 5
        ''')
        
        # إنشاء قائمة كما تظهر في الشاشة
        item_list = [f"{item['item_number']} - {item['item_name']}" for item in test_items]
        
        print('📦 قائمة الأصناف للاختبار:')
        for i, item_text in enumerate(item_list, 1):
            print(f'  {i}. {item_text}')
        
        print('\n🧪 اختبار الاختيار:')
        for item in test_items:
            target_number = str(item['item_number'])
            
            # المنطق الجديد
            found_item = None
            for item_text in item_list:
                if " - " in item_text:
                    item_number_in_list = item_text.split(" - ")[0]
                    if item_number_in_list == target_number:
                        found_item = item_text
                        break
            
            if found_item:
                print(f'  ✅ الصنف {target_number}: تم العثور عليه بشكل صحيح')
                print(f'      {found_item}')
            else:
                print(f'  ❌ الصنف {target_number}: لم يتم العثور عليه!')
            
    except Exception as e:
        print(f'❌ خطأ في اختبار البيانات الفعلية: {e}')

    print('\n🎉 انتهى اختبار إصلاح مشكلة اختيار الصنف!')
    print('✅ تم إصلاح المشكلة: الآن يتم اختيار الصنف الصحيح بدقة')

except Exception as e:
    print(f'❌ خطأ عام: {e}')
    import traceback
    traceback.print_exc()
