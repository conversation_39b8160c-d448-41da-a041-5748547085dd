#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار استيراد Excel مع كميات مختلفة
"""

import pandas as pd
import os

def create_test_excel():
    """إنشاء ملف Excel تجريبي للاختبار"""
    
    # بيانات تجريبية مع كميات مختلفة
    test_data = [
        {
            'رقم الصنف': 'T001',
            'اسم الصنف': 'صنف تجريبي 1',
            'نوع العهدة': 'مستهلكة',
            'التصنيف': 'أغراض تجريبية',
            'الوحدة': 'عدد',
            'الكمية الحالية': 10
        },
        {
            'رقم الصنف': 'T002',
            'اسم الصنف': 'صنف تجريبي 2',
            'نوع العهدة': 'مستهلكة',
            'التصنيف': 'أغراض تجريبية',
            'الوحدة': 'عدد',
            'الكمية الحالية': 25
        },
        {
            'رقم الصنف': 'T003',
            'اسم الصنف': 'صنف تجريبي 3',
            'نوع العهدة': 'مستهلكة',
            'التصنيف': 'أغراض تجريبية',
            'الوحدة': 'عدد',
            'الكمية الحالية': 0
        },
        {
            'رقم الصنف': 'T004',
            'اسم الصنف': 'صنف تجريبي 4',
            'نوع العهدة': 'مستهلكة',
            'التصنيف': 'أغراض تجريبية',
            'الوحدة': 'عدد',
            'الكمية الحالية': 100
        },
        {
            'رقم الصنف': 'T005',
            'اسم الصنف': 'صنف تجريبي 5',
            'نوع العهدة': 'مستهلكة',
            'التصنيف': 'أغراض تجريبية',
            'الوحدة': 'عدد',
            'الكمية الحالية': 5
        }
    ]
    
    # إنشاء DataFrame
    df = pd.DataFrame(test_data)
    
    # حفظ الملف
    file_path = 'e:/desktop_stores_app/test_items.xlsx'
    df.to_excel(file_path, index=False, engine='openpyxl')
    
    print(f"✅ تم إنشاء ملف الاختبار: {file_path}")
    print("📊 البيانات المحفوظة:")
    print(df.to_string(index=False))
    
    return file_path

if __name__ == "__main__":
    create_test_excel()