#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import sys
import os

# إضافة مسار المشروع
sys.path.append('.')

try:
    from database import db_manager
    
    print('🔍 فحص بنية جدول transactions...')
    print('=' * 50)

    # عرض بنية جدول transactions
    try:
        structure = db_manager.fetch_all('''
            PRAGMA table_info(transactions)
        ''')
        
        print('📋 أعمدة جدول transactions:')
        for col in structure:
            print(f'  📄 {col["name"]} ({col["type"]}) - {"NOT NULL" if col["notnull"] else "NULL"} - {"PK" if col["pk"] else ""}')
    except Exception as e:
        print(f'❌ خطأ في فحص بنية transactions: {e}')

    # عرض بنية جدول transaction_items
    try:
        structure = db_manager.fetch_all('''
            PRAGMA table_info(transaction_items)
        ''')
        
        print('\n📋 أعمدة جدول transaction_items:')
        for col in structure:
            print(f'  📄 {col["name"]} ({col["type"]}) - {"NOT NULL" if col["notnull"] else "NULL"} - {"PK" if col["pk"] else ""}')
    except Exception as e:
        print(f'❌ خطأ في فحص بنية transaction_items: {e}')

    # عرض بنية جدول inventory_movements_new
    try:
        structure = db_manager.fetch_all('''
            PRAGMA table_info(inventory_movements_new)
        ''')
        
        print('\n📋 أعمدة جدول inventory_movements_new:')
        for col in structure:
            print(f'  📄 {col["name"]} ({col["type"]}) - {"NOT NULL" if col["notnull"] else "NULL"} - {"PK" if col["pk"] else ""}')
    except Exception as e:
        print(f'❌ خطأ في فحص بنية inventory_movements_new: {e}')

    # عرض آخر المعاملات بالأعمدة الصحيحة
    try:
        print('\n🕐 آخر 5 معاملات:')
        print('=' * 50)
        recent_transactions = db_manager.fetch_all('''
            SELECT *
            FROM transactions
            ORDER BY created_at DESC
            LIMIT 5
        ''')
        
        if recent_transactions:
            for trans in recent_transactions:
                print(f'  🆔 معرف: {trans.get("id", "N/A")}')
                print(f'  📄 رقم المعاملة: {trans.get("transaction_number", "N/A")}')
                print(f'  📅 التاريخ: {trans.get("transaction_date", "N/A")}')
                print(f'  ⏰ تاريخ الإنشاء: {trans.get("created_at", "N/A")}')
                print(f'  📋 جميع البيانات: {dict(trans)}')
                print('-' * 30)
        else:
            print('❌ لا توجد معاملات')
    except Exception as e:
        print(f'❌ خطأ في عرض آخر المعاملات: {e}')

    # البحث في حركات المخزون عن العمليات التجريبية
    try:
        print('\n🔍 البحث في حركات المخزون عن العمليات التجريبية:')
        print('=' * 50)
        movements = db_manager.fetch_all('''
            SELECT *
            FROM inventory_movements_new
            WHERE movement_type = 'صرف'
            ORDER BY movement_date DESC
            LIMIT 10
        ''')

        if movements:
            print(f'📦 تم العثور على {len(movements)} حركة صرف:')
            for mov in movements:
                print(f'  🆔 معرف الحركة: {mov.get("id", "N/A")}')
                print(f'  📅 التاريخ: {mov.get("movement_date", "N/A")}')
                print(f'  📦 رقم الصنف: {mov.get("item_number", "N/A")}')
                print(f'  📊 الكمية: {mov.get("quantity", "N/A")}')
                print(f'  🏢 الجهة: {mov.get("organization_name", "N/A")}')
                print(f'  📝 الملاحظات: {mov.get("notes", "N/A")}')
                print(f'  👤 المستخدم: {mov.get("user_id", "N/A")}')
                print('-' * 30)
        else:
            print('❌ لم يتم العثور على حركات صرف')
    except Exception as e:
        print(f'❌ خطأ في البحث في inventory_movements_new: {e}')

except Exception as e:
    print(f'❌ خطأ عام: {e}')
    import traceback
    traceback.print_exc()
