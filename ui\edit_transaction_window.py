"""
نافذة تعديل عملية الصرف - تطبيق إدارة المخازن
Edit Transaction Window - Desktop Stores Management System
"""

import tkinter as tk
from tkinter import ttk, messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from datetime import datetime, date

from config import APP_CONFIG, UI_CONFIG, get_message
from models import Transaction, Beneficiary, AddedItem, Item
from database import db_manager
from ui.autocomplete_entry import AutocompleteEntry
from ui.success_message import AutoSuccessMessage


class EditTransactionWindow:
    """نافذة تعديل عملية الصرف"""
    
    def __init__(self, parent, transaction_id, main_window=None, refresh_callback=None):
        self.parent = parent
        self.transaction_id = transaction_id
        self.main_window = main_window
        self.refresh_callback = refresh_callback  # إضافة callback للتحديث
        self.edit_window = None
        self.transaction_data = None
        self.beneficiaries = []
        
        # متغيرات النموذج
        self.transaction_number_var = tk.StringVar()
        self.beneficiary_var = tk.StringVar()
        self.receiver_var = tk.StringVar()
        self.notes_var = tk.StringVar()
        
        # تحميل البيانات
        self.load_data()
        
        # إعداد النافذة
        self.setup_window()
    
    def load_data(self):
        """تحميل البيانات من قاعدة البيانات"""
        try:
            # تحميل بيانات العملية
            query = """
                SELECT t.*, b.name as beneficiary_name
                FROM transactions t
                LEFT JOIN beneficiaries b ON t.beneficiary_id = b.id
                WHERE t.id = ?
            """
            result = db_manager.execute_query(query, (self.transaction_id,)).fetchone()
            
            if result:
                self.transaction_data = dict(result)
                # تعيين القيم في المتغيرات
                self.transaction_number_var.set(self.transaction_data.get('transaction_number', ''))
                self.beneficiary_var.set(self.transaction_data.get('beneficiary_name', ''))
                self.notes_var.set(self.transaction_data.get('notes', ''))
            else:
                messagebox.showerror("خطأ", "لم يتم العثور على العملية")
                return
            
            # تحميل المستفيدين
            self.beneficiaries = Beneficiary.get_all(active_only=True)
                
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل البيانات: {e}")
            self.beneficiaries = []
    
    def setup_window(self):
        """إعداد النافذة"""
        self.edit_window = tk.Toplevel(self.parent)
        self.edit_window.title("📝 تعديل عملية الصرف")
        self.edit_window.geometry("700x400")
        self.edit_window.resizable(False, False)

        # توسيط النافذة
        self.center_window()

        # تسجيل النافذة في مراقبة النشاط (معطل)
        # try:
        #     from activity_monitor import register_window_for_activity_monitoring
        #     register_window_for_activity_monitoring(self.edit_window)
        # except Exception as e:
        #     print(f"تحذير: فشل في تسجيل النافذة في مراقبة النشاط: {e}")

        # إعداد المحتوى
        self.setup_content()

        # جعل النافذة في المقدمة
        self.edit_window.lift()
        self.edit_window.focus_force()
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.edit_window.update_idletasks()
        
        screen_width = self.edit_window.winfo_screenwidth()
        screen_height = self.edit_window.winfo_screenheight()
        
        window_width = 700
        window_height = 400
        
        x = max(0, (screen_width - window_width) // 2)
        y = max(0, (screen_height - window_height) // 2)
        
        self.edit_window.geometry(f"{window_width}x{window_height}+{x}+{y}")
        
        # توسيط النافذة في الشاشة
        self.edit_window.transient(self.parent)
        self.edit_window.grab_set()
    
    def setup_content(self):
        """إعداد محتوى النافذة"""
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.edit_window)
        main_frame.pack(fill=BOTH, expand=True, padx=10, pady=10)
        
        # شريط العنوان والأدوات
        self.create_header(main_frame)
        
        # معلومات العملية
        self.create_transaction_info(main_frame)
        
        # أزرار العمليات
        self.create_buttons(main_frame)
    
    def create_header(self, parent):
        """إنشاء شريط العنوان والأدوات"""
        header_frame = ttk_bs.Frame(parent)
        header_frame.pack(fill=X, pady=(0, 10))
        
        # زر العودة للتفاصيل
        back_btn = ttk_bs.Button(
            header_frame,
            text="← العودة للتفاصيل",
            command=self.close_window,
            bootstyle="outline-secondary",
            width=22
        )
        back_btn.pack(side=LEFT)
        
        # العنوان
        title_label = ttk_bs.Label(
            header_frame,
            text="📝 تعديل عملية الصرف",
            bootstyle="primary",
            font=("Arial", 14, "bold")
        )
        title_label.pack(side=LEFT, padx=(20, 0))
    
    def create_transaction_info(self, parent):
        """إنشاء قسم معلومات العملية"""
        info_frame = ttk_bs.LabelFrame(parent, text="ℹ️ معلومات العملية", bootstyle="primary")
        info_frame.pack(fill=BOTH, expand=True, pady=(0, 10))
        
        # إطار داخلي للنموذج
        form_frame = ttk_bs.Frame(info_frame)
        form_frame.pack(fill=BOTH, expand=True, padx=20, pady=20)
        
        # الصف الأول - رقم العملية
        row1 = ttk_bs.Frame(form_frame)
        row1.pack(fill=X, pady=10)
        
        ttk_bs.Label(row1, text="رقم العملية", font=("Arial", 10, "bold")).pack(anchor="e", pady=(0, 5))
        ttk_bs.Entry(
            row1,
            textvariable=self.transaction_number_var,
            state="readonly",
            font=("Arial", 10),
            width=50
        ).pack(fill=X)
        
        # الصف الثاني - المستلم
        row2 = ttk_bs.Frame(form_frame)
        row2.pack(fill=X, pady=10)
        
        ttk_bs.Label(row2, text="المستلم", font=("Arial", 10, "bold")).pack(anchor="e", pady=(0, 5))
        
        # إطار للمستلم مع الإكمال التلقائي
        beneficiary_frame = ttk_bs.Frame(row2)
        beneficiary_frame.pack(fill=X)
        
        self.beneficiary_autocomplete = AutocompleteEntry(
            beneficiary_frame,
            data_source=self.beneficiaries,
            display_func=lambda b: b.name,
            on_select=self.on_beneficiary_selected,
            placeholder="ابحث عن المستفيد...",
            width=50
        )
        self.beneficiary_autocomplete.pack(fill=X)
        
        # تعيين القيمة الحالية
        if self.transaction_data and self.transaction_data.get('beneficiary_name'):
            current_beneficiary = next((b for b in self.beneficiaries 
                                     if b.name == self.transaction_data.get('beneficiary_name')), None)
            if current_beneficiary:
                self.beneficiary_autocomplete.set_selected_item(current_beneficiary)
        
        # الصف الثالث - المندوب المستلم للوحدة
        row3 = ttk_bs.Frame(form_frame)
        row3.pack(fill=X, pady=10)
        
        ttk_bs.Label(row3, text="المندوب المستلم للوحدة", font=("Arial", 10, "bold")).pack(anchor="e", pady=(0, 5))
        
        # إطار للمندوب مع الإكمال التلقائي
        receiver_frame = ttk_bs.Frame(row3)
        receiver_frame.pack(fill=X)
        
        self.receiver_autocomplete = AutocompleteEntry(
            receiver_frame,
            data_source=self.beneficiaries,
            display_func=lambda b: b.name,
            on_select=self.on_receiver_selected,
            placeholder="ابحث عن المندوب المستلم...",
            width=50
        )
        self.receiver_autocomplete.pack(fill=X)
        
        # تعيين القيمة الحالية للمندوب المستلم إذا وجدت
        if self.transaction_data and self.transaction_data.get('receiver_id'):
            receiver_query = "SELECT name FROM beneficiaries WHERE id = ?"
            receiver_result = db_manager.fetch_one(receiver_query, (self.transaction_data.get('receiver_id'),))
            if receiver_result:
                receiver_name = receiver_result[0] if isinstance(receiver_result, tuple) else receiver_result['name']
                current_receiver = next((b for b in self.beneficiaries if b.name == receiver_name), None)
                if current_receiver:
                    self.receiver_autocomplete.set_selected_item(current_receiver)
    
    def create_buttons(self, parent):
        """إنشاء أزرار العمليات"""
        buttons_frame = ttk_bs.Frame(parent)
        buttons_frame.pack(fill=X, pady=10)
        
        # زر الإلغاء
        cancel_btn = ttk_bs.Button(
            buttons_frame,
            text="❌ إلغاء",
            command=self.close_window,
            bootstyle="danger",
            width=15
        )
        cancel_btn.pack(side=RIGHT, padx=5)
        
        # زر الحفظ
        save_btn = ttk_bs.Button(
            buttons_frame,
            text="💾 حفظ",
            command=self.save_changes,
            bootstyle="success",
            width=15
        )
        save_btn.pack(side=RIGHT, padx=5)
    
    def on_beneficiary_selected(self, beneficiary):
        """معالج اختيار المستفيد"""
        if beneficiary:
            # تحديث متغير المستفيد
            self.beneficiary_var.set(beneficiary.name)
    
    def on_receiver_selected(self, receiver):
        """معالج اختيار المندوب المستلم"""
        if receiver:
            # تحديث متغير المندوب المستلم
            self.receiver_var.set(receiver.name)
    
    def save_changes(self):
        """حفظ التغييرات"""
        try:
            # التحقق من صحة البيانات
            beneficiary = self.beneficiary_autocomplete.get_selected_item()
            if not beneficiary:
                messagebox.showwarning("تحذير", "يرجى اختيار المستلم")
                return

            # الحصول على المندوب المستلم
            receiver = self.receiver_autocomplete.get_selected_item()

            # تحديث بيانات العملية (بدون الملاحظات)
            update_query = """
                UPDATE transactions
                SET beneficiary_id = ?, receiver_id = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """

            receiver_id = receiver.id if receiver else None
            db_manager.execute_query(update_query, (beneficiary.id, receiver_id, self.transaction_id))

            # تحديث نافذة التفاصيل إذا كانت موجودة
            if self.refresh_callback:
                self.refresh_callback()

            # تحديث الشاشة الرئيسية
            if hasattr(self.main_window, 'refresh_all_data'):
                self.main_window.refresh_all_data()

            # رسالة نجاح تلقائية بعد تأخير قصير لضمان ظهورها في الوسط
            self.edit_window.after(50, lambda: AutoSuccessMessage.show(
                self.edit_window, 
                "تم حفظ التغييرات بنجاح ✅\n\nتم تحديث بيانات عملية الصرف"
            ))

            # إغلاق النافذة بعد عرض الرسالة
            self.edit_window.after(3200, self.close_window)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ التغييرات: {e}")

    def close_window(self):
        """إغلاق النافذة"""
        self.edit_window.destroy()
