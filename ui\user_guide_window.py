#!/usr/bin/env python3
"""
دليل المستخدم - شرح الصلاحيات وطريقة الاستخدام
User Guide Window - Permissions and Usage Guide
"""

import tkinter as tk
from tkinter import ttk
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *

from permissions_manager import PermissionsManager
from models import User

class UserGuideWindow:
    """نافذة دليل المستخدم"""
    
    def __init__(self, parent, current_user: User):
        self.parent = parent
        self.current_user = current_user
        self.window = None
        
        self.setup_window()
    
    def setup_window(self):
        """إعداد النافذة"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("📖 دليل المستخدم - الصلاحيات وطريقة الاستخدام")
        self.window.geometry("1000x700")
        self.window.resizable(True, True)
        
        # توسيط النافذة
        self.center_window()
        
        # إعداد المحتوى
        self.setup_content()
        
        # جعل النافذة في المقدمة
        self.window.lift()
        self.window.focus_force()
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() - 1000) // 2
        y = (self.window.winfo_screenheight() - 700) // 2
        self.window.geometry(f"1000x700+{x}+{y}")
    
    def setup_content(self):
        """إعداد محتوى النافذة"""
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.window)
        main_frame.pack(fill=BOTH, expand=True, padx=20, pady=20)
        
        # العنوان
        title_label = ttk_bs.Label(
            main_frame,
            text="📖 دليل المستخدم - نظام إدارة المخازن",
            font=("Arial", 16, "bold"),
            bootstyle="primary"
        )
        title_label.pack(pady=(0, 20))
        
        # إنشاء Notebook للتبويبات
        notebook = ttk_bs.Notebook(main_frame)
        notebook.pack(fill=BOTH, expand=True)
        
        # تبويب الصلاحيات الحالية
        self.create_current_permissions_tab(notebook)
        
        # تبويب أنواع المستخدمين
        self.create_user_types_tab(notebook)
        
        # تبويب دليل الاستخدام
        self.create_usage_guide_tab(notebook)
        
        # تبويب كيفية التعرف على الصلاحيات
        self.create_permissions_guide_tab(notebook)
        
        # تبويب الأسئلة الشائعة
        self.create_faq_tab(notebook)
    
    def create_current_permissions_tab(self, notebook):
        """إنشاء تبويب الصلاحيات الحالية"""
        frame = ttk_bs.Frame(notebook)
        notebook.add(frame, text="🔐 صلاحياتي الحالية")
        
        # إطار التمرير
        canvas = tk.Canvas(frame)
        scrollbar = ttk_bs.Scrollbar(frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk_bs.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # معلومات المستخدم الحالي
        user_info_frame = ttk_bs.LabelFrame(scrollable_frame, text="معلومات المستخدم الحالي", padding=15)
        user_info_frame.pack(fill=X, pady=(0, 20))
        
        user_role = PermissionsManager.get_user_role(self.current_user)
        role_desc = PermissionsManager.get_role_description(user_role)
        
        info_text = f"""
👤 اسم المستخدم: {self.current_user.username}
📝 الاسم الكامل: {self.current_user.full_name}
🎭 نوع المستخدم: {role_desc}
✅ حالة الحساب: {'نشط' if self.current_user.is_active else 'غير نشط'}
👑 مدير النظام: {'نعم' if self.current_user.is_admin else 'لا'}
        """
        
        info_label = ttk_bs.Label(user_info_frame, text=info_text, font=("Arial", 11))
        info_label.pack(anchor="w")
        
        # الصلاحيات التفصيلية
        permissions_frame = ttk_bs.LabelFrame(scrollable_frame, text="الصلاحيات التفصيلية", padding=15)
        permissions_frame.pack(fill=BOTH, expand=True)
        
        permissions = PermissionsManager.get_user_permissions(self.current_user)
        
        if self.current_user.is_admin:
            admin_label = ttk_bs.Label(
                permissions_frame,
                text="🌟 أنت مدير النظام - لديك صلاحيات كاملة على جميع الوحدات",
                font=("Arial", 12, "bold"),
                bootstyle="success"
            )
            admin_label.pack(pady=10)
        
        # إنشاء جدول الصلاحيات
        columns = ("module", "view", "add", "edit", "delete", "print")
        tree = ttk.Treeview(permissions_frame, columns=columns, show="headings", height=10)
        
        # تعيين عناوين الأعمدة
        tree.heading("module", text="الوحدة")
        tree.heading("view", text="عرض")
        tree.heading("add", text="إضافة")
        tree.heading("edit", text="تعديل")
        tree.heading("delete", text="حذف")
        tree.heading("print", text="طباعة")
        
        # تعيين عرض الأعمدة
        tree.column("module", width=150)
        tree.column("view", width=80, anchor="center")
        tree.column("add", width=80, anchor="center")
        tree.column("edit", width=80, anchor="center")
        tree.column("delete", width=80, anchor="center")
        tree.column("print", width=80, anchor="center")
        
        # إضافة البيانات
        for module, module_name in PermissionsManager.MODULES.items():
            module_perms = permissions.get(module, [])
            
            row_data = [
                module_name,
                "✅" if "view" in module_perms else "❌",
                "✅" if "add" in module_perms else "❌",
                "✅" if "edit" in module_perms else "❌",
                "✅" if "delete" in module_perms else "❌",
                "✅" if "print" in module_perms else "❌"
            ]
            
            tree.insert("", "end", values=row_data)
        
        tree.pack(fill=BOTH, expand=True, pady=10)
        
        # شريط التمرير للجدول
        tree_scrollbar = ttk_bs.Scrollbar(permissions_frame, orient="vertical", command=tree.yview)
        tree.configure(yscrollcommand=tree_scrollbar.set)
        tree_scrollbar.pack(side="right", fill="y")
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
    
    def create_user_types_tab(self, notebook):
        """إنشاء تبويب أنواع المستخدمين"""
        frame = ttk_bs.Frame(notebook)
        notebook.add(frame, text="👥 أنواع المستخدمين")
        
        # إطار التمرير
        canvas = tk.Canvas(frame)
        scrollbar = ttk_bs.Scrollbar(frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk_bs.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # أنواع المستخدمين
        user_types = [
            {
                "title": "👑 مدير النظام (Admin)",
                "description": "مدير النظام - صلاحيات كاملة",
                "permissions": [
                    "✅ صلاحيات كاملة وغير محدودة على جميع أجزاء النظام",
                    "✅ إدارة المستخدمين (إضافة، تعديل، حذف)",
                    "✅ إدارة الأدوار والصلاحيات",
                    "✅ إدارة المخزون (عرض، إضافة، تعديل، حذف)",
                    "✅ إدارة المعاملات والحركات",
                    "✅ إدارة التقارير والطباعة",
                    "✅ إدارة الإعدادات العامة",
                    "✅ الوصول إلى سجل الأنشطة",
                    "✅ إدارة النسخ الاحتياطية"
                ],
                "color": "success"
            },
            {
                "title": "👨‍💼 مدير قسم (Manager)",
                "description": "مدير - صلاحيات إدارية",
                "permissions": [
                    "✅ إدارة المخزون (عرض، إضافة، تعديل)",
                    "✅ إدارة المعاملات",
                    "✅ عرض التقارير والطباعة",
                    "✅ إدارة المستفيدين",
                    "❌ لا يمكن: إدارة المستخدمين",
                    "❌ لا يمكن: تغيير إعدادات النظام",
                    "❌ لا يمكن: حذف البيانات الحساسة"
                ],
                "color": "primary"
            },
            {
                "title": "👤 مستخدم عادي (User)",
                "description": "مستخدم - صلاحيات محدودة",
                "permissions": [
                    "✅ عرض المخزون",
                    "✅ إضافة معاملات جديدة",
                    "✅ تعديل المعاملات الخاصة به",
                    "✅ طباعة التقارير الأساسية",
                    "❌ لا يمكن: حذف المعاملات",
                    "❌ لا يمكن: تعديل بيانات المخزون",
                    "❌ لا يمكن: الوصول للإعدادات"
                ],
                "color": "info"
            },
            {
                "title": "👁️ مشاهد (Viewer)",
                "description": "مشاهد - عرض فقط",
                "permissions": [
                    "✅ عرض فقط للمخزون والأصناف",
                    "✅ عرض فقط للمعاملات وحركات المخزون",
                    "✅ عرض فقط للمستفيدين",
                    "✅ عرض فقط للتقارير",
                    "✅ عرض فقط للإدارات والأقسام",
                    "❌ لا يمكن: إضافة أي بيانات جديدة",
                    "❌ لا يمكن: تعديل أي بيانات موجودة",
                    "❌ لا يمكن: حذف أي بيانات",
                    "❌ لا يمكن: طباعة التقارير",
                    "❌ لا يمكن: الوصول للإعدادات",
                    "❌ لا يمكن: إدارة المستخدمين",
                    "⚠️ ملاحظة: الأزرار غير المسموحة لن تظهر في الواجهة"
                ],
                "color": "warning"
            }
        ]
        
        for user_type in user_types:
            type_frame = ttk_bs.LabelFrame(
                scrollable_frame,
                text=user_type["title"],
                padding=15,
                bootstyle=user_type["color"]
            )
            type_frame.pack(fill=X, pady=(0, 15))
            
            desc_label = ttk_bs.Label(
                type_frame,
                text=user_type["description"],
                font=("Arial", 11, "bold")
            )
            desc_label.pack(anchor="w", pady=(0, 10))
            
            for permission in user_type["permissions"]:
                perm_label = ttk_bs.Label(
                    type_frame,
                    text=permission,
                    font=("Arial", 10)
                )
                perm_label.pack(anchor="w", padx=(20, 0))
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
    
    def create_usage_guide_tab(self, notebook):
        """إنشاء تبويب دليل الاستخدام"""
        frame = ttk_bs.Frame(notebook)
        notebook.add(frame, text="📚 دليل الاستخدام")
        
        # إطار التمرير
        canvas = tk.Canvas(frame)
        scrollbar = ttk_bs.Scrollbar(frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk_bs.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # دليل الاستخدام
        guide_sections = [
            {
                "title": "🚀 البدء السريع",
                "content": """
1. تسجيل الدخول: استخدم اسم المستخدم وكلمة المرور المخصصة لك
2. الشاشة الرئيسية: تعرض ملخص المخزون والأنشطة الحديثة
3. القوائم: استخدم شريط القوائم للوصول للوظائف المختلفة
4. الأزرار: الأزرار الملونة تشير لوظائف مختلفة (أخضر=إضافة، أزرق=تعديل، أحمر=حذف)
                """
            },
            {
                "title": "📦 إدارة المخزون",
                "content": """
• عرض الأصناف: قائمة "الأصناف" → "عرض جميع الأصناف"
• إضافة صنف جديد: قائمة "الأصناف" → "إضافة صنف جديد"
• تعديل صنف: انقر مرتين على الصنف في الجدول
• البحث: استخدم مربع البحث لإيجاد الأصناف بسرعة
• الفلترة: استخدم القوائم المنسدلة لفلترة النتائج
                """
            },
            {
                "title": "💼 المعاملات",
                "content": """
• معاملة جديدة: قائمة "المعاملات" → "معاملة جديدة"
• أنواع المعاملات: صرف، استلام، تحويل، جرد
• حفظ المعاملة: تأكد من ملء جميع البيانات المطلوبة
• طباعة المعاملة: استخدم زر "طباعة" بعد الحفظ
• تتبع المعاملات: قائمة "المعاملات" → "عرض جميع المعاملات"
                """
            },
            {
                "title": "👥 المستفيدين",
                "content": """
• إضافة مستفيد: قائمة "المستفيدين" → زر "إضافة مستفيد جديد"
• تصنيف المستفيدين: فرد، جهة، مؤسسة
• البحث السريع: استخدم مربع البحث للعثور على المستفيدين
• تعديل البيانات: انقر مرتين على المستفيد
• ربط المعاملات: المستفيدين يظهرون تلقائياً في المعاملات
                """
            },
            {
                "title": "📊 التقارير",
                "content": """
• تقارير المخزون: عرض حالة المخزون الحالية
• تقارير المعاملات: تقارير مفصلة حسب التاريخ والنوع
• تقارير المستفيدين: قوائم وإحصائيات المستفيدين
• التصدير: يمكن تصدير التقارير إلى Excel أو PDF
• الطباعة: طباعة مباشرة أو حفظ كملف PDF
                """
            }
        ]
        
        for section in guide_sections:
            section_frame = ttk_bs.LabelFrame(
                scrollable_frame,
                text=section["title"],
                padding=15
            )
            section_frame.pack(fill=X, pady=(0, 15))
            
            content_label = ttk_bs.Label(
                section_frame,
                text=section["content"],
                font=("Arial", 10),
                justify="left"
            )
            content_label.pack(anchor="w")
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
    
    def create_permissions_guide_tab(self, notebook):
        """إنشاء تبويب دليل التعرف على الصلاحيات"""
        frame = ttk_bs.Frame(notebook)
        notebook.add(frame, text="🔍 كيف أعرف صلاحياتي؟")
        
        # إطار التمرير
        canvas = tk.Canvas(frame)
        scrollbar = ttk_bs.Scrollbar(frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk_bs.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # دليل التعرف على الصلاحيات
        guide_sections = [
            {
                "title": "🔍 كيف أعرف نوع حسابي؟",
                "content": """
1. انظر إلى شريط العنوان في أعلى النافذة - يظهر اسمك
2. انظر إلى الزاوية اليمنى في شريط الأدوات - يظهر اسمك ونوع حسابك
3. افتح هذا الدليل وانظر إلى تبويب "صلاحياتي الحالية"
4. إذا كنت مدير النظام، ستجد رسالة "أنت مدير النظام - لديك صلاحيات كاملة"
                """
            },
            {
                "title": "👀 كيف أعرف ما يمكنني فعله؟",
                "content": """
• الأزرار المتاحة: إذا كان بإمكانك رؤية زر معين، فيمكنك استخدامه
• الأزرار المخفية: إذا لم تر زر "إضافة" أو "تعديل" أو "حذف"، فليس لديك هذه الصلاحية
• القوائم: إذا لم تظهر قائمة معينة في شريط القوائم، فليس لديك صلاحية للوصول إليها
• الرسائل: إذا حاولت فعل شيء غير مسموح، ستظهر رسالة خطأ
                """
            },
            {
                "title": "🚫 ماذا يحدث إذا لم تكن لدي صلاحية؟",
                "content": """
• الأزرار لن تظهر: إذا لم تكن لديك صلاحية، لن ترى الزر أصلاً
• القوائم مخفية: القوائم التي لا تملك صلاحية للوصول إليها لن تظهر
• رسائل واضحة: إذا حاولت الوصول لشيء غير مسموح، ستحصل على رسالة واضحة
• لا أخطاء مربكة: النظام مصمم ليخفي ما لا تحتاجه بدلاً من إظهار أخطاء
                """
            },
            {
                "title": "🎯 أمثلة عملية حسب نوع المستخدم",
                "content": """
👁️ المشاهد (Viewer):
• سترى: جداول البيانات، أزرار البحث والفلترة، زر "تحديث"
• لن ترى: أزرار "إضافة"، "تعديل"، "حذف"، "طباعة"، قائمة "إدارة النظام"

👤 المستخدم العادي (User):
• سترى: كل ما يراه المشاهد + أزرار "إضافة" للمعاملات والمستفيدين
• لن ترى: أزرار "تعديل" و "حذف" للمخزون، قائمة "إدارة النظام"

👨‍💼 مدير القسم (Manager):
• سترى: كل ما يراه المستخدم العادي + أزرار "تعديل" للمعاملات
• لن ترى: قائمة "إدارة النظام"، أزرار حذف البيانات الحساسة

👑 مدير النظام (Admin):
• سترى: جميع الأزرار والقوائم بدون استثناء
                """
            },
            {
                "title": "💡 نصائح مهمة",
                "content": """
• إذا كنت بحاجة لصلاحيات إضافية، اتصل بمدير النظام
• لا تحاول "اختراق" النظام - كل شيء مسجل ومراقب
• الصلاحيات مصممة لحماية البيانات وضمان الأمان
• إذا لم تجد ما تبحث عنه، تأكد أولاً من نوع حسابك
• استخدم زر "تحديث" إذا لم تظهر البيانات الجديدة
• راجع هذا الدليل دورياً لفهم صلاحياتك بشكل أفضل
                """
            }
        ]
        
        for section in guide_sections:
            section_frame = ttk_bs.LabelFrame(
                scrollable_frame,
                text=section["title"],
                padding=15
            )
            section_frame.pack(fill=X, pady=(0, 15))
            
            content_label = ttk_bs.Label(
                section_frame,
                text=section["content"],
                font=("Arial", 10),
                justify="left",
                wraplength=800
            )
            content_label.pack(anchor="w")
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
    
    def create_faq_tab(self, notebook):
        """إنشاء تبويب الأسئلة الشائعة"""
        frame = ttk_bs.Frame(notebook)
        notebook.add(frame, text="❓ أسئلة شائعة")
        
        # إطار التمرير
        canvas = tk.Canvas(frame)
        scrollbar = ttk_bs.Scrollbar(frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk_bs.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # الأسئلة الشائعة
        faqs = [
            {
                "question": "❓ لماذا لا أستطيع إضافة مستخدمين جدد؟",
                "answer": "إدارة المستخدمين متاحة فقط لمديري النظام. إذا كنت بحاجة لإضافة مستخدمين، اتصل بمدير النظام."
            },
            {
                "question": "❓ لماذا لا أستطيع حذف بعض البيانات؟",
                "answer": "صلاحية الحذف محدودة حسب نوع المستخدم. المشاهدون لا يمكنهم الحذف، والمستخدمون العاديون لا يمكنهم حذف بيانات المخزون."
            },
            {
                "question": "❓ كيف أغير كلمة المرور الخاصة بي؟",
                "answer": "اتصل بمدير النظام لتغيير كلمة المرور. لأسباب أمنية، لا يمكن للمستخدمين تغيير كلمات المرور بأنفسهم."
            },
            {
                "question": "❓ لماذا لا تظهر بعض القوائم في شريط القوائم؟",
                "answer": "القوائم تظهر حسب صلاحياتك. إذا لم تكن لديك صلاحية للوصول لوحدة معينة، فلن تظهر في القوائم."
            },
            {
                "question": "❓ كيف أطبع التقارير؟",
                "answer": "استخدم قائمة 'التقارير' واختر التقرير المطلوب، ثم اضغط على زر 'طباعة'. تأكد من أن لديك صلاحية الطباعة."
            },
            {
                "question": "❓ ماذا أفعل إذا نسيت كلمة المرور؟",
                "answer": "اتصل بمدير النظام فوراً. سيقوم بإعادة تعيين كلمة مرور جديدة لك."
            },
            {
                "question": "❓ لماذا لا أستطيع رؤية بعض الأزرار؟",
                "answer": "الأزرار تظهر حسب صلاحياتك. إذا لم تكن لديك صلاحية لوظيفة معينة، فلن يظهر الزر المخصص لها."
            },
            {
                "question": "❓ كيف أعرف صلاحياتي الحالية؟",
                "answer": "راجع تبويب 'صلاحياتي الحالية' في هذا الدليل، أو اسأل مدير النظام."
            }
        ]
        
        for faq in faqs:
            faq_frame = ttk_bs.LabelFrame(
                scrollable_frame,
                text=faq["question"],
                padding=15
            )
            faq_frame.pack(fill=X, pady=(0, 15))
            
            answer_label = ttk_bs.Label(
                faq_frame,
                text=faq["answer"],
                font=("Arial", 10),
                justify="left",
                wraplength=800
            )
            answer_label.pack(anchor="w")
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

# دالة لفتح دليل المستخدم
def show_user_guide(parent, current_user: User):
    """عرض دليل المستخدم"""
    UserGuideWindow(parent, current_user)