#!/usr/bin/env python3
"""
محسن الأداء - حل مشاكل التعليق والبطء
Performance Optimizer - Fix Freezing and Slow Performance Issues
"""

import threading
import time
import queue
import sqlite3
from concurrent.futures import ThreadPoolExecutor
import gc
from pathlib import Path

class PerformanceOptimizer:
    """محسن الأداء للتطبيق"""
    
    def __init__(self):
        self.thread_pool = ThreadPoolExecutor(max_workers=4)
        self.task_queue = queue.Queue()
        self.is_running = True
        
    @staticmethod
    def optimize_database_connection():
        """تحسين اتصال قاعدة البيانات"""
        optimizations = [
            "PRAGMA journal_mode = WAL",
            "PRAGMA synchronous = NORMAL", 
            "PRAGMA cache_size = 10000",
            "PRAGMA temp_store = MEMORY",
            "PRAGMA mmap_size = 268435456",
            "PRAGMA optimize"
        ]
        return optimizations
    
    @staticmethod
    def run_async_task(func, *args, callback=None, **kwargs):
        """تشغيل مهمة في خيط منفصل لتجنب التعليق"""
        def worker():
            try:
                result = func(*args, **kwargs)
                if callback:
                    callback(result)
                return result
            except Exception as e:
                print(f"خطأ في المهمة غير المتزامنة: {e}")
                if callback:
                    callback(None)
                return None
        
        thread = threading.Thread(target=worker, daemon=True)
        thread.start()
        return thread
    
    @staticmethod
    def batch_database_operations(operations, db_path):
        """تنفيذ عمليات قاعدة البيانات في دفعات لتحسين الأداء"""
        try:
            conn = sqlite3.connect(db_path)
            conn.execute("BEGIN TRANSACTION")
            
            # تطبيق تحسينات الأداء
            for optimization in PerformanceOptimizer.optimize_database_connection():
                try:
                    conn.execute(optimization)
                except:
                    pass
            
            # تنفيذ العمليات
            for operation in operations:
                if isinstance(operation, tuple):
                    query, params = operation
                    conn.execute(query, params)
                else:
                    conn.execute(operation)
            
            conn.execute("COMMIT")
            conn.close()
            return True
            
        except Exception as e:
            print(f"خطأ في عمليات قاعدة البيانات: {e}")
            try:
                conn.execute("ROLLBACK")
                conn.close()
            except:
                pass
            return False
    
    @staticmethod
    def cleanup_memory():
        """تنظيف الذاكرة"""
        gc.collect()
        
    @staticmethod
    def optimize_excel_import(file_path, progress_callback=None):
        """تحسين استيراد ملفات Excel"""
        import pandas as pd
        
        def import_worker():
            try:
                # قراءة الملف في أجزاء صغيرة
                chunk_size = 1000
                chunks = []
                
                # قراءة الملف
                if progress_callback:
                    progress_callback(10, "جاري قراءة الملف...")
                
                df = pd.read_excel(file_path)
                total_rows = len(df)
                
                # معالجة البيانات في أجزاء
                for i in range(0, total_rows, chunk_size):
                    chunk = df.iloc[i:i+chunk_size]
                    chunks.append(chunk)
                    
                    if progress_callback:
                        progress = 10 + (i / total_rows) * 80
                        progress_callback(progress, f"معالجة البيانات... {i}/{total_rows}")
                
                if progress_callback:
                    progress_callback(100, "تم الانتهاء من الاستيراد")
                
                return chunks
                
            except Exception as e:
                print(f"خطأ في استيراد Excel: {e}")
                if progress_callback:
                    progress_callback(0, f"خطأ: {e}")
                return None
        
        return import_worker()

class AsyncTaskManager:
    """مدير المهام غير المتزامنة"""
    
    def __init__(self):
        self.tasks = {}
        self.task_counter = 0
    
    def add_task(self, func, *args, callback=None, **kwargs):
        """إضافة مهمة جديدة"""
        task_id = self.task_counter
        self.task_counter += 1
        
        def task_wrapper():
            try:
                result = func(*args, **kwargs)
                if callback:
                    callback(result)
                # إزالة المهمة بعد الانتهاء
                if task_id in self.tasks:
                    del self.tasks[task_id]
                return result
            except Exception as e:
                print(f"خطأ في المهمة {task_id}: {e}")
                if callback:
                    callback(None)
                if task_id in self.tasks:
                    del self.tasks[task_id]
        
        thread = threading.Thread(target=task_wrapper, daemon=True)
        self.tasks[task_id] = thread
        thread.start()
        
        return task_id
    
    def wait_for_task(self, task_id, timeout=30):
        """انتظار انتهاء مهمة معينة"""
        if task_id in self.tasks:
            self.tasks[task_id].join(timeout)
    
    def cancel_all_tasks(self):
        """إلغاء جميع المهام"""
        self.tasks.clear()

# إنشاء مدير المهام العام
task_manager = AsyncTaskManager()