"""
تقرير البحث المحسن عن عمليات الصرف مع البحث التلقائي
Enhanced Transactions Search Report with Auto Search
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime, date
from database import db_manager
import csv
import os
import threading

class EnhancedTransactionsReport:
    def __init__(self, parent):
        self.parent = parent
        self.window = tk.Toplevel(parent)
        self.window.title("🔍 تقرير البحث المتقدم - عمليات الصرف")
        self.window.geometry("1400x800")
        self.window.resizable(True, True)
        
        # توسيط النافذة
        self.center_window()
        
        # متغيرات البحث
        self.search_var = tk.StringVar()
        self.beneficiary_var = tk.StringVar()
        self.receiver_var = tk.StringVar()
        self.item_number_var = tk.StringVar()
        self.item_name_var = tk.StringVar()
        self.date_from_var = tk.StringVar()
        self.date_to_var = tk.StringVar()
        self.transaction_number_var = tk.StringVar()
        
        # ربط البحث التلقائي
        self.search_var.trace('w', self.auto_search)
        self.beneficiary_var.trace('w', self.auto_search)
        self.receiver_var.trace('w', self.auto_search)
        self.item_number_var.trace('w', self.auto_search)
        self.item_name_var.trace('w', self.auto_search)
        self.transaction_number_var.trace('w', self.auto_search)
        self.date_from_var.trace('w', self.auto_search)
        self.date_to_var.trace('w', self.auto_search)
        
        self.setup_ui()
        self.load_initial_data()
        
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        x = (screen_width - 1400) // 2
        y = (screen_height - 800) // 2
        self.window.geometry(f"1400x800+{x}+{y}")
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # تكوين الشبكة
        self.window.columnconfigure(0, weight=1)
        self.window.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # العنوان
        title_frame = ttk.Frame(main_frame)
        title_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        
        title_label = ttk.Label(title_frame, text="🔍 تقرير البحث المتقدم - عمليات الصرف", 
                               font=('Arial', 18, 'bold'))
        title_label.pack(side=tk.LEFT)
        
        # مؤشر البحث التلقائي
        self.search_indicator = ttk.Label(title_frame, text="🔄 البحث التلقائي مفعل", 
                                         font=('Arial', 10), foreground='green')
        self.search_indicator.pack(side=tk.RIGHT)
        
        # إطار البحث السريع
        quick_search_frame = ttk.LabelFrame(main_frame, text="🔍 البحث السريع (في جميع الحقول)", padding="10")
        quick_search_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        quick_search_frame.columnconfigure(1, weight=1)
        
        ttk.Label(quick_search_frame, text="البحث العام:", font=('Arial', 10, 'bold')).grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        search_entry = ttk.Entry(quick_search_frame, textvariable=self.search_var, width=50, font=('Arial', 11))
        search_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        search_entry.focus()
        
        # ربط أحداث إضافية لحقل البحث
        search_entry.bind('<KeyRelease>', lambda e: self.auto_search())
        search_entry.bind('<Return>', lambda e: self.manual_search())
        search_entry.bind('<FocusOut>', lambda e: self.auto_search())
        
        ttk.Label(quick_search_frame, text="💡 اكتب أي كلمة للبحث في جميع الحقول تلقائياً", 
                 font=('Arial', 9), foreground='blue').grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=(5, 0))
        
        # إطار البحث المتقدم
        advanced_search_frame = ttk.LabelFrame(main_frame, text="🎯 البحث المتقدم (حقول محددة)", padding="10")
        advanced_search_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        advanced_search_frame.columnconfigure(1, weight=1)
        advanced_search_frame.columnconfigure(3, weight=1)
        advanced_search_frame.columnconfigure(5, weight=1)
        
        # الصف الأول
        ttk.Label(advanced_search_frame, text="المستفيد:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        beneficiary_entry = ttk.Entry(advanced_search_frame, textvariable=self.beneficiary_var, width=18)
        beneficiary_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 15))
        
        ttk.Label(advanced_search_frame, text="المندوب المستلم:").grid(row=0, column=2, sticky=tk.W, padx=(0, 10))
        receiver_entry = ttk.Entry(advanced_search_frame, textvariable=self.receiver_var, width=18)
        receiver_entry.grid(row=0, column=3, sticky=(tk.W, tk.E), padx=(0, 15))
        
        ttk.Label(advanced_search_frame, text="رقم السند:").grid(row=0, column=4, sticky=tk.W, padx=(0, 10))
        transaction_entry = ttk.Entry(advanced_search_frame, textvariable=self.transaction_number_var, width=15)
        transaction_entry.grid(row=0, column=5, sticky=(tk.W, tk.E))
        
        # الصف الثاني
        ttk.Label(advanced_search_frame, text="رقم الصنف:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(10, 0))
        item_number_entry = ttk.Entry(advanced_search_frame, textvariable=self.item_number_var, width=18)
        item_number_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(0, 15), pady=(10, 0))
        
        ttk.Label(advanced_search_frame, text="اسم الصنف:").grid(row=1, column=2, sticky=tk.W, padx=(0, 10), pady=(10, 0))
        item_name_entry = ttk.Entry(advanced_search_frame, textvariable=self.item_name_var, width=18)
        item_name_entry.grid(row=1, column=3, sticky=(tk.W, tk.E), padx=(0, 15), pady=(10, 0))
        
        # الصف الثالث - التواريخ
        ttk.Label(advanced_search_frame, text="من تاريخ:").grid(row=2, column=0, sticky=tk.W, padx=(0, 10), pady=(10, 0))
        date_from_entry = ttk.Entry(advanced_search_frame, textvariable=self.date_from_var, width=15)
        date_from_entry.grid(row=2, column=1, sticky=tk.W, padx=(0, 15), pady=(10, 0))
        
        ttk.Label(advanced_search_frame, text="إلى تاريخ:").grid(row=2, column=2, sticky=tk.W, padx=(0, 10), pady=(10, 0))
        date_to_entry = ttk.Entry(advanced_search_frame, textvariable=self.date_to_var, width=15)
        date_to_entry.grid(row=2, column=3, sticky=tk.W, padx=(0, 15), pady=(10, 0))
        
        # تلميحات التواريخ
        ttk.Label(advanced_search_frame, text="(YYYY-MM-DD)", font=('Arial', 8), foreground='gray').grid(row=3, column=1, sticky=tk.W, padx=(0, 15))
        ttk.Label(advanced_search_frame, text="(YYYY-MM-DD)", font=('Arial', 8), foreground='gray').grid(row=3, column=3, sticky=tk.W, padx=(0, 15))
        
        # أزرار التحكم
        control_frame = ttk.Frame(advanced_search_frame)
        control_frame.grid(row=2, column=4, columnspan=2, sticky=tk.E, pady=(10, 0))
        
        ttk.Button(control_frame, text="🔍 بحث يدوي", command=self.manual_search, width=15).pack(side=tk.LEFT, padx=(0, 8))
        ttk.Button(control_frame, text="🧹 مسح الكل", command=self.clear_all_search, width=15).pack(side=tk.LEFT, padx=(0, 8))
        
        # ربط أحداث إضافية لحقول البحث المتقدم (بعد تعريفها)
        beneficiary_entry.bind('<KeyRelease>', lambda e: self.auto_search())
        receiver_entry.bind('<KeyRelease>', lambda e: self.auto_search())
        transaction_entry.bind('<KeyRelease>', lambda e: self.auto_search())
        item_number_entry.bind('<KeyRelease>', lambda e: self.auto_search())
        item_name_entry.bind('<KeyRelease>', lambda e: self.auto_search())
        date_from_entry.bind('<KeyRelease>', lambda e: self.auto_search())
        date_to_entry.bind('<KeyRelease>', lambda e: self.auto_search())
        
        # إطار النتائج
        results_frame = ttk.LabelFrame(main_frame, text="📊 نتائج البحث", padding="10")
        results_frame.grid(row=3, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        results_frame.columnconfigure(0, weight=1)
        results_frame.rowconfigure(0, weight=1)
        
        # جدول النتائج
        columns = ("رقم السند", "تاريخ السند", "المستفيد", "المندوب المستلم", 
                  "رقم الصنف", "اسم الصنف", "الكمية", "ملاحظات")
        self.tree = ttk.Treeview(results_frame, columns=columns, show="headings", height=18)
        
        # تعريف الأعمدة
        self.tree.heading("رقم السند", text="رقم السند")
        self.tree.heading("تاريخ السند", text="تاريخ السند")
        self.tree.heading("المستفيد", text="المستفيد")
        self.tree.heading("المندوب المستلم", text="المندوب المستلم")
        self.tree.heading("رقم الصنف", text="رقم الصنف")
        self.tree.heading("اسم الصنف", text="اسم الصنف")
        self.tree.heading("الكمية", text="الكمية")
        self.tree.heading("ملاحظات", text="ملاحظات")
        
        # تحديد عرض الأعمدة
        self.tree.column("رقم السند", width=100, anchor=tk.CENTER)
        self.tree.column("تاريخ السند", width=100, anchor=tk.CENTER)
        self.tree.column("المستفيد", width=150, anchor=tk.W)
        self.tree.column("المندوب المستلم", width=150, anchor=tk.W)
        self.tree.column("رقم الصنف", width=100, anchor=tk.CENTER)
        self.tree.column("اسم الصنف", width=200, anchor=tk.W)
        self.tree.column("الكمية", width=80, anchor=tk.CENTER)
        self.tree.column("ملاحظات", width=150, anchor=tk.W)
        
        # شريط التمرير
        scrollbar_v = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.tree.yview)
        scrollbar_h = ttk.Scrollbar(results_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        self.tree.configure(yscrollcommand=scrollbar_v.set, xscrollcommand=scrollbar_h.set)
        
        # وضع الجدول وأشرطة التمرير
        self.tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar_v.grid(row=0, column=1, sticky=(tk.N, tk.S))
        scrollbar_h.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        # إطار الإحصائيات والأزرار
        bottom_frame = ttk.Frame(main_frame)
        bottom_frame.grid(row=4, column=0, sticky=(tk.W, tk.E), pady=(10, 0))
        bottom_frame.columnconfigure(1, weight=1)
        
        # الإحصائيات
        stats_frame = ttk.Frame(bottom_frame)
        stats_frame.grid(row=0, column=0, sticky=tk.W)
        
        self.stats_label = ttk.Label(stats_frame, text="📊 عدد النتائج: 0", font=('Arial', 11, 'bold'))
        self.stats_label.pack(side=tk.LEFT, padx=(0, 20))
        
        self.search_time_label = ttk.Label(stats_frame, text="⏱️ وقت البحث: 0.00 ثانية", font=('Arial', 9))
        self.search_time_label.pack(side=tk.LEFT)
        
        # أزرار التقارير
        reports_frame = ttk.Frame(bottom_frame)
        reports_frame.grid(row=0, column=2, sticky=tk.E)
        
        ttk.Button(reports_frame, text="🖨️ طباعة", command=self.print_report, width=15).pack(side=tk.LEFT, padx=(0, 8))
        ttk.Button(reports_frame, text="📊 تصدير Excel", command=self.export_to_excel, width=15).pack(side=tk.LEFT, padx=(0, 8))
        ttk.Button(reports_frame, text="📄 تصدير PDF", command=self.export_to_pdf, width=15).pack(side=tk.LEFT, padx=(0, 8))
        
    def auto_search(self, *args):
        """البحث التلقائي عند تغيير أي حقل"""
        try:
            # تأخير قصير لتجنب البحث المتكرر أثناء الكتابة
            if hasattr(self, 'search_timer'):
                self.window.after_cancel(self.search_timer)
            
            # تحديث مؤشر البحث
            self.search_indicator.config(text="⏳ انتظار...", foreground='orange')
            
            # تقليل التأخير لجعل البحث أسرع
            self.search_timer = self.window.after(500, self.perform_search)  # تأخير 500ms
            print("[بحث تلقائي] تم تشغيل البحث التلقائي")
            
        except Exception as e:
            print(f"[خطأ البحث التلقائي] {str(e)}")
            # في حالة الخطأ، قم بالبحث مباشرة
            self.perform_search()
        
    def manual_search(self):
        """البحث اليدوي"""
        self.perform_search()
        
    def perform_search(self):
        """تنفيذ البحث"""
        start_time = datetime.now()
        
        try:
            # تحديث مؤشر البحث
            self.search_indicator.config(text="🔄 جاري البحث...", foreground='orange')
            self.window.update()
            
            # مسح النتائج الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # بناء الاستعلام المحسن - يدعم الأصناف من جدولي items و added_items
            query = """
            SELECT DISTINCT
                COALESCE(t.transaction_number, '') as transaction_number,
                COALESCE(t.transaction_date, '') as transaction_date,
                COALESCE(b.name, '') as beneficiary_name,
                COALESCE(r.name, '') as receiver_name,
                COALESCE(
                    i.code,
                    ai.item_number,
                    CAST(ti.item_id AS TEXT)
                ) as item_number,
                COALESCE(
                    i.name,
                    ai.item_name,
                    'صنف غير محدد'
                ) as item_name,
                CAST(ROUND(COALESCE(ti.quantity, 0)) AS INTEGER) as quantity,
                COALESCE(
                    i.unit,
                    ai.unit,
                    'وحدة'
                ) as unit_symbol,
                COALESCE(ti.notes, '') as notes
            FROM transactions t
            LEFT JOIN beneficiaries b ON t.beneficiary_id = b.id
            LEFT JOIN beneficiaries r ON t.receiver_id = r.id
            LEFT JOIN transaction_items ti ON t.id = ti.transaction_id
            LEFT JOIN items i ON ti.item_id = i.id
            LEFT JOIN added_items ai ON ti.item_id = ai.id
            WHERE t.id IS NOT NULL AND ti.id IS NOT NULL
            """
            
            params = []
            
            # البحث العام (في جميع الحقول)
            search_text = self.search_var.get().strip()
            if search_text:
                search_term = f"%{search_text}%"
                query += """ AND (
                    LOWER(COALESCE(t.transaction_number, '')) LIKE LOWER(?) OR
                    LOWER(COALESCE(b.name, '')) LIKE LOWER(?) OR
                    LOWER(COALESCE(r.name, '')) LIKE LOWER(?) OR
                    LOWER(COALESCE(i.name, '')) LIKE LOWER(?) OR
                    LOWER(COALESCE(ai.item_name, '')) LIKE LOWER(?) OR
                    LOWER(COALESCE(i.code, '')) LIKE LOWER(?) OR
                    LOWER(COALESCE(ai.item_number, '')) LIKE LOWER(?) OR
                    LOWER(CAST(COALESCE(i.id, ti.item_id) AS TEXT)) LIKE LOWER(?) OR
                    LOWER(COALESCE(ti.notes, '')) LIKE LOWER(?)
                )"""
                params.extend([search_term] * 9)
                print(f"[بحث عام] البحث عن: '{search_text}'")
            
            # البحث المتقدم (حقول محددة)
            if self.beneficiary_var.get().strip():
                query += " AND COALESCE(b.name, '') LIKE ?"
                params.append(f"%{self.beneficiary_var.get().strip()}%")
                print(f"[بحث المستفيد] البحث عن: '{self.beneficiary_var.get().strip()}'")
            
            if self.receiver_var.get().strip():
                query += " AND COALESCE(r.name, '') LIKE ?"
                params.append(f"%{self.receiver_var.get().strip()}%")
                print(f"[بحث المندوب] البحث عن: '{self.receiver_var.get().strip()}'")
            
            if self.transaction_number_var.get().strip():
                query += " AND t.transaction_number LIKE ?"
                params.append(f"%{self.transaction_number_var.get().strip()}%")
                print(f"[بحث رقم السند] البحث عن: '{self.transaction_number_var.get().strip()}'")
            
            if self.item_number_var.get().strip():
                query += " AND (COALESCE(i.code, '') LIKE ? OR COALESCE(ai.item_number, '') LIKE ? OR CAST(COALESCE(i.id, ti.item_id) AS TEXT) LIKE ?)"
                search_term = f"%{self.item_number_var.get().strip()}%"
                params.extend([search_term, search_term, search_term])
                print(f"[بحث رقم الصنف] البحث عن: '{self.item_number_var.get().strip()}'")
            
            if self.item_name_var.get().strip():
                query += " AND (COALESCE(i.name, '') LIKE ? OR COALESCE(ai.item_name, '') LIKE ?)"
                search_term = f"%{self.item_name_var.get().strip()}%"
                params.extend([search_term, search_term])
                print(f"[بحث اسم الصنف] البحث عن: '{self.item_name_var.get().strip()}'")
            
            if self.date_from_var.get().strip():
                query += " AND t.transaction_date >= ?"
                params.append(self.date_from_var.get().strip())
            
            if self.date_to_var.get().strip():
                query += " AND t.transaction_date <= ?"
                params.append(self.date_to_var.get().strip())
            
            query += " ORDER BY t.transaction_date DESC, t.transaction_number"
            
            # طباعة الاستعلام للتشخيص
            print(f"[استعلام] {query}")
            print(f"[معاملات] {params}")
            
            # تنفيذ الاستعلام
            results = db_manager.fetch_all(query, tuple(params) if params else None)
            print(f"[نتائج] تم العثور على {len(results)} نتيجة")
            
            # إضافة النتائج للجدول
            for i, row in enumerate(results):
                try:
                    # تحويل الكمية إلى عدد صحيح
                    quantity = row[6] if row[6] is not None else 0
                    try:
                        quantity = int(float(quantity))
                    except (ValueError, TypeError):
                        quantity = 0
                    
                    values = (
                        str(row[0] or ""),  # transaction_number
                        str(row[1] or ""),  # transaction_date
                        str(row[2] or ""),  # beneficiary_name
                        str(row[3] or ""),  # receiver_name
                        str(row[4] or ""),  # item_number
                        str(row[5] or ""),  # item_name
                        str(quantity),      # quantity as integer
                        str(row[8] or "")   # notes (تخطي عمود الوحدة row[7])
                    )
                    self.tree.insert("", tk.END, values=values)
                except Exception as row_error:
                    print(f"[خطأ صف] خطأ في إضافة الصف {i}: {row_error}")
                    print(f"[بيانات الصف] {row}")
            
            # حساب وقت البحث
            end_time = datetime.now()
            search_duration = (end_time - start_time).total_seconds()
            
            # تحديث الإحصائيات
            count = len(results)
            self.stats_label.config(text=f"📊 عدد النتائج: {count}")
            self.search_time_label.config(text=f"⏱️ وقت البحث: {search_duration:.2f} ثانية")
            
            # تحديث مؤشر البحث
            self.search_indicator.config(text="✅ البحث التلقائي مفعل", foreground='green')
            
            print(f"[اكتمل] البحث اكتمل بنجاح - {count} نتيجة في {search_duration:.2f} ثانية")
            
        except Exception as e:
            print(f"[خطأ البحث] {str(e)}")
            import traceback
            traceback.print_exc()
            messagebox.showerror("خطأ", f"فشل في البحث: {str(e)}")
            self.search_indicator.config(text="❌ خطأ في البحث", foreground='red')
    
    def clear_all_search(self):
        """مسح جميع حقول البحث وإعادة تحميل جميع البيانات"""
        print("[مسح البحث] مسح جميع حقول البحث...")
        
        # إلغاء أي مؤقت بحث نشط
        if hasattr(self, 'search_timer'):
            self.window.after_cancel(self.search_timer)
        
        # مسح جميع حقول البحث
        self.search_var.set("")
        self.beneficiary_var.set("")
        self.receiver_var.set("")
        self.item_number_var.set("")
        self.item_name_var.set("")
        self.date_from_var.set("")
        self.date_to_var.set("")
        self.transaction_number_var.set("")
        
        # إعادة تحميل جميع البيانات
        self.window.after(100, self.perform_search)
        
    def load_initial_data(self):
        """تحميل البيانات الأولية - عرض جميع عمليات الصرف"""
        print("[تحميل أولي] بدء تحميل جميع عمليات الصرف...")
        try:
            # تأخير قصير للتأكد من أن الواجهة جاهزة
            self.window.after(200, self.perform_initial_load)
        except Exception as e:
            print(f"[خطأ تحميل أولي] {str(e)}")
            # في حالة الخطأ، قم بالتحميل مباشرة
            self.perform_initial_load()
    
    def perform_initial_load(self):
        """تنفيذ التحميل الأولي لجميع البيانات"""
        print("[تحميل أولي] تنفيذ التحميل الأولي...")
        # مسح جميع حقول البحث لعرض جميع البيانات
        self.search_var.set("")
        self.beneficiary_var.set("")
        self.receiver_var.set("")
        self.item_number_var.set("")
        self.item_name_var.set("")
        self.date_from_var.set("")
        self.date_to_var.set("")
        self.transaction_number_var.set("")
        
        # تنفيذ البحث لعرض جميع البيانات
        self.perform_search()
        

    
    def print_report(self):
        """طباعة التقرير"""
        try:
            # الحصول على البيانات من الجدول
            data = []
            for item in self.tree.get_children():
                values = self.tree.item(item)['values']
                data.append(values)
            
            if not data:
                messagebox.showwarning("تحذير", "لا توجد بيانات للطباعة")
                return
            
            # إنشاء ملف HTML مؤقت للطباعة
            import tempfile
            import webbrowser
            
            html_content = self.generate_html_report(data)
            
            # إنشاء ملف مؤقت
            with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as f:
                f.write(html_content)
                temp_filename = f.name
            
            # فتح الملف في المتصفح للطباعة
            webbrowser.open(f'file://{temp_filename}')
            
            messagebox.showinfo("طباعة", "تم فتح التقرير في المتصفح\nيمكنك الآن طباعته باستخدام Ctrl+P")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في طباعة التقرير: {str(e)}")
    
    def export_to_excel(self):
        """تصدير النتائج إلى ملف Excel"""
        try:
            # الحصول على البيانات من الجدول
            data = []
            for item in self.tree.get_children():
                values = self.tree.item(item)['values']
                data.append(values)
            
            if not data:
                messagebox.showwarning("تحذير", "لا توجد بيانات للتصدير")
                return
            
            # اختيار مكان الحفظ
            filename = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                title="حفظ التقرير Excel",
                initialname=f"transactions_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            )
            
            if filename:
                try:
                    import pandas as pd
                    
                    # إنشاء DataFrame
                    headers = ["رقم السند", "تاريخ السند", "المستفيد", "المندوب المستلم", 
                              "رقم الصنف", "اسم الصنف", "الكمية", "ملاحظات"]
                    
                    df = pd.DataFrame(data, columns=headers)
                    
                    # حفظ إلى Excel
                    with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                        df.to_excel(writer, sheet_name='تقرير عمليات الصرف', index=False)
                        
                        # تنسيق الورقة
                        worksheet = writer.sheets['تقرير عمليات الصرف']
                        
                        # تعديل عرض الأعمدة
                        for column in worksheet.columns:
                            max_length = 0
                            column_letter = column[0].column_letter
                            for cell in column:
                                try:
                                    if len(str(cell.value)) > max_length:
                                        max_length = len(str(cell.value))
                                except:
                                    pass
                            adjusted_width = min(max_length + 2, 50)
                            worksheet.column_dimensions[column_letter].width = adjusted_width
                    
                    messagebox.showinfo("نجح", f"تم تصدير التقرير Excel بنجاح إلى:\n{filename}")
                    
                except ImportError:
                    messagebox.showerror("خطأ", "مكتبات pandas أو openpyxl غير مثبتة\nيرجى تثبيتها باستخدام:\npip install pandas openpyxl")
                except Exception as e:
                    messagebox.showerror("خطأ", f"فشل في إنشاء ملف Excel: {str(e)}")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصدير Excel: {str(e)}")
    
    def export_to_pdf(self):
        """تصدير النتائج إلى ملف PDF"""
        try:
            # الحصول على البيانات من الجدول
            data = []
            for item in self.tree.get_children():
                values = self.tree.item(item)['values']
                data.append(values)
            
            if not data:
                messagebox.showwarning("تحذير", "لا توجد بيانات للتصدير")
                return
            
            # اختيار مكان الحفظ
            filename = filedialog.asksaveasfilename(
                defaultextension=".pdf",
                filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")],
                title="حفظ التقرير PDF",
                initialname=f"transactions_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
            )
            
            if filename:
                self.create_pdf_report(data, filename)
                messagebox.showinfo("نجح", f"تم تصدير التقرير PDF بنجاح إلى:\n{filename}")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصدير PDF: {str(e)}")
    
    def create_pdf_report(self, data, filename):
        """إنشاء تقرير PDF"""
        try:
            # استخدام HTML لإنشاء PDF
            html_content = self.generate_html_report(data)
            
            # حفظ HTML مؤقت
            import tempfile
            with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as f:
                f.write(html_content)
                temp_html = f.name
            
            # محاولة تحويل HTML إلى PDF باستخدام wkhtmltopdf إذا كان متوفراً
            try:
                import subprocess
                subprocess.run([
                    'wkhtmltopdf', 
                    '--page-size', 'A4',
                    '--orientation', 'Landscape',
                    '--encoding', 'UTF-8',
                    temp_html, 
                    filename
                ], check=True)
            except (subprocess.CalledProcessError, FileNotFoundError):
                # إذا لم يكن wkhtmltopdf متوفراً، احفظ كـ HTML
                import shutil
                shutil.copy(temp_html, filename.replace('.pdf', '.html'))
                messagebox.showinfo("تنبيه", "تم حفظ التقرير كملف HTML بدلاً من PDF\nيمكنك طباعته من المتصفح")
            
            # حذف الملف المؤقت
            import os
            try:
                os.unlink(temp_html)
            except:
                pass
                
        except Exception as e:
            raise Exception(f"فشل في إنشاء PDF: {str(e)}")
    
    def generate_html_report(self, data):
        """إنشاء تقرير HTML للطباعة"""
        html = f"""
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>تقرير عمليات الصرف</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                h1 {{ text-align: center; color: #333; }}
                table {{ width: 100%; border-collapse: collapse; margin-top: 20px; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: right; }}
                th {{ background-color: #f2f2f2; font-weight: bold; }}
                .header {{ text-align: center; margin-bottom: 20px; }}
                .date {{ text-align: left; font-size: 12px; color: #666; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>تقرير عمليات الصرف</h1>
                <div class="date">تاريخ الطباعة: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</div>
                <div class="date">عدد النتائج: {len(data)}</div>
            </div>
            <table>
                <thead>
                    <tr>
                        <th>رقم السند</th>
                        <th>تاريخ السند</th>
                        <th>المستفيد</th>
                        <th>المندوب المستلم</th>
                        <th>رقم الصنف</th>
                        <th>اسم الصنف</th>
                        <th>الكمية</th>
                        <th>ملاحظات</th>
                    </tr>
                </thead>
                <tbody>
        """
        
        for row in data:
            html += "<tr>"
            for cell in row:
                html += f"<td>{cell}</td>"
            html += "</tr>"
        
        html += """
                </tbody>
            </table>
        </body>
        </html>
        """
        
        return html
    
    def create_pdf_report_with_reportlab(self, data, filename):
        """إنشاء تقرير PDF باستخدام ReportLab"""
        try:
            from reportlab.lib.pagesizes import A4, landscape
            from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
            from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
            from reportlab.lib import colors
            from reportlab.pdfbase import pdfmetrics
            from reportlab.pdfbase.ttfonts import TTFont
            
            # إنشاء المستند
            doc = SimpleDocTemplate(filename, pagesize=landscape(A4))
            elements = []
            
            # العنوان
            styles = getSampleStyleSheet()
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=16,
                spaceAfter=30,
                alignment=1  # وسط
            )
            
            title = Paragraph("تقرير عمليات الصرف", title_style)
            elements.append(title)
            
            # معلومات التقرير
            info_style = ParagraphStyle(
                'Info',
                parent=styles['Normal'],
                fontSize=10,
                spaceAfter=20
            )
            
            info_text = f"تاريخ الطباعة: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} | عدد النتائج: {len(data)}"
            info = Paragraph(info_text, info_style)
            elements.append(info)
            
            # إنشاء الجدول
            headers = ["رقم السند", "تاريخ السند", "المستفيد", "المندوب المستلم", 
                      "رقم الصنف", "اسم الصنف", "الكمية", "ملاحظات"]
            
            table_data = [headers] + data
            
            table = Table(table_data)
            table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('FONTSIZE', (0, 1), (-1, -1), 8),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            
            elements.append(table)
            
            # بناء المستند
            doc.build(elements)
            
        except ImportError:
            messagebox.showerror("خطأ", "مكتبة reportlab غير مثبتة\nيرجى تثبيتها باستخدام: pip install reportlab")
        except Exception as e:
            raise e

def main():
    """اختبار النافذة"""
    root = tk.Tk()
    root.withdraw()  # إخفاء النافذة الرئيسية
    
    app = EnhancedTransactionsReport(root)
    root.mainloop()

if __name__ == "__main__":
    main()