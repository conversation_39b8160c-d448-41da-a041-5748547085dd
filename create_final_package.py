#!/usr/bin/env python3
"""
إنشاء الحزمة النهائية المحدثة - نظام إدارة المخازن
Create Final Updated Package - Desktop Stores Management System
"""

import os
import shutil
from pathlib import Path
import time

def create_final_updated_package():
    """إنشاء الحزمة النهائية المحدثة"""
    print("📦 إنشاء الحزمة النهائية المحدثة...")
    print("=" * 60)
    
    # مسارات المجلدات
    source_dir = Path("dist_updated/نظام_إدارة_المخازن_محدث")
    final_package_dir = Path("Desktop_Stores_Management_Final")
    
    if not source_dir.exists():
        print("❌ مجلد التطبيق المحدث غير موجود!")
        return False
    
    # حذف الحزمة السابقة إن وجدت
    if final_package_dir.exists():
        shutil.rmtree(final_package_dir)
        print("🗑️ تم حذف الحزمة السابقة")
    
    # نسخ التطبيق المحدث
    print("📁 نسخ ملفات التطبيق المحدث...")
    shutil.copytree(source_dir, final_package_dir)
    
    # إعادة تسمية الملف التنفيذي
    old_exe = final_package_dir / "نظام_إدارة_المخازن_محدث.exe"
    new_exe = final_package_dir / "نظام_إدارة_المخازن.exe"
    if old_exe.exists():
        old_exe.rename(new_exe)
        print("✅ تم إعادة تسمية الملف التنفيذي")
    
    # إنشاء مجلدات البيانات
    data_dirs = ["data", "reports", "backups", "logs"]
    for dir_name in data_dirs:
        dir_path = final_package_dir / dir_name
        dir_path.mkdir(exist_ok=True)
        
        # إنشاء ملف README في كل مجلد
        readme_content = {
            "data": "مجلد البيانات - يحتوي على قاعدة البيانات والملفات المهمة",
            "reports": "مجلد التقارير - يحتوي على التقارير المُصدرة", 
            "backups": "مجلد النسخ الاحتياطية - يحتوي على نسخ احتياطية من البيانات",
            "logs": "مجلد السجلات - يحتوي على ملفات سجل التطبيق"
        }
        
        readme_file = dir_path / "README.txt"
        readme_file.write_text(readme_content[dir_name], encoding='utf-8')
        print(f"✅ تم إنشاء مجلد: {dir_name}")
    
    # إنشاء ملف تشغيل سريع
    batch_content = """@echo off
chcp 65001 > nul
title نظام إدارة المخازن والمستودعات - الإصدار المحدث

echo ========================================
echo 🏪 نظام إدارة المخازن والمستودعات
echo 📈 الإصدار المحدث مع تحسينات الأداء
echo ========================================
echo.
echo 🚀 بدء تشغيل التطبيق...
echo.

cd /d "%~dp0"

start "" "نظام_إدارة_المخازن.exe"

echo ✅ تم تشغيل التطبيق
echo 💡 التحسينات الجديدة:
echo    - حل مشاكل التعليق في استيراد Excel
echo    - تحسين سرعة حفظ المعاملات
echo    - واجهة أكثر استجابة
echo.
echo يمكنك إغلاق هذه النافذة الآن
timeout /t 5 > nul
"""
    
    batch_file = final_package_dir / "تشغيل_البرنامج.bat"
    batch_file.write_text(batch_content, encoding='utf-8')
    print("✅ تم إنشاء ملف التشغيل السريع")
    
    # نسخ ملف README
    if Path("README.md").exists():
        shutil.copy2("README.md", final_package_dir / "اقرأني.md")
        print("✅ تم نسخ ملف التعليمات")
    
    # إنشاء ملف معلومات الإصدار المحدث
    version_info = f"""نظام إدارة المخازن والمستودعات
Desktop Stores Management System

الإصدار: 1.3.0 (الإصدار النهائي المحدث)
تاريخ البناء: {time.strftime('%Y-%m-%d %H:%M:%S')}
نوع البناء: Final Portable Package

🎯 التحسينات الجديدة في هذا الإصدار:
✅ حل مشاكل التعليق في استيراد ملفات Excel
✅ تحسين سرعة حفظ المعاملات والعمليات
✅ عمليات غير متزامنة لواجهة أكثر استجابة
✅ تحسينات قاعدة البيانات للأداء الأفضل
✅ معالجة محسنة للأخطاء والاستثناءات
✅ تنظيف تلقائي للذاكرة لمنع التسريبات
✅ واجهة مستخدم محسنة ومستقرة

📁 المحتويات:
- نظام_إدارة_المخازن.exe: الملف التنفيذي الرئيسي (محدث)
- تشغيل_البرنامج.bat: ملف تشغيل سريع
- data/: مجلد البيانات (فارغ للتوزيع)
- reports/: مجلد التقارير
- backups/: مجلد النسخ الاحتياطية
- logs/: مجلد السجلات
- _internal/: ملفات النظام (محدث)

🔐 بيانات الدخول الافتراضية:
اسم المستخدم: admin
كلمة المرور: admin

⚠️ مهم: يرجى تغيير كلمة المرور بعد أول تسجيل دخول

🚀 مميزات التطبيق:
- إدارة شاملة للمخزون والأصناف
- نظام معاملات متقدم مع تتبع كامل
- تقارير مفصلة وقابلة للتخصيص
- استيراد وتصدير البيانات من/إلى Excel
- نظام أمان متعدد المستويات
- نسخ احتياطي تلقائي
- واجهة عربية سهلة الاستخدام

💻 متطلبات التشغيل:
- Windows 10 أو أحدث
- 4 GB RAM أو أكثر
- 500 MB مساحة فارغة
- دقة شاشة 1024x768 أو أعلى

📝 ملاحظات مهمة:
- يمكن نقل هذا المجلد بالكامل إلى أي جهاز آخر
- لا يحتاج إلى تثبيت Python أو أي مكتبات إضافية
- جميع المتطلبات مدمجة في التطبيق
- البيانات محفوظة محلياً وآمنة
- يُنصح بعمل نسخة احتياطية دورية

🛠️ للدعم الفني:
- راجع ملف اقرأني.md للتعليمات التفصيلية
- تحقق من ملفات السجل في مجلد logs/
- استخدم وظيفة النسخ الاحتياطي المدمجة

© 2025 Desktop Stores Team - جميع الحقوق محفوظة
"""
    
    version_file = final_package_dir / "معلومات_الإصدار.txt"
    version_file.write_text(version_info, encoding='utf-8')
    print("✅ تم إنشاء ملف معلومات الإصدار")
    
    # إنشاء ملف تعليمات سريعة
    quick_guide = """🚀 دليل التشغيل السريع

1️⃣ تشغيل التطبيق:
   - انقر مرتين على "نظام_إدارة_المخازن.exe"
   - أو انقر على "تشغيل_البرنامج.bat"

2️⃣ تسجيل الدخول:
   - اسم المستخدم: admin
   - كلمة المرور: admin

3️⃣ الوظائف الرئيسية:
   📦 إدارة الأصناف: إضافة وتعديل الأصناف
   👥 إدارة المستفيدين: إضافة الجهات المستفيدة
   📊 المعاملات: إنشاء عمليات الصرف والاستلام
   📈 التقارير: عرض وطباعة التقارير
   💾 النسخ الاحتياطي: حفظ واستعادة البيانات

4️⃣ استيراد البيانات:
   - استخدم قائمة "ملف" > "استيراد من Excel"
   - تأكد من تنسيق الملف الصحيح
   - انتظر انتهاء العملية (لا تغلق التطبيق)

5️⃣ نصائح مهمة:
   ✅ غير كلمة المرور الافتراضية
   ✅ اعمل نسخة احتياطية دورية
   ✅ لا تحذف مجلد _internal
   ✅ احتفظ بنسخة من البيانات خارج التطبيق

للمساعدة الكاملة، راجع ملف "اقرأني.md"
"""
    
    guide_file = final_package_dir / "دليل_سريع.txt"
    guide_file.write_text(quick_guide, encoding='utf-8')
    print("✅ تم إنشاء الدليل السريع")
    
    # حساب حجم الحزمة
    total_size = sum(f.stat().st_size for f in final_package_dir.rglob('*') if f.is_file())
    size_mb = total_size / (1024 * 1024)
    
    print("\n" + "=" * 60)
    print("🎉 تم إنشاء الحزمة النهائية المحدثة بنجاح!")
    print(f"📁 المجلد: {final_package_dir}")
    print(f"📊 الحجم: {size_mb:.1f} MB")
    print(f"📄 الملف التنفيذي: نظام_إدارة_المخازن.exe")
    print("🚀 التطبيق جاهز للتوزيع مع جميع التحسينات")
    print("💡 تم حل جميع مشاكل الأداء والتعليق")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    create_final_updated_package()