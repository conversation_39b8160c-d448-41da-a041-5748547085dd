"""
مدير النسخ الاحتياطية - تطبيق إدارة المخازن
Backup Manager - Desktop Stores Management System
"""

import shutil
import zipfile
import json
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Optional
import threading
import time

from config import BACKUPS_DIR, DATABASE_PATH, BACKUP_CONFIG
from database import db_manager
from utils.logger import setup_logger

logger = setup_logger("BackupManager")

class BackupManager:
    """مدير النسخ الاحتياطية"""
    
    def __init__(self):
        self.backup_dir = BACKUPS_DIR
        self.database_path = DATABASE_PATH
        self.auto_backup_enabled = BACKUP_CONFIG.get("auto_backup", False)
        self.backup_interval = BACKUP_CONFIG.get("backup_interval", 24)  # ساعات
        self.max_backups = BACKUP_CONFIG.get("max_backups", 30)
        self.compress_backups = BACKUP_CONFIG.get("compress_backups", True)
        
        # إنشاء مجلد النسخ الاحتياطية
        self.backup_dir.mkdir(exist_ok=True)
        
        # بدء النسخ الاحتياطي التلقائي
        if self.auto_backup_enabled:
            self.start_auto_backup()
    
    def create_backup(self, backup_name: str = None, include_logs: bool = False) -> tuple[bool, str, str]:
        """
        إنشاء نسخة احتياطية
        Returns: (success, message, backup_path)
        """
        try:
            # إنشاء اسم النسخة الاحتياطية
            if not backup_name:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_name = f"backup_{timestamp}"
            
            # مسار النسخة الاحتياطية
            if self.compress_backups:
                backup_path = self.backup_dir / f"{backup_name}.zip"
            else:
                backup_path = self.backup_dir / f"{backup_name}.db"
            
            # إنشاء النسخة الاحتياطية
            if self.compress_backups:
                success = self._create_compressed_backup(backup_path, include_logs)
            else:
                success = self._create_simple_backup(backup_path)
            
            if success:
                # حفظ معلومات النسخة الاحتياطية
                self._save_backup_info(backup_name, backup_path, include_logs)
                
                # تنظيف النسخ القديمة
                self._cleanup_old_backups()
                
                logger.info(f"تم إنشاء نسخة احتياطية: {backup_path}")
                return True, "تم إنشاء النسخة الاحتياطية بنجاح", str(backup_path)
            else:
                return False, "فشل في إنشاء النسخة الاحتياطية", ""
                
        except Exception as e:
            logger.error(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
            return False, f"خطأ في إنشاء النسخة الاحتياطية: {e}", ""
    
    def _create_simple_backup(self, backup_path: Path) -> bool:
        """إنشاء نسخة احتياطية بسيطة"""
        try:
            # نسخ ملف قاعدة البيانات
            shutil.copy2(self.database_path, backup_path)
            return True
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء النسخة الاحتياطية البسيطة: {e}")
            return False
    
    def _create_compressed_backup(self, backup_path: Path, include_logs: bool = False) -> bool:
        """إنشاء نسخة احتياطية مضغوطة"""
        try:
            with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zip_file:
                # إضافة قاعدة البيانات
                if self.database_path.exists():
                    zip_file.write(self.database_path, "database.db")
                
                # إضافة ملفات السجلات (اختياري)
                if include_logs:
                    logs_dir = Path(__file__).parent.parent / "logs"
                    if logs_dir.exists():
                        for log_file in logs_dir.glob("*.log"):
                            zip_file.write(log_file, f"logs/{log_file.name}")
                
                # إضافة ملف معلومات النسخة الاحتياطية
                backup_info = {
                    "created_at": datetime.now().isoformat(),
                    "database_size": self.database_path.stat().st_size if self.database_path.exists() else 0,
                    "include_logs": include_logs,
                    "version": "1.0"
                }
                
                zip_file.writestr("backup_info.json", json.dumps(backup_info, indent=2, ensure_ascii=False))
            
            return True
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء النسخة الاحتياطية المضغوطة: {e}")
            return False
    
    def restore_backup(self, backup_path: str) -> tuple[bool, str]:
        """
        استعادة نسخة احتياطية
        Returns: (success, message)
        """
        try:
            backup_file = Path(backup_path)
            
            if not backup_file.exists():
                return False, "ملف النسخة الاحتياطية غير موجود"
            
            # إنشاء نسخة احتياطية من قاعدة البيانات الحالية قبل الاستعادة
            current_backup_name = f"before_restore_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            self.create_backup(current_backup_name)
            
            # استعادة النسخة الاحتياطية
            if backup_file.suffix == '.zip':
                success = self._restore_compressed_backup(backup_file)
            else:
                success = self._restore_simple_backup(backup_file)
            
            if success:
                logger.info(f"تم استعادة النسخة الاحتياطية: {backup_path}")
                return True, "تم استعادة النسخة الاحتياطية بنجاح"
            else:
                return False, "فشل في استعادة النسخة الاحتياطية"
                
        except Exception as e:
            logger.error(f"خطأ في استعادة النسخة الاحتياطية: {e}")
            return False, f"خطأ في استعادة النسخة الاحتياطية: {e}"
    
    def _restore_simple_backup(self, backup_path: Path) -> bool:
        """استعادة نسخة احتياطية بسيطة"""
        try:
            # إغلاق اتصال قاعدة البيانات
            db_manager.disconnect()
            
            # نسخ ملف النسخة الاحتياطية
            shutil.copy2(backup_path, self.database_path)
            
            # إعادة الاتصال بقاعدة البيانات
            db_manager.connect()
            
            return True
            
        except Exception as e:
            logger.error(f"خطأ في استعادة النسخة الاحتياطية البسيطة: {e}")
            return False
    
    def _restore_compressed_backup(self, backup_path: Path) -> bool:
        """استعادة نسخة احتياطية مضغوطة"""
        try:
            # إغلاق اتصال قاعدة البيانات
            db_manager.disconnect()
            
            with zipfile.ZipFile(backup_path, 'r') as zip_file:
                # استخراج قاعدة البيانات
                if "database.db" in zip_file.namelist():
                    zip_file.extract("database.db", self.backup_dir)
                    
                    # نسخ قاعدة البيانات المستخرجة
                    extracted_db = self.backup_dir / "database.db"
                    shutil.move(extracted_db, self.database_path)
                else:
                    return False
            
            # إعادة الاتصال بقاعدة البيانات
            db_manager.connect()
            
            return True
            
        except Exception as e:
            logger.error(f"خطأ في استعادة النسخة الاحتياطية المضغوطة: {e}")
            return False
    
    def get_backup_list(self) -> List[Dict]:
        """الحصول على قائمة النسخ الاحتياطية"""
        try:
            backups = []
            
            # البحث عن ملفات النسخ الاحتياطية
            for backup_file in self.backup_dir.glob("backup_*"):
                if backup_file.suffix in ['.db', '.zip']:
                    backup_info = {
                        "name": backup_file.stem,
                        "path": str(backup_file),
                        "size": backup_file.stat().st_size,
                        "created_at": datetime.fromtimestamp(backup_file.stat().st_mtime),
                        "type": "مضغوط" if backup_file.suffix == '.zip' else "بسيط"
                    }
                    
                    # محاولة قراءة معلومات إضافية من النسخة المضغوطة
                    if backup_file.suffix == '.zip':
                        try:
                            with zipfile.ZipFile(backup_file, 'r') as zip_file:
                                if "backup_info.json" in zip_file.namelist():
                                    info_data = json.loads(zip_file.read("backup_info.json"))
                                    backup_info.update(info_data)
                        except:
                            pass
                    
                    backups.append(backup_info)
            
            # ترتيب حسب تاريخ الإنشاء (الأحدث أولاً)
            backups.sort(key=lambda x: x["created_at"], reverse=True)
            
            return backups
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على قائمة النسخ الاحتياطية: {e}")
            return []
    
    def delete_backup(self, backup_path: str) -> tuple[bool, str]:
        """حذف نسخة احتياطية"""
        try:
            backup_file = Path(backup_path)
            
            if not backup_file.exists():
                return False, "ملف النسخة الاحتياطية غير موجود"
            
            backup_file.unlink()
            
            logger.info(f"تم حذف النسخة الاحتياطية: {backup_path}")
            return True, "تم حذف النسخة الاحتياطية بنجاح"
            
        except Exception as e:
            logger.error(f"خطأ في حذف النسخة الاحتياطية: {e}")
            return False, f"خطأ في حذف النسخة الاحتياطية: {e}"
    
    def _save_backup_info(self, backup_name: str, backup_path: Path, include_logs: bool):
        """حفظ معلومات النسخة الاحتياطية"""
        try:
            info_file = self.backup_dir / f"{backup_name}_info.json"
            
            backup_info = {
                "name": backup_name,
                "path": str(backup_path),
                "created_at": datetime.now().isoformat(),
                "size": backup_path.stat().st_size,
                "include_logs": include_logs,
                "database_size": self.database_path.stat().st_size if self.database_path.exists() else 0
            }
            
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(backup_info, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            logger.error(f"خطأ في حفظ معلومات النسخة الاحتياطية: {e}")
    
    def _cleanup_old_backups(self):
        """تنظيف النسخ الاحتياطية القديمة"""
        try:
            backups = self.get_backup_list()
            
            # حذف النسخ الزائدة عن الحد الأقصى
            if len(backups) > self.max_backups:
                old_backups = backups[self.max_backups:]
                
                for backup in old_backups:
                    try:
                        backup_file = Path(backup["path"])
                        if backup_file.exists():
                            backup_file.unlink()
                        
                        # حذف ملف المعلومات إذا كان موجوداً
                        info_file = self.backup_dir / f"{backup['name']}_info.json"
                        if info_file.exists():
                            info_file.unlink()
                            
                        logger.info(f"تم حذف النسخة الاحتياطية القديمة: {backup['name']}")
                        
                    except Exception as e:
                        logger.error(f"خطأ في حذف النسخة الاحتياطية القديمة {backup['name']}: {e}")
                        
        except Exception as e:
            logger.error(f"خطأ في تنظيف النسخ الاحتياطية القديمة: {e}")
    
    def start_auto_backup(self):
        """بدء النسخ الاحتياطي التلقائي"""
        def auto_backup_thread():
            while self.auto_backup_enabled:
                try:
                    # انتظار الفترة المحددة
                    time.sleep(self.backup_interval * 3600)  # تحويل الساعات إلى ثوان
                    
                    # إنشاء نسخة احتياطية تلقائية
                    if self.auto_backup_enabled:
                        backup_name = f"auto_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                        success, message, _ = self.create_backup(backup_name)
                        
                        if success:
                            logger.info("تم إنشاء نسخة احتياطية تلقائية")
                        else:
                            logger.error(f"فشل في إنشاء النسخة الاحتياطية التلقائية: {message}")
                            
                except Exception as e:
                    logger.error(f"خطأ في النسخ الاحتياطي التلقائي: {e}")
        
        # بدء خيط النسخ الاحتياطي التلقائي
        backup_thread = threading.Thread(target=auto_backup_thread, daemon=True)
        backup_thread.start()
        
        logger.info("تم بدء النسخ الاحتياطي التلقائي")
    
    def stop_auto_backup(self):
        """إيقاف النسخ الاحتياطي التلقائي"""
        self.auto_backup_enabled = False
        logger.info("تم إيقاف النسخ الاحتياطي التلقائي")
    
    def get_backup_statistics(self) -> Dict:
        """الحصول على إحصائيات النسخ الاحتياطية"""
        try:
            backups = self.get_backup_list()
            
            if not backups:
                return {
                    "total_backups": 0,
                    "total_size": 0,
                    "latest_backup": None,
                    "oldest_backup": None
                }
            
            total_size = sum(backup["size"] for backup in backups)
            latest_backup = backups[0]["created_at"]
            oldest_backup = backups[-1]["created_at"]
            
            return {
                "total_backups": len(backups),
                "total_size": total_size,
                "total_size_mb": round(total_size / 1024 / 1024, 2),
                "latest_backup": latest_backup,
                "oldest_backup": oldest_backup,
                "auto_backup_enabled": self.auto_backup_enabled,
                "backup_interval_hours": self.backup_interval
            }
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على إحصائيات النسخ الاحتياطية: {e}")
            return {}
