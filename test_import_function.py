#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار دالة استيراد Excel مباشرة
"""

import pandas as pd
import sys
import os
from datetime import datetime

# إضافة مسار المشروع
sys.path.append('e:/desktop_stores_app')

from models import AddedItem
from database import db_manager

def test_excel_import():
    """اختبار استيراد Excel مباشرة"""
    
    file_path = 'e:/desktop_stores_app/test_items.xlsx'
    
    if not os.path.exists(file_path):
        print("❌ ملف الاختبار غير موجود")
        return
    
    try:
        # قراءة ملف Excel
        df = pd.read_excel(file_path)
        print("📊 تم قراءة ملف Excel بنجاح")
        print(f"📊 عدد الصفوف: {len(df)}")
        print("📊 الأعمدة الموجودة:", list(df.columns))
        print("\n📊 البيانات المقروءة:")
        print(df.to_string(index=False))
        
        # التحقق من وجود الأعمدة المطلوبة
        required_columns = ['رقم الصنف', 'اسم الصنف', 'نوع العهدة', 'التصنيف', 'الوحدة', 'الكمية الحالية']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            print(f"❌ الأعمدة التالية مفقودة: {missing_columns}")
            return
        
        print("\n✅ جميع الأعمدة المطلوبة موجودة")
        
        # اختبار معالجة البيانات
        success_count = 0
        error_count = 0
        
        for index, row in df.iterrows():
            print(f"\n🔍 معالجة الصف {index + 1}:")
            print(f"   رقم الصنف: {row['رقم الصنف']}")
            print(f"   اسم الصنف: {row['اسم الصنف']}")
            print(f"   الكمية الحالية (خام): {row['الكمية الحالية']} (نوع: {type(row['الكمية الحالية'])})")
            
            # معالجة الكمية كما في الكود الأصلي
            current_qty_raw = row.get('الكمية الحالية', 0)
            try:
                if pd.isna(current_qty_raw) or current_qty_raw == '' or current_qty_raw is None:
                    current_qty = 0
                else:
                    # تحويل إلى رقم صحيح
                    current_qty = int(float(str(current_qty_raw).strip()))
                    if current_qty < 0:
                        current_qty = 0  # منع الكميات السالبة
            except (ValueError, TypeError):
                print(f"⚠️ تحذير: قيمة غير صحيحة للكمية في الصف {index + 2}: {current_qty_raw}, سيتم استخدام 0")
                current_qty = 0
            
            print(f"   الكمية المعالجة: {current_qty}")
            
            # التحقق من عدم وجود الصنف مسبقاً
            existing_item = AddedItem.get_by_item_number(str(row['رقم الصنف']))
            if existing_item:
                print(f"⚠️ الصنف {row['رقم الصنف']} موجود مسبقاً")
                error_count += 1
                continue
            
            # إنشاء الصنف الجديد
            new_item = AddedItem(
                item_number=str(row['رقم الصنف']),
                item_name=str(row['اسم الصنف']),
                custody_type=str(row.get('نوع العهدة', '')),
                classification=str(row.get('التصنيف', '')),
                unit=str(row.get('الوحدة', 'عدد')),
                current_quantity=current_qty,
                entered_quantity=current_qty,
                data_entry_user='admin',
                entry_date=datetime.now().strftime('%Y-%m-%d'),
                is_active=True
            )
            
            # حفظ الصنف
            if new_item.save():
                success_count += 1
                print(f"✅ تم حفظ الصنف بنجاح - ID: {new_item.id}")
                
                # التحقق من الحفظ
                saved_item = AddedItem.get_by_item_number(str(row['رقم الصنف']))
                if saved_item:
                    print(f"✅ تأكيد: الكمية المحفوظة = {saved_item.current_quantity}")
                else:
                    print("❌ خطأ: لم يتم العثور على الصنف بعد الحفظ")
            else:
                error_count += 1
                print(f"❌ فشل في حفظ الصنف")
        
        print(f"\n📊 نتائج الاختبار:")
        print(f"✅ نجح: {success_count}")
        print(f"❌ فشل: {error_count}")
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_excel_import()