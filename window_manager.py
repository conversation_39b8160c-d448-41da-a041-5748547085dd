"""
مدير النوافذ العالمي - تطبيق إدارة المخازن
Global Window Manager - Desktop Stores Management System
"""

import tkinter as tk
import logging
from typing import List, Optional

logger = logging.getLogger(__name__)

class WindowManager:
    """مدير النوافذ العالمي لتتبع وإدارة جميع النوافذ المفتوحة"""
    
    _instance = None
    _windows: List[tk.Toplevel] = []
    _main_window = None
    _login_window = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(WindowManager, cls).__new__(cls)
        return cls._instance
    
    @classmethod
    def register_window(cls, window: tk.Toplevel, window_type: str = "general"):
        """تسجيل نافذة جديدة"""
        try:
            if window not in cls._windows:
                cls._windows.append(window)
                window_title = window.title() if hasattr(window, 'title') else 'نافذة غير معروفة'
                logger.info(f"📝 تم تسجيل نافذة ({window_type}): {window_title}")
                
                # ربط حدث الإغلاق لإلغاء التسجيل التلقائي
                original_destroy = window.destroy
                def on_destroy():
                    cls.unregister_window(window)
                    original_destroy()
                window.destroy = on_destroy
                
        except Exception as e:
            logger.error(f"خطأ في تسجيل النافذة: {e}")
    
    @classmethod
    def unregister_window(cls, window: tk.Toplevel):
        """إلغاء تسجيل نافذة"""
        try:
            if window in cls._windows:
                cls._windows.remove(window)
                window_title = window.title() if hasattr(window, 'title') else 'نافذة غير معروفة'
                logger.info(f"❌ تم إلغاء تسجيل نافذة: {window_title}")
        except Exception as e:
            logger.debug(f"تحذير في إلغاء تسجيل النافذة: {e}")
    
    @classmethod
    def set_main_window(cls, window):
        """تعيين النافذة الرئيسية"""
        cls._main_window = window
        logger.info("🏠 تم تعيين النافذة الرئيسية")
    
    @classmethod
    def set_login_window(cls, window):
        """تعيين نافذة تسجيل الدخول"""
        cls._login_window = window
        logger.info("🔐 تم تعيين نافذة تسجيل الدخول")
    
    @classmethod
    def close_all_windows(cls, exclude_login: bool = True, exclude_main: bool = True) -> int:
        """إغلاق جميع النوافذ المسجلة"""
        closed_count = 0
        windows_to_close = cls._windows.copy()  # نسخة لتجنب تعديل القائمة أثناء التكرار
        
        for window in windows_to_close:
            try:
                # تجاهل النوافذ المستثناة
                if exclude_login and window == cls._login_window:
                    continue
                if exclude_main and window == cls._main_window:
                    continue
                
                # التحقق من وجود النافذة
                if window and hasattr(window, 'winfo_exists') and window.winfo_exists():
                    window_title = window.title() if hasattr(window, 'title') else 'نافذة غير معروفة'
                    window.destroy()
                    closed_count += 1
                    logger.info(f"✅ تم إغلاق النافذة: {window_title}")
                
            except Exception as e:
                logger.debug(f"تحذير في إغلاق النافذة: {e}")
        
        # تنظيف القائمة
        cls._windows = [w for w in cls._windows if w == cls._login_window or w == cls._main_window]
        
        logger.info(f"🎯 تم إغلاق {closed_count} نافذة مسجلة")
        return closed_count
    
    @classmethod
    def close_all_unregistered_windows(cls) -> int:
        """إغلاق جميع النوافذ غير المسجلة"""
        try:
            import tkinter as tk
            closed_count = 0
            
            # البحث عن جميع النوافذ في النظام
            if tk._default_root:
                all_toplevels = [widget for widget in tk._default_root.winfo_children() 
                               if isinstance(widget, tk.Toplevel)]
                
                for toplevel in all_toplevels:
                    try:
                        # تجاهل النوافذ المسجلة
                        if toplevel in cls._windows:
                            continue
                        
                        # تجاهل نوافذ تسجيل الدخول
                        window_title = toplevel.title().lower()
                        if ("تسجيل الدخول" in window_title or "login" in window_title):
                            continue
                        
                        # إغلاق النافذة
                        if toplevel.winfo_exists():
                            toplevel.destroy()
                            closed_count += 1
                            logger.info(f"✅ تم إغلاق نافذة غير مسجلة: {toplevel.title()}")
                    
                    except Exception as e:
                        logger.debug(f"تحذير في إغلاق نافذة غير مسجلة: {e}")
            
            logger.info(f"🎯 تم إغلاق {closed_count} نافذة غير مسجلة")
            return closed_count
            
        except Exception as e:
            logger.error(f"خطأ في إغلاق النوافذ غير المسجلة: {e}")
            return 0
    
    @classmethod
    def force_close_all_windows(cls) -> int:
        """إغلاق قسري لجميع النوافذ (للطوارئ)"""
        total_closed = 0
        
        # إغلاق النوافذ المسجلة
        registered_closed = cls.close_all_windows(exclude_login=True, exclude_main=False)
        total_closed += registered_closed
        
        # إغلاق النوافذ غير المسجلة
        unregistered_closed = cls.close_all_unregistered_windows()
        total_closed += unregistered_closed
        
        # محاولة أخيرة لإغلاق أي نوافذ متبقية
        try:
            import tkinter as tk
            if tk._default_root:
                remaining_windows = [widget for widget in tk._default_root.winfo_children() 
                                   if isinstance(widget, (tk.Toplevel, tk.Tk))]
                
                emergency_closed = 0
                for window in remaining_windows:
                    try:
                        window_title = window.title().lower()
                        if ("تسجيل الدخول" not in window_title and "login" not in window_title):
                            if window.winfo_exists():
                                window.destroy()
                                emergency_closed += 1
                    except:
                        pass
                
                total_closed += emergency_closed
                if emergency_closed > 0:
                    logger.info(f"🚨 إغلاق طارئ لـ {emergency_closed} نافذة إضافية")
        
        except Exception as e:
            logger.error(f"خطأ في الإغلاق القسري: {e}")
        
        logger.info(f"🎯 إجمالي النوافذ المُغلقة قسرياً: {total_closed}")
        return total_closed
    
    @classmethod
    def get_open_windows_count(cls) -> int:
        """الحصول على عدد النوافذ المفتوحة"""
        # تنظيف النوافذ المُغلقة من القائمة
        cls._windows = [w for w in cls._windows if w and hasattr(w, 'winfo_exists') and w.winfo_exists()]
        return len(cls._windows)
    
    @classmethod
    def get_open_windows_info(cls) -> List[str]:
        """الحصول على معلومات النوافذ المفتوحة"""
        windows_info = []
        for window in cls._windows:
            try:
                if window and hasattr(window, 'winfo_exists') and window.winfo_exists():
                    title = window.title() if hasattr(window, 'title') else 'نافذة غير معروفة'
                    windows_info.append(title)
            except:
                pass
        return windows_info
    
    @classmethod
    def cleanup(cls):
        """تنظيف مدير النوافذ"""
        cls._windows.clear()
        cls._main_window = None
        cls._login_window = None
        logger.info("🧹 تم تنظيف مدير النوافذ")

# إنشاء مثيل عالمي
window_manager = WindowManager()

# دوال مساعدة للاستخدام السهل
def register_window(window: tk.Toplevel, window_type: str = "general"):
    """تسجيل نافذة جديدة"""
    WindowManager.register_window(window, window_type)

def unregister_window(window: tk.Toplevel):
    """إلغاء تسجيل نافذة"""
    WindowManager.unregister_window(window)

def close_all_windows(exclude_login: bool = True) -> int:
    """إغلاق جميع النوافذ"""
    return WindowManager.force_close_all_windows()

def get_open_windows_count() -> int:
    """الحصول على عدد النوافذ المفتوحة"""
    return WindowManager.get_open_windows_count()

def get_open_windows_info() -> List[str]:
    """الحصول على معلومات النوافذ المفتوحة"""
    return WindowManager.get_open_windows_info()
