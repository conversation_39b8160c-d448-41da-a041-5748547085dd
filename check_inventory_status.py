#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import sys
import os

# إضافة مسار المشروع
sys.path.append('.')

try:
    from database import db_manager
    
    print('🔍 فحص حالة المخزون والحركات...')
    print('=' * 60)

    # فحص الأصناف المضافة
    try:
        items_count = db_manager.fetch_one('''
            SELECT COUNT(*) as count
            FROM added_items
            WHERE is_active = 1
        ''')
        
        print(f'📦 إجمالي الأصناف النشطة: {items_count["count"]}')
        
        # عرض آخر 10 أصناف
        recent_items = db_manager.fetch_all('''
            SELECT id, item_number, item_name, current_quantity, created_at
            FROM added_items
            WHERE is_active = 1
            ORDER BY id DESC
            LIMIT 10
        ''')
        
        print('\n📋 آخر 10 أصناف مضافة:')
        for item in recent_items:
            print(f'  🆔 {item["id"]} | 📦 {item["item_number"]} | 📝 {item["item_name"]} | 📊 {item["current_quantity"]}')
            
    except Exception as e:
        print(f'❌ خطأ في فحص الأصناف: {e}')

    # فحص حركات المخزون
    try:
        movements_count = db_manager.fetch_one('''
            SELECT COUNT(*) as count
            FROM inventory_movements_new
            WHERE is_active = 1
        ''')
        
        print(f'\n📦 إجمالي حركات المخزون النشطة: {movements_count["count"]}')
        
        # فحص حركات المخزون حسب النوع
        movement_types = db_manager.fetch_all('''
            SELECT movement_type, COUNT(*) as count
            FROM inventory_movements_new
            WHERE is_active = 1
            GROUP BY movement_type
            ORDER BY count DESC
        ''')
        
        print('\n📊 حركات المخزون حسب النوع:')
        for mov_type in movement_types:
            print(f'  🔄 {mov_type["movement_type"]}: {mov_type["count"]} حركة')
            
        # عرض آخر 15 حركة
        recent_movements = db_manager.fetch_all('''
            SELECT im.id, im.item_number, im.movement_type, im.quantity, 
                   im.movement_date, im.organization_name, im.notes,
                   ai.item_name
            FROM inventory_movements_new im
            LEFT JOIN added_items ai ON im.item_number = ai.item_number
            WHERE im.is_active = 1
            ORDER BY im.id DESC
            LIMIT 15
        ''')
        
        print(f'\n📋 آخر 15 حركة مخزون:')
        for mov in recent_movements:
            print(f'  🆔 {mov["id"]} | 📦 {mov["item_number"]} ({mov["item_name"]}) | 🔄 {mov["movement_type"]} | 📊 {mov["quantity"]} | 📅 {mov["movement_date"]}')
            
    except Exception as e:
        print(f'❌ خطأ في فحص حركات المخزون: {e}')

    # فحص الفجوات في معرفات الأصناف
    try:
        print('\n🔍 فحص معرفات الأصناف:')
        print('=' * 40)
        
        min_max_ids = db_manager.fetch_one('''
            SELECT MIN(id) as min_id, MAX(id) as max_id, COUNT(*) as count
            FROM added_items
            WHERE is_active = 1
        ''')
        
        print(f'📊 أصغر معرف: {min_max_ids["min_id"]}')
        print(f'📊 أكبر معرف: {min_max_ids["max_id"]}')
        print(f'📊 عدد الأصناف: {min_max_ids["count"]}')
        
        expected_range = min_max_ids["max_id"] - min_max_ids["min_id"] + 1
        missing_count = expected_range - min_max_ids["count"]
        
        if missing_count > 0:
            print(f'⚠️ يوجد {missing_count} معرف مفقود في النطاق')
            
            # البحث عن المعرفات المفقودة
            missing_ids = db_manager.fetch_all('''
                WITH RECURSIVE series(x) AS (
                    SELECT (SELECT MIN(id) FROM added_items WHERE is_active = 1)
                    UNION ALL
                    SELECT x+1 FROM series
                    WHERE x < (SELECT MAX(id) FROM added_items WHERE is_active = 1)
                )
                SELECT x as missing_id
                FROM series
                WHERE x NOT IN (SELECT id FROM added_items WHERE is_active = 1)
                LIMIT 10
            ''')
            
            if missing_ids:
                print('🔍 بعض المعرفات المفقودة:')
                for missing in missing_ids:
                    print(f'  ❌ معرف مفقود: {missing["missing_id"]}')
        else:
            print('✅ لا توجد فجوات في المعرفات')
            
    except Exception as e:
        print(f'❌ خطأ في فحص معرفات الأصناف: {e}')

    # فحص آخر معرف في sqlite_sequence
    try:
        sequence_info = db_manager.fetch_one('''
            SELECT seq
            FROM sqlite_sequence
            WHERE name = 'added_items'
        ''')
        
        if sequence_info:
            print(f'\n📊 آخر معرف في التسلسل: {sequence_info["seq"]}')
        else:
            print('\n❌ لم يتم العثور على معلومات التسلسل')
            
    except Exception as e:
        print(f'❌ خطأ في فحص التسلسل: {e}')

except Exception as e:
    print(f'❌ خطأ عام: {e}')
    import traceback
    traceback.print_exc()
