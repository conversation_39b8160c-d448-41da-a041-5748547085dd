"""
نافذة الإعدادات المتقدمة للجدول التنظيمي
"""

import tkinter as tk
from tkinter import ttk, messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from models import OrganizationalChart
from database import db_manager


class OrganizationalChartAdvancedWindow:
    """نافذة الإعدادات المتقدمة للجدول التنظيمي"""
    
    def __init__(self, parent, main_window):
        self.parent = parent
        self.main_window = main_window
        self.window = None
        self.statistics = {}
        
        self.setup_window()
        self.load_statistics()
    
    def setup_window(self):
        """إعداد النافذة"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("⚙️ الإعدادات المتقدمة - الجدول التنظيمي")
        self.window.geometry("900x600")
        self.window.resizable(False, False)
        
        # توسيط النافذة
        self.center_window()
        
        # إعداد المحتوى
        self.setup_content()
        
        # جعل النافذة في المقدمة
        self.window.lift()
        self.window.focus_force()
        self.window.grab_set()  # جعل النافذة modal
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        x = (screen_width - 900) // 2
        y = (screen_height - 600) // 2
        self.window.geometry(f"900x600+{x}+{y}")
    
    def setup_content(self):
        """إعداد محتوى النافذة"""
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.window)
        main_frame.pack(fill=BOTH, expand=True, padx=20, pady=20)
        
        # شريط العنوان
        self.create_header(main_frame)
        
        # المحتوى الرئيسي
        content_frame = ttk_bs.Frame(main_frame)
        content_frame.pack(fill=BOTH, expand=True, pady=(20, 0))
        
        # قسم الإحصائيات (يمين)
        self.create_statistics_section(content_frame)
        
        # قسم حذف البيانات (يسار)
        self.create_data_management_section(content_frame)
        
        # أزرار الإغلاق
        self.create_buttons(main_frame)
    
    def create_header(self, parent):
        """إنشاء شريط العنوان"""
        header_frame = ttk_bs.Frame(parent)
        header_frame.pack(fill=X, pady=(0, 20))
        
        # العنوان
        title_label = ttk_bs.Label(
            header_frame,
            text="⚙️ الإعدادات المتقدمة",
            bootstyle="primary"
        )
        title_label.pack()
        
        # وصف
        desc_label = ttk_bs.Label(
            header_frame,
            text="إدارة البيانات والإحصائيات للجدول التنظيمي",
            bootstyle="secondary"
        )
        desc_label.pack(pady=(5, 0))
    
    def create_statistics_section(self, parent):
        """إنشاء قسم الإحصائيات"""
        # إطار الإحصائيات (يمين)
        stats_frame = ttk_bs.LabelFrame(
            parent, 
            text="📊 إحصائيات النظام", 
            padding=20,
            bootstyle="info"
        )
        stats_frame.pack(side=RIGHT, fill=BOTH, expand=True, padx=(10, 0))
        
        # عدد الأصناف
        self.create_stat_item(stats_frame, "عدد الأصناف", "active_items", "📦")
        
        # عدد حركات المخزون
        self.create_stat_item(stats_frame, "عدد حركات المخزون", "inventory_movements", "📈")
        
        # عدد الأصناف المرتبطة بعمليات صرف
        self.create_stat_item(stats_frame, "عدد الأصناف المرتبطة بعمليات صرف", "linked_items", "🔗")
        
        # زر تحديث الإحصائيات
        refresh_btn = ttk_bs.Button(
            stats_frame,
            text="🔄 تحديث الإحصائيات",
            command=self.refresh_statistics,
            bootstyle="info",
            width=22
        )
        refresh_btn.pack(pady=(20, 0))
    
    def create_stat_item(self, parent, label, key, icon):
        """إنشاء عنصر إحصائي"""
        item_frame = ttk_bs.Frame(parent)
        item_frame.pack(fill=X, pady=(0, 15))
        
        # الأيقونة والنص
        label_frame = ttk_bs.Frame(item_frame)
        label_frame.pack(side=LEFT, fill=X, expand=True)
        
        ttk_bs.Label(
            label_frame,
            text=f"{icon} {label}:"
        ).pack(anchor=W)
        
        # القيمة
        value_label = ttk_bs.Label(
            item_frame,
            text="0",
            bootstyle="primary"
        )
        value_label.pack(side=RIGHT)
        
        # حفظ مرجع للتحديث لاحقاً
        setattr(self, f"{key}_label", value_label)
    
    def create_data_management_section(self, parent):
        """إنشاء قسم إدارة البيانات"""
        # إطار حذف البيانات (يسار)
        data_frame = ttk_bs.LabelFrame(
            parent, 
            text="🗑️ حذف البيانات", 
            padding=20,
            bootstyle="danger"
        )
        data_frame.pack(side=LEFT, fill=BOTH, expand=True, padx=(0, 10))
        
        # إعادة تعيين المخزون
        reset_section = ttk_bs.Frame(data_frame)
        reset_section.pack(fill=X, pady=(0, 20))
        
        ttk_bs.Label(
            reset_section,
            text="إعادة تعيين المخزون",
            bootstyle="danger"
        ).pack(anchor=W)
        
        ttk_bs.Label(
            reset_section,
            text="هذا الإجراء سيقوم بحذف جميع حركات المخزون وإعادة تعيين كميات الأصناف إلى صفر مع\nالمحافظة ببيانات الأصناف نفسها.",
            justify=RIGHT,
            wraplength=300
        ).pack(anchor=W, pady=(5, 10))
        
        reset_btn = ttk_bs.Button(
            reset_section,
            text="🔄 إعادة تعيين المخزون",
            command=self.reset_inventory,
            bootstyle="outline-warning",
            width=25
        )
        reset_btn.pack()
        
        ttk_bs.Label(
            reset_section,
            text="لا يمكن تعيين المخزون إذا هناك أصناف مرتبطة بعمليات صرف",
            bootstyle="danger",
            justify=RIGHT,
            wraplength=300
        ).pack(anchor=W, pady=(5, 0))
        
        # حذف جميع الأصناف
        delete_section = ttk_bs.Frame(data_frame)
        delete_section.pack(fill=X, pady=(20, 0))
        
        ttk_bs.Label(
            delete_section,
            text="حذف جميع الأصناف",
            bootstyle="danger"
        ).pack(anchor=W)
        
        ttk_bs.Label(
            delete_section,
            text="هذا الإجراء سيقوم بحذف جميع الأصناف وحركات المخزون المرتبطة بها.",
            justify=RIGHT,
            wraplength=300
        ).pack(anchor=W, pady=(5, 10))
        
        delete_btn = ttk_bs.Button(
            delete_section,
            text="🗑️ حذف جميع الأصناف",
            command=self.delete_all_items,
            bootstyle="danger",
            width=22
        )
        delete_btn.pack()
        
        ttk_bs.Label(
            delete_section,
            text="لا يمكن حذف الأصناف إذا هناك أصناف مرتبطة بعمليات صرف",
            bootstyle="danger",
            justify=RIGHT,
            wraplength=300
        ).pack(anchor=W, pady=(5, 0))
    
    def create_buttons(self, parent):
        """إنشاء أزرار الإغلاق"""
        buttons_frame = ttk_bs.Frame(parent)
        buttons_frame.pack(fill=X, pady=(20, 0))
        
        # زر الإغلاق
        close_btn = ttk_bs.Button(
            buttons_frame,
            text="❌ إغلاق",
            command=self.window.destroy,
            bootstyle="secondary",
            width=18
        )
        close_btn.pack(side=RIGHT)
    
    def load_statistics(self):
        """تحميل الإحصائيات"""
        try:
            # إحصائيات الجدول التنظيمي
            self.statistics = OrganizationalChart.get_statistics()
            
            # إحصائيات إضافية
            # عدد حركات المخزون
            result = db_manager.fetch_one("SELECT COUNT(*) FROM inventory_movements")
            self.statistics['inventory_movements'] = result[0] if result else 0
            
            # عدد الأصناف المرتبطة بعمليات صرف
            result = db_manager.fetch_one("""
                SELECT COUNT(DISTINCT oc.id) 
                FROM organizational_chart oc
                INNER JOIN transaction_items ti ON oc.id = ti.item_id
                WHERE oc.is_active = 1
            """)
            self.statistics['linked_items'] = result[0] if result else 0
            
            # تحديث العرض
            self.update_statistics_display()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل الإحصائيات: {e}")
    
    def update_statistics_display(self):
        """تحديث عرض الإحصائيات"""
        try:
            # تحديث عدد الأصناف
            if hasattr(self, 'active_items_label'):
                self.active_items_label.config(text=str(self.statistics.get('active_items', 0)))
            
            # تحديث عدد حركات المخزون
            if hasattr(self, 'inventory_movements_label'):
                self.inventory_movements_label.config(text=str(self.statistics.get('inventory_movements', 0)))
            
            # تحديث عدد الأصناف المرتبطة
            if hasattr(self, 'linked_items_label'):
                self.linked_items_label.config(text=str(self.statistics.get('linked_items', 0)))
                
        except Exception as e:
            print(f"خطأ في تحديث عرض الإحصائيات: {e}")
    
    def refresh_statistics(self):
        """تحديث الإحصائيات"""
        self.load_statistics()
        messagebox.showinfo("تم", "تم تحديث الإحصائيات بنجاح")
    
    def reset_inventory(self):
        """إعادة تعيين المخزون"""
        # التحقق من وجود أصناف مرتبطة بعمليات صرف
        if self.statistics.get('linked_items', 0) > 0:
            messagebox.showerror(
                "خطأ", 
                "لا يمكن إعادة تعيين المخزون لأن هناك أصناف مرتبطة بعمليات صرف.\n"
                "يرجى حذف عمليات الصرف أولاً."
            )
            return
        
        # تأكيد العملية
        if messagebox.askyesno(
            "تأكيد إعادة التعيين",
            "هل تريد إعادة تعيين جميع كميات المخزون إلى صفر؟\n"
            "سيتم حذف جميع حركات المخزون.\n"
            "هذا الإجراء لا يمكن التراجع عنه."
        ):
            try:
                # حذف حركات المخزون
                db_manager.execute_query("DELETE FROM inventory_movements")
                
                # إعادة تعيين الكميات
                db_manager.execute_query(
                    "UPDATE organizational_chart SET quantity = 0, updated_at = CURRENT_TIMESTAMP"
                )
                
                messagebox.showinfo("نجح", "تم إعادة تعيين المخزون بنجاح")
                
                # تحديث الإحصائيات
                self.refresh_statistics()
                
                # تحديث الشاشة الرئيسية
                if hasattr(self.main_window, 'refresh_data'):
                    self.main_window.refresh_data()
                    
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في إعادة تعيين المخزون: {e}")
    
    def delete_all_items(self):
        """حذف جميع الأصناف"""
        # التحقق من وجود أصناف مرتبطة بعمليات صرف
        if self.statistics.get('linked_items', 0) > 0:
            messagebox.showerror(
                "خطأ", 
                "لا يمكن حذف الأصناف لأن هناك أصناف مرتبطة بعمليات صرف.\n"
                "يرجى حذف عمليات الصرف أولاً."
            )
            return
        
        # تأكيد العملية
        if messagebox.askyesno(
            "تأكيد الحذف",
            "هل تريد حذف جميع أصناف الجدول التنظيمي؟\n"
            "سيتم حذف جميع الأصناف وحركات المخزون المرتبطة بها.\n"
            "هذا الإجراء لا يمكن التراجع عنه."
        ):
            try:
                # حذف حركات المخزون أولاً
                db_manager.execute_query("DELETE FROM inventory_movements")
                
                # حذف جميع الأصناف
                if OrganizationalChart.delete_all():
                    messagebox.showinfo("نجح", "تم حذف جميع الأصناف بنجاح")
                    
                    # تحديث الإحصائيات
                    self.refresh_statistics()
                    
                    # تحديث الشاشة الرئيسية
                    if hasattr(self.main_window, 'refresh_data'):
                        self.main_window.refresh_data()
                else:
                    messagebox.showerror("خطأ", "فشل في حذف الأصناف")
                    
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حذف الأصناف: {e}")
