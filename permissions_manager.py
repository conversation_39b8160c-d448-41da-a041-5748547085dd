#!/usr/bin/env python3
"""
مدير الصلاحيات - نظام التحكم في الوصول
Permissions Manager - Access Control System
"""

from typing import Optional, Dict, List
from models import User
from database import db_manager
from utils.logger import setup_logger

logger = setup_logger()

class PermissionsManager:
    """مدير الصلاحيات"""
    
    # تعريف الوحدات والصلاحيات
    MODULES = {
        'inventory': 'إدارة المخزون',
        'transactions': 'المعاملات',
        'beneficiaries': 'المستفيدين',
        'reports': 'التقارير',
        'users': 'إدارة المستخدمين',
        'settings': 'الإعدادات',
        'departments': 'الإدارات والأقسام'
    }
    
    PERMISSIONS = {
        'view': 'عرض',
        'add': 'إضافة',
        'edit': 'تعديل',
        'delete': 'حذف',
        'print': 'طباعة'
    }
    
    # صلاحيات الأدوار الافتراضية
    DEFAULT_ROLE_PERMISSIONS = {
        'admin': {
            # مدير النظام - صلاحيات كاملة
            'inventory': ['view', 'add', 'edit', 'delete', 'print'],
            'transactions': ['view', 'add', 'edit', 'delete', 'print'],
            'beneficiaries': ['view', 'add', 'edit', 'delete', 'print'],
            'reports': ['view', 'add', 'edit', 'delete', 'print'],
            'users': ['view', 'add', 'edit', 'delete', 'print'],
            'settings': ['view', 'add', 'edit', 'delete', 'print'],
            'departments': ['view', 'add', 'edit', 'delete', 'print']
        },
        'manager': {
            # مدير قسم - صلاحيات إدارية محدودة
            'inventory': ['view', 'add', 'edit', 'print'],
            'transactions': ['view', 'add', 'edit', 'print'],
            'beneficiaries': ['view', 'add', 'edit', 'print'],
            'reports': ['view', 'print'],
            'users': [],  # لا يمكن إدارة المستخدمين
            'settings': ['view'],  # عرض فقط
            'departments': ['view', 'edit']
        },
        'user': {
            # مستخدم عادي - صلاحيات محدودة
            'inventory': ['view'],
            'transactions': ['view', 'add'],
            'beneficiaries': ['view', 'add'],
            'reports': ['view'],
            'users': [],  # لا يمكن إدارة المستخدمين
            'settings': [],  # لا يمكن الوصول للإعدادات
            'departments': ['view']
        },
        'viewer': {
            # مشاهد - عرض فقط
            'inventory': ['view'],
            'transactions': ['view'],
            'beneficiaries': ['view'],
            'reports': ['view'],
            'users': [],  # لا يمكن إدارة المستخدمين
            'settings': [],  # لا يمكن الوصول للإعدادات
            'departments': ['view']
        }
    }
    
    @staticmethod
    def get_user_role(user: User) -> str:
        """الحصول على دور المستخدم"""
        try:
            if user.is_admin:
                return 'admin'
            
            # البحث في جدول user_roles
            query = """
                SELECT r.name FROM roles r
                JOIN user_roles ur ON r.id = ur.role_id
                WHERE ur.user_id = ?
                LIMIT 1
            """
            
            result = db_manager.fetch_one(query, (user.id,))
            if result:
                return result['name']
            
            # افتراضي: مستخدم عادي
            return 'user'
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على دور المستخدم: {e}")
            return 'viewer'  # أقل الصلاحيات في حالة الخطأ
    
    @staticmethod
    def has_permission(user: User, module: str, permission: str) -> bool:
        """التحقق من صلاحية المستخدم"""
        try:
            if not user or not user.is_active:
                return False
            
            # المدير له جميع الصلاحيات
            if user.is_admin:
                return True
            
            # الحصول على دور المستخدم
            user_role = PermissionsManager.get_user_role(user)
            
            # التحقق من الصلاحيات الافتراضية
            role_permissions = PermissionsManager.DEFAULT_ROLE_PERMISSIONS.get(user_role, {})
            module_permissions = role_permissions.get(module, [])
            
            return permission in module_permissions
            
        except Exception as e:
            logger.error(f"خطأ في التحقق من الصلاحيات: {e}")
            return False
    
    @staticmethod
    def get_user_permissions(user: User) -> Dict[str, List[str]]:
        """الحصول على جميع صلاحيات المستخدم"""
        try:
            if not user or not user.is_active:
                return {}
            
            # المدير له جميع الصلاحيات
            if user.is_admin:
                return PermissionsManager.DEFAULT_ROLE_PERMISSIONS['admin']
            
            # الحصول على دور المستخدم
            user_role = PermissionsManager.get_user_role(user)
            
            return PermissionsManager.DEFAULT_ROLE_PERMISSIONS.get(user_role, {})
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على صلاحيات المستخدم: {e}")
            return {}
    
    @staticmethod
    def can_access_module(user: User, module: str) -> bool:
        """التحقق من إمكانية الوصول للوحدة"""
        return PermissionsManager.has_permission(user, module, 'view')
    
    @staticmethod
    def get_role_description(role_name: str) -> str:
        """الحصول على وصف الدور"""
        descriptions = {
            'admin': 'مدير النظام - صلاحيات كاملة',
            'manager': 'مدير قسم - صلاحيات إدارية',
            'user': 'مستخدم عادي - صلاحيات محدودة',
            'viewer': 'مشاهد - عرض فقط'
        }
        return descriptions.get(role_name, 'غير محدد')
    
    @staticmethod
    def get_permission_summary(user: User) -> str:
        """الحصول على ملخص صلاحيات المستخدم"""
        try:
            if not user or not user.is_active:
                return "المستخدم غير نشط"
            
            if user.is_admin:
                return "مدير النظام - صلاحيات كاملة على جميع الوحدات"
            
            user_role = PermissionsManager.get_user_role(user)
            role_desc = PermissionsManager.get_role_description(user_role)
            
            permissions = PermissionsManager.get_user_permissions(user)
            
            summary = f"{role_desc}\n\nالصلاحيات:\n"
            
            for module, perms in permissions.items():
                if perms:
                    module_name = PermissionsManager.MODULES.get(module, module)
                    perm_names = [PermissionsManager.PERMISSIONS.get(p, p) for p in perms]
                    summary += f"• {module_name}: {', '.join(perm_names)}\n"
            
            return summary
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء ملخص الصلاحيات: {e}")
            return "خطأ في تحديد الصلاحيات"

# دالة مساعدة للاستخدام السريع
def check_permission(user: User, module: str, permission: str) -> bool:
    """دالة مساعدة للتحقق من الصلاحيات"""
    return PermissionsManager.has_permission(user, module, permission)

def can_access(user: User, module: str) -> bool:
    """دالة مساعدة للتحقق من إمكانية الوصول"""
    return PermissionsManager.can_access_module(user, module)