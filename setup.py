#!/usr/bin/env python3
"""
إعداد التطبيق لإنشاء ملف exe
Setup script for creating executable file
"""

import sys
from cx_Freeze import setup, Executable
from pathlib import Path
import os

# معلومات التطبيق
APP_NAME = "نظام إدارة المخازن"
APP_VERSION = "1.2.0"
APP_DESCRIPTION = "نظام إدارة المخازن والمستودعات - Desktop Stores Management System"
APP_AUTHOR = "Desktop Stores Team"

# المجلد الحالي
current_dir = Path(__file__).parent

# الملفات المطلوبة
include_files = [
    # ملفات الإعدادات
    ("config.py", "config.py"),
    ("settings.json", "settings.json"),
    
    # مجلدات البيانات
    ("ui/", "ui/"),
    ("utils/", "utils/"),
    ("assets/", "assets/"),
    
    # ملفات قاعدة البيانات
    ("models.py", "models.py"),
    ("database.py", "database.py"),
    ("auth_manager.py", "auth_manager.py"),
    
    # ملفات مساعدة
    ("font_manager.py", "font_manager.py"),
    ("performance_optimizer.py", "performance_optimizer.py"),
    ("safe_window_manager.py", "safe_window_manager.py"),
    ("permissions_manager.py", "permissions_manager.py"),
    ("activity_monitor.py", "activity_monitor.py"),
    ("window_manager.py", "window_manager.py"),
]

# إضافة الملفات الموجودة فقط
filtered_include_files = []
for src, dst in include_files:
    src_path = current_dir / src
    if src_path.exists():
        filtered_include_files.append((str(src_path), dst))
        print(f"✅ سيتم تضمين: {src}")
    else:
        print(f"⚠️ ملف غير موجود: {src}")

# المكتبات المطلوبة
packages = [
    "tkinter",
    "ttkbootstrap", 
    "sqlite3",
    "bcrypt",
    "PIL",
    "pandas",
    "openpyxl",
    "reportlab",
    "matplotlib",
    "numpy",
    "psutil",
    "pyperclip",
    "threading",
    "queue",
    "concurrent.futures",
    "pathlib",
    "json",
    "datetime",
    "os",
    "sys",
    "logging",
    "hashlib",
    "base64",
    "uuid",
    "re",
    "shutil",
    "subprocess",
    "webbrowser"
]

# الوحدات المطلوبة
includes = [
    "tkinter.ttk",
    "tkinter.messagebox",
    "tkinter.filedialog",
    "tkinter.font",
    "sqlite3",
    "bcrypt",
    "PIL.Image",
    "PIL.ImageTk",
    "pandas",
    "openpyxl",
    "reportlab.pdfgen",
    "reportlab.lib",
    "matplotlib.pyplot",
    "numpy",
    "psutil",
    "pyperclip"
]

# الوحدات المستبعدة
excludes = [
    "test",
    "tests", 
    "unittest",
    "pdb",
    "doctest",
    "difflib",
    "inspect",
    "pydoc"
]

# خيارات البناء
build_exe_options = {
    "packages": packages,
    "includes": includes,
    "excludes": excludes,
    "include_files": filtered_include_files,
    "optimize": 2,
    "build_exe": "dist/Desktop_Stores_Management"
}

# إعداد الملف التنفيذي
base = None
if sys.platform == "win32":
    base = "Win32GUI"  # إخفاء نافذة الكونسول

executable = Executable(
    script="main.py",
    base=base,
    target_name="نظام_إدارة_المخازن.exe",
    icon="assets/icons/app_icon.ico" if (current_dir / "assets" / "icons" / "app_icon.ico").exists() else None,
    copyright=f"© 2025 {APP_AUTHOR}",
    trademarks=APP_DESCRIPTION
)

# إعداد التطبيق
setup(
    name=APP_NAME,
    version=APP_VERSION,
    description=APP_DESCRIPTION,
    author=APP_AUTHOR,
    options={"build_exe": build_exe_options},
    executables=[executable]
)

print("\n" + "="*60)
print("🎯 تم إنشاء ملف exe بنجاح!")
print("📁 المجلد: dist/Desktop_Stores_Management/")
print("📄 الملف: نظام_إدارة_المخازن.exe")
print("="*60)