#!/usr/bin/env python3
"""
شاشة تعديل حركة مخزون
Edit Inventory Movement Window
"""

import tkinter as tk
from tkinter import ttk, messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from datetime import datetime

from config import APP_CONFIG, UI_CONFIG, get_message
from models import AddedItem, Department, Section, InventoryMovement
from database import db_manager
from utils.window_utils import quick_center, setup_modal_window

class EditInventoryMovementWindow:
    """شاشة تعديل حركة مخزون"""
    
    def __init__(self, parent, main_window, movement_id):
        self.parent = parent
        self.main_window = main_window
        self.movement_id = movement_id
        self.movement_window = None
        self.movement_data = None
        
        # متغيرات النموذج
        self.item_var = tk.StringVar()
        self.movement_type_var = tk.StringVar()
        self.quantity_var = tk.StringVar()
        self.organization_var = tk.StringVar()
        
        # تحميل بيانات الحركة
        self.load_movement_data()
        
        if self.movement_data:
            self.setup_window()
        else:
            messagebox.showerror("خطأ", "لم يتم العثور على بيانات الحركة")
    
    def load_movement_data(self):
        """تحميل بيانات الحركة من قاعدة البيانات"""
        try:
            query = """
                SELECT im.*, ai.item_name
                FROM inventory_movements_new im
                LEFT JOIN added_items ai ON im.item_number = ai.item_number
                WHERE im.id = ?
            """
            result = db_manager.fetch_one(query, (self.movement_id,))
            
            if result:
                self.movement_data = result
                # تعيين القيم الافتراضية
                self.item_var.set(f"{result['item_number']} - {result['item_name'] or 'غير محدد'}")
                self.movement_type_var.set(result['movement_type'])
                self.quantity_var.set(str(int(result['quantity'])))  # تحويل إلى عدد صحيح
                
                # تكوين نص الهيئة/الإدارة/القسم
                org_text = ""
                if result['organization_type'] and result['organization_name']:
                    org_text = f"{result['organization_type']}: {result['organization_name']}"
                self.organization_var.set(org_text)
                
        except Exception as e:
            print(f"خطأ في تحميل بيانات الحركة: {e}")
            self.movement_data = None
    
    def setup_window(self):
        """إعداد النافذة"""
        self.movement_window = tk.Toplevel(self.parent)

        # إعداد النافذة المنبثقة مع التوسيط
        setup_modal_window(
            self.movement_window,
            self.parent,
            "✏️ تعديل حركة مخزون",
            600,
            400  # ارتفاع أقل لأننا أزلنا حقل الملاحظات
        )

        # إعداد المحتوى
        self.setup_content()

        # توسيط نهائي مع جعل النافذة في المقدمة
        quick_center(self.movement_window, 600, 400)
    
    def setup_content(self):
        """إعداد محتوى النافذة"""
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.movement_window)
        main_frame.pack(fill=BOTH, expand=True, padx=20, pady=20)
        
        # العنوان
        title_label = ttk_bs.Label(
            main_frame,
            text="✏️ تعديل حركة مخزون",
            bootstyle="primary",
            anchor=CENTER
        )
        title_label.pack(pady=(0, 20))
        
        # إطار النموذج
        form_frame = ttk_bs.LabelFrame(main_frame, text="بيانات الحركة", bootstyle="info")
        form_frame.pack(fill=BOTH, expand=True, pady=(0, 20))
        
        # الصنف (للقراءة فقط)
        item_frame = ttk_bs.Frame(form_frame)
        item_frame.pack(fill=X, padx=20, pady=10)
        
        ttk_bs.Label(item_frame, text="الصنف", width=20).pack(side=RIGHT, padx=(0, 10))
        
        item_entry = ttk_bs.Entry(
            item_frame,
            textvariable=self.item_var,
            state="readonly",
            width=40
        )
        item_entry.pack(side=RIGHT)
        
        # نوع الحركة
        movement_type_frame = ttk_bs.Frame(form_frame)
        movement_type_frame.pack(fill=X, padx=20, pady=10)
        
        ttk_bs.Label(movement_type_frame, text="نوع الحركة:", width=15).pack(side=RIGHT, padx=(0, 10))
        
        movement_type_combo = ttk_bs.Combobox(
            movement_type_frame,
            textvariable=self.movement_type_var,
            values=["إضافة", "صرف"],
            state="readonly",
            width=37
        )
        movement_type_combo.pack(side=RIGHT)
        
        # الكمية
        quantity_frame = ttk_bs.Frame(form_frame)
        quantity_frame.pack(fill=X, padx=20, pady=10)
        
        ttk_bs.Label(quantity_frame, text="الكمية:", width=15).pack(side=RIGHT, padx=(0, 10))
        
        quantity_entry = ttk_bs.Entry(
            quantity_frame,
            textvariable=self.quantity_var,
            width=20
        )
        quantity_entry.pack(side=RIGHT)
        
        # الهيئة/الإدارة/القسم
        org_frame = ttk_bs.Frame(form_frame)
        org_frame.pack(fill=X, padx=20, pady=10)
        
        ttk_bs.Label(org_frame, text="الهيئة/الإدارة/القسم:", width=15).pack(side=RIGHT, padx=(0, 10))
        
        # زر إضافة هيئة جديدة
        add_org_btn = ttk_bs.Button(
            org_frame,
            text="+",
            command=self.open_add_organization_window,
            bootstyle="success",
            width=15
        )
        add_org_btn.pack(side=RIGHT, padx=(5, 0))
        
        org_combo = ttk_bs.Combobox(
            org_frame,
            textvariable=self.organization_var,
            state="readonly",
            width=35
        )
        org_combo.pack(side=RIGHT, fill=X, expand=True)
        self.load_organizations(org_combo)
        
        # أزرار العمليات
        buttons_frame = ttk_bs.Frame(main_frame)
        buttons_frame.pack(fill=X, pady=(10, 0))
        
        # زر الحفظ
        save_btn = ttk_bs.Button(
            buttons_frame,
            text="💾 حفظ",
            command=self.save_movement,
            bootstyle="success",
            width=15
        )
        save_btn.pack(side=RIGHT, padx=(10, 0))
        
        # زر الإلغاء
        cancel_btn = ttk_bs.Button(
            buttons_frame,
            text="❌ إلغاء",
            command=self.close_window,
            bootstyle="secondary",
            width=15
        )
        cancel_btn.pack(side=RIGHT)
        
        # ربط مفتاح Enter بالحفظ
        self.movement_window.bind('<Return>', lambda e: self.save_movement())
        self.movement_window.bind('<Escape>', lambda e: self.close_window())
    
    def load_organizations(self, combo):
        """تحميل قائمة الهيئات والإدارات والأقسام"""
        try:
            organizations = []
            
            # تحميل الوحدات
            units = Unit.get_all()
            for unit in units:
                organizations.append(f"وحدة: {unit.name}")
            
            # تحميل الإدارات
            departments = Department.get_all()
            for dept in departments:
                organizations.append(f"إدارة: {dept.name}")
            
            # تحميل الأقسام
            sections = Section.get_all()
            for section in sections:
                organizations.append(f"قسم: {section.name}")
            
            combo['values'] = organizations
            
        except Exception as e:
            print(f"خطأ في تحميل الهيئات: {e}")
            combo['values'] = []
    
    def open_add_organization_window(self):
        """فتح شاشة إضافة هيئة/إدارة/قسم جديد"""
        try:
            from ui.add_organization_window import AddOrganizationWindow
            AddOrganizationWindow(self.movement_window, self)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح شاشة إضافة الهيئة: {e}")

    def refresh_organizations_combo(self):
        """تحديث قائمة الهيئات المنسدلة"""
        try:
            # البحث عن combobox الهيئات وتحديثه
            for widget in self.movement_window.winfo_children():
                self.find_and_update_combo(widget)
        except Exception as e:
            print(f"خطأ في تحديث قائمة الهيئات: {e}")

    def find_and_update_combo(self, widget):
        """البحث عن وتحديث قائمة الهيئات المنسدلة"""
        try:
            if isinstance(widget, ttk_bs.Combobox) and hasattr(widget, 'get'):
                # تحديث القائمة
                self.load_organizations(widget)

            # البحث في الأطفال
            for child in widget.winfo_children():
                self.find_and_update_combo(child)
        except:
            pass
    
    def save_movement(self):
        """حفظ تعديلات الحركة"""
        try:
            # التحقق من صحة البيانات
            if not self.quantity_var.get().strip():
                messagebox.showerror("خطأ", "يرجى إدخال الكمية")
                return
            
            try:
                quantity = int(self.quantity_var.get())
                if quantity <= 0:
                    messagebox.showerror("خطأ", "يجب أن تكون الكمية أكبر من صفر")
                    return
            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال كمية صحيحة (عدد صحيح)")
                return
            
            # تحليل نوع وأسم الهيئة
            org_text = self.organization_var.get()
            org_type = ""
            org_name = ""
            
            if org_text and ":" in org_text:
                parts = org_text.split(":", 1)
                org_type = parts[0].strip()
                org_name = parts[1].strip()
            
            # تحديث الحركة في قاعدة البيانات
            query = """
                UPDATE inventory_movements_new SET
                movement_type = ?, quantity = ?, organization_type = ?,
                organization_name = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """
            
            if db_manager.execute_query(query, (
                self.movement_type_var.get(),
                quantity,
                org_type,
                org_name,
                self.movement_id
            )):
                # رسالة نجاح
                messagebox.showinfo("نجح", "تم تحديث حركة المخزون بنجاح")
                
                # تحديث الشاشات المفتوحة
                self.refresh_parent_windows()
                
                # إغلاق النافذة
                self.close_window()
            else:
                messagebox.showerror("خطأ", "فشل في تحديث حركة المخزون")
                
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ التعديلات: {e}")
            print(f"خطأ في الحفظ: {e}")
            import traceback
            traceback.print_exc()
    
    def refresh_parent_windows(self):
        """تحديث النوافذ الأصلية"""
        try:
            # تحديث النافذة الرئيسية إذا كانت متوفرة
            if self.main_window and hasattr(self.main_window, 'refresh_all_data'):
                self.main_window.refresh_all_data()
            
            # تحديث شاشات المخزون المفتوحة
            from ui.add_inventory_movement_window import refresh_all_registered_windows
            refresh_all_registered_windows()
            
        except Exception as e:
            print(f"خطأ في تحديث النوافذ: {e}")
    
    def close_window(self):
        """إغلاق النافذة"""
        self.movement_window.destroy()
