#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح مطابقة الأصناف مع الجدول التنظيمي عند الاستيراد
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models import OrganizationalChart, AddedItem
from database import db_manager
import pandas as pd
import tempfile

def test_organizational_chart_validation():
    """اختبار التحقق من وجود الأصناف في الجدول التنظيمي"""
    print("🧪 اختبار التحقق من وجود الأصناف في الجدول التنظيمي...")
    
    try:
        # تنظيف البيانات الموجودة
        print("🗑️ تنظيف البيانات الموجودة...")
        db_manager.execute_query("UPDATE organizational_chart SET is_active = 0")
        db_manager.execute_query("UPDATE added_items SET is_active = 0")
        
        # إضافة أصناف في الجدول التنظيمي
        print("➕ إضافة أصناف في الجدول التنظيمي...")
        org_items = [
            {"item_code": "ORG001", "item_name": "صنف منظم 1", "quantity": 100},
            {"item_code": "ORG002", "item_name": "صنف منظم 2", "quantity": 200},
            {"item_code": "ORG003", "item_name": "صنف منظم 3", "quantity": 300},
        ]
        
        for item_data in org_items:
            org_item = OrganizationalChart(
                sequence_number=OrganizationalChart.get_next_sequence_number(),
                item_code=item_data["item_code"],
                item_name=item_data["item_name"],
                quantity=item_data["quantity"],
                is_active=True
            )
            org_item.save()
            print(f"   ✅ تم إضافة: {item_data['item_name']} ({item_data['item_code']})")
        
        # اختبار دالة get_by_item_code
        print("\n🔍 اختبار دالة البحث برقم الصنف...")
        found_item = OrganizationalChart.get_by_item_code("ORG001")
        if found_item and found_item.item_name == "صنف منظم 1":
            print("✅ دالة البحث برقم الصنف تعمل بشكل صحيح")
        else:
            print("❌ دالة البحث برقم الصنف لا تعمل")
            return False
        
        # اختبار البحث بصنف غير موجود
        not_found_item = OrganizationalChart.get_by_item_code("NOTFOUND")
        if not_found_item is None:
            print("✅ البحث بصنف غير موجود يعطي None بشكل صحيح")
        else:
            print("❌ البحث بصنف غير موجود لا يعطي None")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التحقق من الجدول التنظيمي: {e}")
        return False

def test_excel_import_with_validation():
    """اختبار استيراد Excel مع التحقق من الجدول التنظيمي"""
    print("\n🧪 اختبار استيراد Excel مع التحقق من الجدول التنظيمي...")
    
    try:
        # إنشاء ملف Excel اختبار
        test_data = {
            'اسم الصنف': [
                'صنف منظم 1',      # موجود في الجدول التنظيمي
                'صنف منظم 2',      # موجود في الجدول التنظيمي  
                'صنف غير منظم',    # غير موجود في الجدول التنظيمي
                'صنف منظم 3'       # موجود في الجدول التنظيمي
            ],
            'رقم الصنف': ['ORG001', 'ORG002', 'NOTORG', 'ORG003'],
            'الكمية': [50, 75, 25, 100],
            'نوع العهدة': ['عهدة شخصية', 'عهدة إدارية', 'عهدة شخصية', 'عهدة إدارية'],
            'التصنيف': ['أ', 'ب', 'ج', 'أ'],
            'الوحدة': ['قطعة', 'كيلو', 'متر', 'قطعة']
        }
        
        df = pd.DataFrame(test_data)
        
        # حفظ في ملف مؤقت
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
            df.to_excel(tmp_file.name, index=False)
            temp_file_path = tmp_file.name
        
        print(f"📁 تم إنشاء ملف Excel اختبار: {temp_file_path}")
        
        # تنظيف جدول الأصناف المضافة
        db_manager.execute_query("UPDATE added_items SET is_active = 0")
        
        # استيراد البيانات
        print("📥 بدء استيراد البيانات...")
        from utils.excel_import_manager import ExcelImportManager
        
        result = ExcelImportManager.import_items_from_excel(temp_file_path)
        
        print(f"\n📊 نتائج الاستيراد:")
        print(f"   ✅ نجح: {result.success_count}")
        print(f"   🔄 مكرر: {result.duplicate_count}")
        print(f"   ❌ أخطاء: {result.error_count}")
        
        if result.errors:
            print("   📝 تفاصيل الأخطاء:")
            for error in result.errors:
                print(f"      • {error}")
        
        # التحقق من النتائج المتوقعة
        # يجب أن ينجح 3 أصناف (الموجودة في الجدول التنظيمي)
        # ويفشل 1 صنف (غير موجود في الجدول التنظيمي)
        
        expected_success = 3
        expected_errors = 1
        
        if result.success_count == expected_success and result.error_count == expected_errors:
            print("✅ النتائج متطابقة مع المتوقع")
            
            # التحقق من الأصناف المضافة
            added_items = AddedItem.get_all()
            print(f"\n📋 الأصناف المضافة ({len(added_items)}):")
            for item in added_items:
                print(f"   • {item.item_name} ({item.item_number})")
            
            # التحقق من أن الأصناف المضافة موجودة في الجدول التنظيمي
            all_valid = True
            for item in added_items:
                org_item = OrganizationalChart.get_by_item_code(item.item_number)
                if not org_item:
                    print(f"❌ الصنف {item.item_name} غير موجود في الجدول التنظيمي!")
                    all_valid = False
            
            if all_valid:
                print("✅ جميع الأصناف المضافة موجودة في الجدول التنظيمي")
                return True
            else:
                print("❌ بعض الأصناف المضافة غير موجودة في الجدول التنظيمي")
                return False
        else:
            print(f"❌ النتائج غير متطابقة - متوقع: {expected_success} نجح، {expected_errors} خطأ")
            print(f"   فعلي: {result.success_count} نجح، {result.error_count} خطأ")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار استيراد Excel: {e}")
        return False
    finally:
        # حذف الملف المؤقت
        try:
            os.unlink(temp_file_path)
        except:
            pass

def main():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء اختبارات إصلاح مطابقة الأصناف مع الجدول التنظيمي")
    print("=" * 70)
    
    tests = [
        ("اختبار التحقق من الجدول التنظيمي", test_organizational_chart_validation),
        ("اختبار استيراد Excel مع التحقق", test_excel_import_with_validation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                print(f"✅ {test_name}: نجح")
                passed += 1
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {e}")
    
    print("\n" + "="*70)
    print(f"📊 النتائج النهائية: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت!")
        print("✅ إصلاح مطابقة الأصناف مع الجدول التنظيمي يعمل بشكل صحيح")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت")
        return False

if __name__ == "__main__":
    main()
