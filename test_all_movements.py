#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import sys
import os

# إضافة مسار المشروع
sys.path.append('.')

try:
    from database import db_manager
    
    print('🔍 اختبار تسجيل جميع أنواع حركات المخزون...')
    print('=' * 60)

    # فحص أنواع الحركات الموجودة حالياً
    try:
        movement_types = db_manager.fetch_all('''
            SELECT movement_type, COUNT(*) as count
            FROM inventory_movements_new
            WHERE is_active = 1
            GROUP BY movement_type
            ORDER BY count DESC
        ''')
        
        print('📊 أنواع الحركات الموجودة حالياً:')
        total_movements = 0
        for mov_type in movement_types:
            print(f'  🔄 {mov_type["movement_type"]}: {mov_type["count"]} حركة')
            total_movements += mov_type["count"]
        
        print(f'\n📦 إجمالي حركات المخزون: {total_movements}')
        
    except Exception as e:
        print(f'❌ خطأ في فحص أنواع الحركات: {e}')

    # فحص آخر 10 حركات
    try:
        print('\n📋 آخر 10 حركات في النظام:')
        print('-' * 80)
        
        recent_movements = db_manager.fetch_all('''
            SELECT im.id, im.item_number, im.movement_type, im.quantity,
                   im.organization_type, im.organization_name, im.notes,
                   im.movement_date,
                   COALESCE(u.full_name, u.username, 'غير محدد') as user_name,
                   ai.item_name
            FROM inventory_movements_new im
            LEFT JOIN users u ON im.user_id = u.id
            LEFT JOIN added_items ai ON im.item_number = ai.item_number
            WHERE im.is_active = 1
            ORDER BY im.movement_date DESC, im.id DESC
            LIMIT 10
        ''')
        
        for i, movement in enumerate(recent_movements, 1):
            print(f'{i:2d}. 📦 {movement["item_number"]} | {movement["item_name"] or "غير محدد"}')
            print(f'    🔄 {movement["movement_type"]} | كمية: {movement["quantity"]}')
            print(f'    👤 {movement["user_name"]} | 📅 {movement["movement_date"]}')
            if movement["notes"]:
                print(f'    📝 {movement["notes"]}')
            print()
            
    except Exception as e:
        print(f'❌ خطأ في عرض آخر الحركات: {e}')

    # فحص التطابق بين الأصناف وحركات المخزون
    try:
        print('\n🔍 فحص التطابق بين الأصناف وحركات المخزون:')
        print('-' * 50)
        
        items_count = db_manager.fetch_one('''
            SELECT COUNT(*) as count
            FROM added_items
            WHERE is_active = 1
        ''')
        
        movements_count = db_manager.fetch_one('''
            SELECT COUNT(*) as count
            FROM inventory_movements_new
            WHERE is_active = 1
        ''')
        
        unique_items_in_movements = db_manager.fetch_one('''
            SELECT COUNT(DISTINCT item_number) as count
            FROM inventory_movements_new
            WHERE is_active = 1
        ''')
        
        print(f'📦 إجمالي الأصناف النشطة: {items_count["count"]}')
        print(f'📦 إجمالي حركات المخزون: {movements_count["count"]}')
        print(f'📦 أصناف لها حركات: {unique_items_in_movements["count"]}')
        
        if items_count["count"] == unique_items_in_movements["count"]:
            print('✅ ممتاز! جميع الأصناف لها حركات مخزون')
        else:
            missing_count = items_count["count"] - unique_items_in_movements["count"]
            print(f'⚠️ يوجد {missing_count} صنف بدون حركات مخزون')
            
    except Exception as e:
        print(f'❌ خطأ في فحص التطابق: {e}')

    # فحص الأصناف التي لها أكثر من حركة واحدة
    try:
        print('\n📊 الأصناف التي لها حركات متعددة:')
        print('-' * 40)
        
        items_with_multiple_movements = db_manager.fetch_all('''
            SELECT item_number, COUNT(*) as movement_count,
                   ai.item_name
            FROM inventory_movements_new im
            LEFT JOIN added_items ai ON im.item_number = ai.item_number
            WHERE im.is_active = 1
            GROUP BY item_number
            HAVING COUNT(*) > 1
            ORDER BY movement_count DESC
            LIMIT 10
        ''')
        
        if items_with_multiple_movements:
            print('📦 أصناف لها حركات متعددة (أول 10):')
            for item in items_with_multiple_movements:
                print(f'  📦 {item["item_number"]}: {item["item_name"] or "غير محدد"} ({item["movement_count"]} حركة)')
        else:
            print('📦 جميع الأصناف لها حركة واحدة فقط')
            
    except Exception as e:
        print(f'❌ خطأ في فحص الحركات المتعددة: {e}')

    # إحصائيات شاملة
    try:
        print('\n📈 إحصائيات شاملة:')
        print('=' * 30)
        
        # إحصائيات حسب نوع المنظمة
        org_stats = db_manager.fetch_all('''
            SELECT organization_type, COUNT(*) as count
            FROM inventory_movements_new
            WHERE is_active = 1
            GROUP BY organization_type
            ORDER BY count DESC
        ''')
        
        print('📊 حركات حسب نوع المنظمة:')
        for org in org_stats:
            print(f'  🏢 {org["organization_type"]}: {org["count"]} حركة')
        
        # إحصائيات حسب المستخدم
        user_stats = db_manager.fetch_all('''
            SELECT COALESCE(u.full_name, u.username, 'غير محدد') as user_name,
                   COUNT(*) as count
            FROM inventory_movements_new im
            LEFT JOIN users u ON im.user_id = u.id
            WHERE im.is_active = 1
            GROUP BY im.user_id
            ORDER BY count DESC
        ''')
        
        print('\n📊 حركات حسب المستخدم:')
        for user in user_stats:
            print(f'  👤 {user["user_name"]}: {user["count"]} حركة')
            
    except Exception as e:
        print(f'❌ خطأ في الإحصائيات الشاملة: {e}')

    print('\n🎉 انتهى اختبار تسجيل حركات المخزون!')
    print('✅ النظام جاهز لتسجيل جميع أنواع العمليات: إضافة، تعديل، صرف، حذف')

except Exception as e:
    print(f'❌ خطأ عام: {e}')
    import traceback
    traceback.print_exc()
