#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import sys
import os

# إضافة مسار المشروع
sys.path.append('.')

try:
    from database import db_manager
    
    print('🔍 البحث عن عمليات الصرف التجريبية...')
    print('=' * 50)

    # البحث في جدول العمليات
    operations = db_manager.fetch_all('''
        SELECT o.id, o.operation_number, o.beneficiary_name, o.operation_date, o.created_at,
               COUNT(oi.id) as items_count
        FROM operations o
        LEFT JOIN operation_items oi ON o.id = oi.operation_id
        WHERE o.beneficiary_name LIKE '%تجرب%' OR o.beneficiary_name LIKE '%test%' 
           OR o.beneficiary_name LIKE '%خالد%'
        GROUP BY o.id
        ORDER BY o.created_at DESC
    ''')

    if operations:
        print(f'📋 تم العثور على {len(operations)} عملية صرف:')
        for op in operations:
            print(f'  🆔 معرف العملية: {op["id"]}')
            print(f'  📄 رقم العملية: {op["operation_number"]}')
            print(f'  👤 اسم المستفيد: {op["beneficiary_name"]}')
            print(f'  📅 تاريخ العملية: {op["operation_date"]}')
            print(f'  📦 عدد الأصناف: {op["items_count"]}')
            print(f'  ⏰ تاريخ الإنشاء: {op["created_at"]}')
            print('-' * 30)
    else:
        print('❌ لم يتم العثور على عمليات صرف تجريبية')

    # البحث في جدول حركات المخزون
    movements = db_manager.fetch_all('''
        SELECT im.id, im.movement_date, im.item_number, im.movement_type, 
               im.quantity, im.organization_name, im.notes,
               ai.item_name
        FROM inventory_movements_new im
        LEFT JOIN added_items ai ON im.item_number = ai.item_number
        WHERE im.organization_name LIKE '%خالد%' OR im.notes LIKE '%تجرب%'
           OR im.notes LIKE '%test%'
        ORDER BY im.movement_date DESC
    ''')

    if movements:
        print(f'📦 تم العثور على {len(movements)} حركة مخزون تجريبية:')
        for mov in movements:
            print(f'  🆔 معرف الحركة: {mov["id"]}')
            print(f'  📅 التاريخ: {mov["movement_date"]}')
            print(f'  📦 رقم الصنف: {mov["item_number"]} - {mov["item_name"]}')
            print(f'  🔄 نوع الحركة: {mov["movement_type"]}')
            print(f'  📊 الكمية: {mov["quantity"]}')
            print(f'  🏢 الجهة: {mov["organization_name"]}')
            print(f'  📝 الملاحظات: {mov["notes"]}')
            print('-' * 30)
    else:
        print('❌ لم يتم العثور على حركات مخزون تجريبية')

    # البحث عن آخر العمليات
    print('\n🕐 آخر 5 عمليات صرف:')
    print('=' * 50)
    recent_operations = db_manager.fetch_all('''
        SELECT o.id, o.operation_number, o.beneficiary_name, o.operation_date, o.created_at
        FROM operations o
        ORDER BY o.created_at DESC
        LIMIT 5
    ''')
    
    for op in recent_operations:
        print(f'  🆔 {op["id"]} | 📄 {op["operation_number"]} | 👤 {op["beneficiary_name"]} | 📅 {op["operation_date"]}')

except Exception as e:
    print(f'❌ خطأ: {e}')
    import traceback
    traceback.print_exc()
