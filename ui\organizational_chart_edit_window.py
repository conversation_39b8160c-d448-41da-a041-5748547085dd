"""
نافذة تعديل صنف في الجدول التنظيمي
"""

import tkinter as tk
from tkinter import ttk, messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from models import OrganizationalChart
from ui.success_message import AutoSuccessMessage


class OrganizationalChartEditWindow:
    """نافذة تعديل صنف في الجدول التنظيمي"""

    def __init__(self, parent, main_window, item_id):
        self.parent = parent
        self.main_window = main_window
        self.item_id = item_id
        self.window = None
        self.item_data = None

        # متغيرات النموذج
        self.sequence_var = tk.IntVar()
        self.item_code_var = tk.StringVar()
        self.item_name_var = tk.StringVar()
        self.unit_var = tk.StringVar()

        # تحميل بيانات العنصر
        self.load_item_data()
        
        if self.item_data:
            self.create_window()
        else:
            messagebox.showerror("خطأ", "فشل في تحميل بيانات العنصر")

    def load_item_data(self):
        """تحميل بيانات العنصر المراد تعديله"""
        try:
            self.item_data = OrganizationalChart.get_by_id(self.item_id)
            if self.item_data:
                # تعبئة المتغيرات بالبيانات الحالية
                self.sequence_var.set(self.item_data.sequence_number or 0)
                self.item_code_var.set(self.item_data.item_code or "")
                self.item_name_var.set(self.item_data.item_name or "")
                self.unit_var.set(self.item_data.unit or "")
        except Exception as e:
            print(f"خطأ في تحميل بيانات العنصر: {e}")
            self.item_data = None

    def create_window(self):
        """إنشاء النافذة"""
        # إنشاء النافذة
        self.window = tk.Toplevel(self.parent)
        self.window.title("تعديل صنف في الجدول التنظيمي")
        self.window.geometry("900x600")  # زيادة الارتفاع لإظهار جميع العناصر
        self.window.resizable(False, False)

        # توسيط النافذة
        self.window.update_idletasks()
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        x = (screen_width - 900) // 2
        y = (screen_height - 600) // 2
        self.window.geometry(f"900x600+{x}+{y}")

        # جعل النافذة في المقدمة
        self.window.lift()
        self.window.focus_force()
        self.window.grab_set()

        # ربط مفتاح Enter للحفظ
        self.window.bind('<Return>', lambda event: self.save_changes())
        self.window.bind('<Escape>', lambda event: self.cancel_edit())

        # إنشاء المحتوى
        self.create_content()

    def create_content(self):
        """إنشاء محتوى النافذة"""
        # العنوان الرئيسي
        title_label = tk.Label(
            self.window,
            text="تعديل صنف في الجدول التنظيمي",
            font=("Arial", 16, "bold"),
            fg="blue"
        )
        title_label.pack(pady=20)

        # الإطار الرئيسي للحقول
        main_frame = tk.Frame(self.window)
        main_frame.pack(padx=50, pady=10)

        # إطار للحقول
        fields_frame = tk.Frame(main_frame)
        fields_frame.pack()

        # الرقم التسلسلي
        tk.Label(fields_frame, text="الرقم التسلسلي", font=("Arial", 12)).pack(anchor="e", pady=(0, 5))
        sequence_entry = tk.Entry(
            fields_frame,
            textvariable=self.sequence_var,
            width=45,
            justify="center",
            font=("Arial", 11)
        )
        sequence_entry.pack(pady=(0, 15))

        # رقم الصنف
        tk.Label(fields_frame, text="رقم الصنف *", font=("Arial", 12)).pack(anchor="e", pady=(0, 5))
        item_code_entry = tk.Entry(
            fields_frame,
            textvariable=self.item_code_var,
            width=45,
            justify="center",
            font=("Arial", 11)
        )
        item_code_entry.pack(pady=(0, 15))

        # اسم الصنف
        tk.Label(fields_frame, text="اسم الصنف *", font=("Arial", 12)).pack(anchor="e", pady=(0, 5))
        item_name_entry = tk.Entry(
            fields_frame,
            textvariable=self.item_name_var,
            width=45,
            justify="center",
            font=("Arial", 11)
        )
        item_name_entry.pack(pady=(0, 15))

        # اسم المعدة
        tk.Label(fields_frame, text="اسم المعدة", font=("Arial", 12)).pack(anchor="e", pady=(0, 5))
        unit_entry = tk.Entry(
            fields_frame,
            textvariable=self.unit_var,
            width=45,
            justify="center",
            font=("Arial", 11)
        )
        unit_entry.pack(pady=(0, 15))

        # ملاحظة الحقول المطلوبة
        note_frame = tk.Frame(self.window)
        note_frame.pack(pady=5)
        tk.Label(note_frame, text="* الحقول المطلوبة", font=("Arial", 10), fg="red").pack()

        # رسالة إرشادية للحفظ
        instruction_frame = tk.Frame(self.window)
        instruction_frame.pack(pady=10)
        instruction_label = tk.Label(
            instruction_frame,
            text="لإتمام عملية حفظ التعديل والتحديث\nاضغط Enter",
            font=("Arial", 12, "bold"),
            fg="green",
            justify="center"
        )
        instruction_label.pack()

        # إطار الأزرار في الأسفل
        buttons_frame = tk.Frame(self.window)
        buttons_frame.pack(pady=30)

        # زر الحفظ
        save_btn = tk.Button(
            buttons_frame,
            text="حفظ التعديلات",
            command=self.save_changes,
            bg="green",
            fg="white",
            font=("Arial", 12, "bold"),
            width=20,
            height=2
        )
        save_btn.pack(side="left", padx=10)

        # زر الإلغاء
        cancel_btn = tk.Button(
            buttons_frame,
            text="إلغاء",
            command=self.cancel_edit,
            bg="red",
            fg="white",
            font=("Arial", 12, "bold"),
            width=20,
            height=2
        )
        cancel_btn.pack(side="left", padx=10)

        # ربط مفاتيح الاختصار
        self.window.bind('<Return>', lambda e: self.save_changes())
        self.window.bind('<Escape>', lambda e: self.cancel_edit())

    def save_changes(self):
        """حفظ التعديلات"""
        try:
            # التحقق من صحة البيانات
            item_code = self.item_code_var.get().strip()
            item_name = self.item_name_var.get().strip()

            if not item_code:
                messagebox.showerror("خطأ", "يرجى إدخال رقم الصنف")
                return

            if not item_name:
                messagebox.showerror("خطأ", "يرجى إدخال اسم الصنف")
                return

            # التحقق من عدم تكرار رقم الصنف (إلا للعنصر الحالي)
            if OrganizationalChart.is_item_code_exists(item_code, self.item_id):
                messagebox.showerror("خطأ", f"رقم الصنف '{item_code}' موجود مسبقاً")
                return

            # تحديث بيانات العنصر
            self.item_data.sequence_number = self.sequence_var.get()
            self.item_data.item_code = item_code
            self.item_data.item_name = item_name
            self.item_data.unit = self.unit_var.get().strip() if self.unit_var.get().strip() else None

            # حفظ التعديلات
            if self.item_data.save():
                # تحديث الشاشة الرئيسية
                if self.main_window and hasattr(self.main_window, 'refresh_data'):
                    self.main_window.refresh_data()

                # رسالة نجاح تلقائية تختفي خلال 3 ثواني
                success_message = f"تم تحديث الصنف بنجاح\n\nاسم الصنف: {item_name}\nرقم الصنف: {item_code}"

                AutoSuccessMessage.show(
                    self.main_window.window if self.main_window else self.parent,
                    success_message,
                    duration=3000
                )

                # إغلاق نافذة التعديل فقط (الجدول التنظيمي يبقى مفتوح)
                self.window.destroy()
            else:
                messagebox.showerror("خطأ", "فشل في حفظ التعديلات")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ التعديلات: {e}")
            print(f"خطأ في الحفظ: {e}")
            import traceback
            traceback.print_exc()

    def cancel_edit(self):
        """إلغاء التعديل"""
        if messagebox.askyesno("تأكيد", "هل تريد إلغاء التعديل؟"):
            self.window.destroy()


# اختبار الشاشة
if __name__ == "__main__":
    import sys
    from pathlib import Path
    
    # إضافة مسار المشروع
    project_root = Path(__file__).parent.parent
    sys.path.insert(0, str(project_root))
    
    try:
        # إعداد قاعدة البيانات
        from database import db_manager
        db_manager.init_database()
        
        # إنشاء نافذة اختبار
        root = tk.Tk()
        root.title("اختبار شاشة تعديل صنف")
        root.geometry("300x200")
        
        def open_edit_window():
            # استخدام معرف عنصر موجود للاختبار
            edit_window = OrganizationalChartEditWindow(root, None, 1)
        
        tk.Button(
            root,
            text="فتح شاشة تعديل صنف",
            command=open_edit_window,
            bg="blue",
            fg="white",
            width=25,
            height=3
        ).pack(expand=True)
        
        root.mainloop()
        
    except Exception as e:
        print(f"خطأ: {e}")
        import traceback
        traceback.print_exc()
