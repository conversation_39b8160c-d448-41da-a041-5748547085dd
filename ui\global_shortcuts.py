#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام مفاتيح الاختصار العامة - تطبيق إدارة المخازن
Global Shortcuts System - Desktop Stores Management System
"""

import tkinter as tk
from tkinter import messagebox
import pyperclip


class GlobalShortcuts:
    """مدير مفاتيح الاختصار العامة"""
    
    def __init__(self, window, context_handler=None):
        """
        تهيئة مفاتيح الاختصار العامة
        
        Args:
            window: النافذة المراد ربط مفاتيح الاختصار بها
            context_handler: معالج السياق للعمليات المخصصة
        """
        self.window = window
        self.context_handler = context_handler
        self.clipboard_data = None
        
        # ربط مفاتيح الاختصار
        self.bind_shortcuts()
    
    def bind_shortcuts(self):
        """ربط مفاتيح الاختصار بالنافذة"""
        # F1 للحفظ
        self.window.bind('<F1>', self.handle_save)
        
        # F2 للحذف
        self.window.bind('<F2>', self.handle_delete)
        
        # F3 للنسخ
        self.window.bind('<F3>', self.handle_copy)
        
        # F4 للصق
        self.window.bind('<F4>', self.handle_paste)
        
        # التأكد من أن النافذة تستقبل الأحداث
        self.window.focus_set()
    
    def handle_save(self, event=None):
        """معالج مفتاح F1 - الحفظ"""
        try:
            if self.context_handler and hasattr(self.context_handler, 'save_action'):
                self.context_handler.save_action()
            elif hasattr(self.window, 'save_data'):
                self.window.save_data()
            elif hasattr(self.window, 'save_changes'):
                self.window.save_changes()
            elif hasattr(self.window, 'save'):
                self.window.save()
            else:
                # البحث عن أزرار الحفظ في النافذة
                self._find_and_click_button(['حفظ', 'save', 'إضافة', 'تحديث'])
        except Exception as e:
            print(f"خطأ في عملية الحفظ: {e}")
        
        return "break"  # منع انتشار الحدث
    
    def handle_delete(self, event=None):
        """معالج مفتاح F2 - الحذف"""
        try:
            if self.context_handler and hasattr(self.context_handler, 'delete_action'):
                self.context_handler.delete_action()
            elif hasattr(self.window, 'delete_selected'):
                self.window.delete_selected()
            elif hasattr(self.window, 'delete_item'):
                self.window.delete_item()
            elif hasattr(self.window, 'delete'):
                self.window.delete()
            else:
                # البحث عن أزرار الحذف في النافذة
                self._find_and_click_button(['حذف', 'delete', 'إزالة'])
        except Exception as e:
            print(f"خطأ في عملية الحذف: {e}")
        
        return "break"
    
    def handle_copy(self, event=None):
        """معالج مفتاح F3 - النسخ"""
        try:
            if self.context_handler and hasattr(self.context_handler, 'copy_action'):
                self.context_handler.copy_action()
            else:
                # نسخ البيانات المحددة
                self._copy_selected_data()
        except Exception as e:
            print(f"خطأ في عملية النسخ: {e}")
        
        return "break"
    
    def handle_paste(self, event=None):
        """معالج مفتاح F4 - اللصق"""
        try:
            if self.context_handler and hasattr(self.context_handler, 'paste_action'):
                self.context_handler.paste_action()
            else:
                # لصق البيانات
                self._paste_data()
        except Exception as e:
            print(f"خطأ في عملية اللصق: {e}")
        
        return "break"
    
    def _find_and_click_button(self, button_texts):
        """البحث عن زر بنص معين والنقر عليه"""
        def search_widget(widget):
            # فحص النص الخاص بالعنصر
            if hasattr(widget, 'cget'):
                try:
                    text = widget.cget('text')
                    if text and any(btn_text in text for btn_text in button_texts):
                        if hasattr(widget, 'invoke'):
                            widget.invoke()
                            return True
                        elif hasattr(widget, 'command'):
                            command = widget.cget('command')
                            if command:
                                command()
                                return True
                except:
                    pass
            
            # البحث في العناصر الفرعية
            try:
                for child in widget.winfo_children():
                    if search_widget(child):
                        return True
            except:
                pass
            
            return False
        
        search_widget(self.window)
    
    def _copy_selected_data(self):
        """نسخ البيانات المحددة"""
        try:
            # البحث عن عنصر محدد في جدول
            focused_widget = self.window.focus_get()
            
            if focused_widget:
                # إذا كان العنصر المحدد هو Treeview
                if hasattr(focused_widget, 'selection'):
                    selection = focused_widget.selection()
                    if selection:
                        # نسخ بيانات العنصر المحدد
                        item_data = focused_widget.item(selection[0])
                        values = item_data.get('values', [])
                        if values:
                            # تحويل البيانات إلى نص
                            text_data = '\t'.join(str(v) for v in values)
                            pyperclip.copy(text_data)
                            self.clipboard_data = values
                            print("تم نسخ البيانات المحددة")
                            return
                
                # إذا كان العنصر المحدد هو Entry أو Text
                if hasattr(focused_widget, 'selection_get'):
                    try:
                        selected_text = focused_widget.selection_get()
                        if selected_text:
                            pyperclip.copy(selected_text)
                            print("تم نسخ النص المحدد")
                            return
                    except:
                        pass
                
                # نسخ محتوى Entry أو Text
                if hasattr(focused_widget, 'get'):
                    try:
                        content = focused_widget.get()
                        if content:
                            pyperclip.copy(content)
                            print("تم نسخ المحتوى")
                            return
                    except:
                        pass
            
            print("لا توجد بيانات للنسخ")
            
        except Exception as e:
            print(f"خطأ في نسخ البيانات: {e}")
    
    def _paste_data(self):
        """لصق البيانات"""
        try:
            # الحصول على البيانات من الحافظة
            clipboard_text = pyperclip.paste()
            
            if not clipboard_text:
                print("لا توجد بيانات في الحافظة")
                return
            
            # البحث عن العنصر المحدد
            focused_widget = self.window.focus_get()
            
            if focused_widget:
                # إذا كان العنصر هو Entry
                if hasattr(focused_widget, 'insert') and hasattr(focused_widget, 'delete'):
                    try:
                        # حذف المحتوى الحالي ولصق الجديد
                        focused_widget.delete(0, tk.END)
                        focused_widget.insert(0, clipboard_text)
                        print("تم لصق البيانات في الحقل")
                        return
                    except:
                        pass
                
                # إذا كان العنصر هو Text
                if hasattr(focused_widget, 'insert') and hasattr(focused_widget, 'delete'):
                    try:
                        focused_widget.insert(tk.INSERT, clipboard_text)
                        print("تم لصق البيانات في النص")
                        return
                    except:
                        pass
            
            print("لا يمكن لصق البيانات في العنصر الحالي")
            
        except Exception as e:
            print(f"خطأ في لصق البيانات: {e}")
    
    def set_context_handler(self, handler):
        """تعيين معالج السياق"""
        self.context_handler = handler
    
    def unbind_shortcuts(self):
        """إلغاء ربط مفاتيح الاختصار"""
        try:
            self.window.unbind('<F1>')
            self.window.unbind('<F2>')
            self.window.unbind('<F3>')
            self.window.unbind('<F4>')
        except:
            pass


class ContextHandler:
    """معالج السياق للعمليات المخصصة"""
    
    def __init__(self):
        self.save_callback = None
        self.delete_callback = None
        self.copy_callback = None
        self.paste_callback = None
    
    def set_save_callback(self, callback):
        """تعيين دالة الحفظ"""
        self.save_callback = callback
    
    def set_delete_callback(self, callback):
        """تعيين دالة الحذف"""
        self.delete_callback = callback
    
    def set_copy_callback(self, callback):
        """تعيين دالة النسخ"""
        self.copy_callback = callback
    
    def set_paste_callback(self, callback):
        """تعيين دالة اللصق"""
        self.paste_callback = callback
    
    def save_action(self):
        """تنفيذ عملية الحفظ"""
        if self.save_callback:
            self.save_callback()
    
    def delete_action(self):
        """تنفيذ عملية الحذف"""
        if self.delete_callback:
            self.delete_callback()
    
    def copy_action(self):
        """تنفيذ عملية النسخ"""
        if self.copy_callback:
            self.copy_callback()
    
    def paste_action(self):
        """تنفيذ عملية اللصق"""
        if self.paste_callback:
            self.paste_callback()
