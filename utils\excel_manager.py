"""
مدير ملفات Excel - تطبيق إدارة المخازن
Excel Manager - Desktop Stores Management System
"""

import pandas as pd
import openpyxl
from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
from openpyxl.utils.dataframe import dataframe_to_rows
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any, Optional
import logging

from config import REPORTS_DIR, APP_CONFIG
from utils.logger import setup_logger

logger = setup_logger("ExcelManager")

class ExcelManager:
    """مدير ملفات Excel"""
    
    def __init__(self):
        self.reports_dir = REPORTS_DIR
        self.reports_dir.mkdir(exist_ok=True)
    
    def export_to_excel(self, data: List[Dict], filename: str, sheet_name: str = "البيانات",
                       title: str = None, headers: Dict[str, str] = None) -> tuple[bool, str]:
        """
        تصدير البيانات إلى ملف Excel
        
        Args:
            data: قائمة البيانات
            filename: اسم الملف
            sheet_name: اسم الورقة
            title: عنوان التقرير
            headers: ترجمة أسماء الأعمدة
        
        Returns:
            (success, file_path)
        """
        try:
            if not data:
                return False, "لا توجد بيانات للتصدير"
            
            # إنشاء DataFrame
            df = pd.DataFrame(data)
            
            # ترجمة أسماء الأعمدة إذا تم توفيرها
            if headers:
                df = df.rename(columns=headers)
            
            # إنشاء مسار الملف
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            file_path = self.reports_dir / f"{filename}_{timestamp}.xlsx"
            
            # إنشاء ملف Excel
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name=sheet_name, index=False, startrow=3 if title else 0)
                
                # تنسيق الملف
                workbook = writer.book
                worksheet = writer.sheets[sheet_name]
                
                # إضافة العنوان
                if title:
                    self._add_title(worksheet, title)
                
                # تنسيق الجدول
                self._format_table(worksheet, df, start_row=4 if title else 1)
                
                # تعديل عرض الأعمدة
                self._adjust_column_widths(worksheet, df)
            
            logger.info(f"تم تصدير البيانات إلى Excel: {file_path}")
            return True, str(file_path)
            
        except Exception as e:
            logger.error(f"خطأ في تصدير البيانات إلى Excel: {e}")
            return False, f"خطأ في التصدير: {e}"
    
    def _add_title(self, worksheet, title: str):
        """إضافة عنوان للتقرير"""
        try:
            # دمج الخلايا للعنوان
            worksheet.merge_cells('A1:E1')
            
            # إضافة العنوان
            title_cell = worksheet['A1']
            title_cell.value = title
            title_cell.font = Font(name='Arial', size=16, bold=True, color='FFFFFF')
            title_cell.fill = PatternFill(start_color='0a3d62', end_color='0a3d62', fill_type='solid')
            title_cell.alignment = Alignment(horizontal='center', vertical='center')
            
            # إضافة تاريخ التقرير
            worksheet.merge_cells('A2:E2')
            date_cell = worksheet['A2']
            date_cell.value = f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            date_cell.font = Font(name='Arial', size=10, italic=True)
            date_cell.alignment = Alignment(horizontal='center')
            
        except Exception as e:
            logger.warning(f"تعذر إضافة العنوان: {e}")
    
    def _format_table(self, worksheet, df: pd.DataFrame, start_row: int = 1):
        """تنسيق الجدول"""
        try:
            # تنسيق رأس الجدول
            header_fill = PatternFill(start_color='40739e', end_color='40739e', fill_type='solid')
            header_font = Font(name='Arial', size=12, bold=True, color='FFFFFF')
            
            # تطبيق التنسيق على رأس الجدول
            for col_num, column in enumerate(df.columns, 1):
                cell = worksheet.cell(row=start_row, column=col_num)
                cell.fill = header_fill
                cell.font = header_font
                cell.alignment = Alignment(horizontal='center', vertical='center')
                cell.border = self._get_border()
            
            # تنسيق بيانات الجدول
            data_font = Font(name='Arial', size=10)
            
            for row_num in range(start_row + 1, start_row + len(df) + 1):
                for col_num in range(1, len(df.columns) + 1):
                    cell = worksheet.cell(row=row_num, column=col_num)
                    cell.font = data_font
                    cell.alignment = Alignment(horizontal='center', vertical='center')
                    cell.border = self._get_border()
                    
                    # تلوين الصفوف بالتناوب
                    if row_num % 2 == 0:
                        cell.fill = PatternFill(start_color='f8f9fa', end_color='f8f9fa', fill_type='solid')
            
        except Exception as e:
            logger.warning(f"تعذر تنسيق الجدول: {e}")
    
    def _get_border(self):
        """الحصول على حدود الخلايا"""
        thin_border = Side(border_style="thin", color="000000")
        return Border(left=thin_border, right=thin_border, top=thin_border, bottom=thin_border)
    
    def _adjust_column_widths(self, worksheet, df: pd.DataFrame):
        """تعديل عرض الأعمدة"""
        try:
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                
                # تعيين عرض العمود (مع حد أدنى وأقصى)
                adjusted_width = min(max(max_length + 2, 10), 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width
                
        except Exception as e:
            logger.warning(f"تعذر تعديل عرض الأعمدة: {e}")
    
    def import_from_excel(self, file_path: str, sheet_name: str = None) -> tuple[bool, List[Dict], str]:
        """
        استيراد البيانات من ملف Excel
        
        Args:
            file_path: مسار الملف
            sheet_name: اسم الورقة (اختياري)
        
        Returns:
            (success, data, message)
        """
        try:
            file_path = Path(file_path)
            
            if not file_path.exists():
                return False, [], "الملف غير موجود"
            
            # قراءة الملف
            if sheet_name:
                df = pd.read_excel(file_path, sheet_name=sheet_name)
            else:
                df = pd.read_excel(file_path)
            
            # تحويل إلى قائمة من القواميس
            data = df.to_dict('records')
            
            # تنظيف البيانات (إزالة القيم الفارغة)
            cleaned_data = []
            for row in data:
                cleaned_row = {k: v for k, v in row.items() if pd.notna(v)}
                if cleaned_row:  # إضافة الصف فقط إذا كان يحتوي على بيانات
                    cleaned_data.append(cleaned_row)
            
            logger.info(f"تم استيراد {len(cleaned_data)} سجل من {file_path}")
            return True, cleaned_data, f"تم استيراد {len(cleaned_data)} سجل بنجاح"
            
        except Exception as e:
            logger.error(f"خطأ في استيراد البيانات من Excel: {e}")
            return False, [], f"خطأ في الاستيراد: {e}"
    
    def create_template(self, columns: List[str], filename: str, 
                       sample_data: List[Dict] = None) -> tuple[bool, str]:
        """
        إنشاء قالب Excel للاستيراد
        
        Args:
            columns: قائمة أسماء الأعمدة
            filename: اسم الملف
            sample_data: بيانات تجريبية (اختياري)
        
        Returns:
            (success, file_path)
        """
        try:
            # إنشاء DataFrame فارغ مع الأعمدة المطلوبة
            df = pd.DataFrame(columns=columns)
            
            # إضافة بيانات تجريبية إذا تم توفيرها
            if sample_data:
                sample_df = pd.DataFrame(sample_data)
                df = pd.concat([df, sample_df], ignore_index=True)
            
            # إنشاء مسار الملف
            file_path = self.reports_dir / f"template_{filename}.xlsx"
            
            # إنشاء ملف Excel
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name="البيانات", index=False)
                
                # تنسيق القالب
                workbook = writer.book
                worksheet = writer.sheets["البيانات"]
                
                # تنسيق رأس الجدول
                header_fill = PatternFill(start_color='40739e', end_color='40739e', fill_type='solid')
                header_font = Font(name='Arial', size=12, bold=True, color='FFFFFF')
                
                for col_num, column in enumerate(columns, 1):
                    cell = worksheet.cell(row=1, column=col_num)
                    cell.fill = header_fill
                    cell.font = header_font
                    cell.alignment = Alignment(horizontal='center', vertical='center')
                    cell.border = self._get_border()
                
                # تعديل عرض الأعمدة
                self._adjust_column_widths(worksheet, df)
                
                # إضافة ورقة تعليمات
                self._add_instructions_sheet(workbook, columns)
            
            logger.info(f"تم إنشاء قالب Excel: {file_path}")
            return True, str(file_path)
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء قالب Excel: {e}")
            return False, f"خطأ في إنشاء القالب: {e}"
    
    def _add_instructions_sheet(self, workbook, columns: List[str]):
        """إضافة ورقة التعليمات"""
        try:
            # إنشاء ورقة التعليمات
            instructions_sheet = workbook.create_sheet("التعليمات")
            
            # عنوان التعليمات
            instructions_sheet['A1'] = "تعليمات استخدام القالب"
            instructions_sheet['A1'].font = Font(name='Arial', size=16, bold=True, color='0a3d62')
            
            # التعليمات العامة
            instructions = [
                "",
                "تعليمات عامة:",
                "1. لا تقم بتغيير أسماء الأعمدة في ورقة البيانات",
                "2. تأكد من ملء جميع الحقول المطلوبة",
                "3. استخدم التنسيق الصحيح للتواريخ (YYYY-MM-DD)",
                "4. تأكد من صحة البيانات قبل الاستيراد",
                "",
                "وصف الأعمدة:",
            ]
            
            for i, instruction in enumerate(instructions, 2):
                instructions_sheet[f'A{i}'] = instruction
                if instruction.endswith(":"):
                    instructions_sheet[f'A{i}'].font = Font(name='Arial', size=12, bold=True)
            
            # وصف الأعمدة
            start_row = len(instructions) + 2
            for i, column in enumerate(columns, start_row):
                instructions_sheet[f'A{i}'] = f"• {column}: [وصف العمود]"
            
            # تعديل عرض العمود
            instructions_sheet.column_dimensions['A'].width = 50
            
        except Exception as e:
            logger.warning(f"تعذر إضافة ورقة التعليمات: {e}")
    
    def validate_excel_data(self, data: List[Dict], required_columns: List[str]) -> tuple[bool, List[str]]:
        """
        التحقق من صحة البيانات المستوردة
        
        Args:
            data: البيانات المستوردة
            required_columns: الأعمدة المطلوبة
        
        Returns:
            (is_valid, error_messages)
        """
        errors = []
        
        try:
            if not data:
                errors.append("لا توجد بيانات للتحقق منها")
                return False, errors
            
            # التحقق من وجود الأعمدة المطلوبة
            first_row = data[0]
            missing_columns = [col for col in required_columns if col not in first_row]
            
            if missing_columns:
                errors.append(f"الأعمدة المطلوبة مفقودة: {', '.join(missing_columns)}")
            
            # التحقق من البيانات في كل صف
            for i, row in enumerate(data, 1):
                row_errors = []
                
                # التحقق من الحقول المطلوبة
                for col in required_columns:
                    if col not in row or not row[col] or str(row[col]).strip() == '':
                        row_errors.append(f"الحقل '{col}' مطلوب")
                
                if row_errors:
                    errors.append(f"الصف {i}: {', '.join(row_errors)}")
            
            return len(errors) == 0, errors
            
        except Exception as e:
            logger.error(f"خطأ في التحقق من البيانات: {e}")
            errors.append(f"خطأ في التحقق من البيانات: {e}")
            return False, errors
    
    def get_excel_sheets(self, file_path: str) -> tuple[bool, List[str], str]:
        """
        الحصول على قائمة أوراق ملف Excel
        
        Args:
            file_path: مسار الملف
        
        Returns:
            (success, sheet_names, message)
        """
        try:
            file_path = Path(file_path)
            
            if not file_path.exists():
                return False, [], "الملف غير موجود"
            
            # قراءة أسماء الأوراق
            excel_file = pd.ExcelFile(file_path)
            sheet_names = excel_file.sheet_names
            
            return True, sheet_names, f"تم العثور على {len(sheet_names)} ورقة"
            
        except Exception as e:
            logger.error(f"خطأ في قراءة أوراق Excel: {e}")
            return False, [], f"خطأ في قراءة الملف: {e}"
