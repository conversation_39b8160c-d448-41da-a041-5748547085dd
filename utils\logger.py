"""
نظام التسجيل - تطبيق إدارة المخازن
Logging System - Desktop Stores Management System
"""

import logging
import logging.handlers
from pathlib import Path
from datetime import datetime
import sys

from config import LOGGING_CONFIG, LOGS_DIR

def setup_logger(name: str = "StoresApp", level: str = None) -> logging.Logger:
    """إعداد نظام التسجيل"""
    
    # إنشاء مجلد السجلات إذا لم يكن موجوداً
    LOGS_DIR.mkdir(exist_ok=True)
    
    # إنشاء المسجل
    logger = logging.getLogger(name)
    
    # تعيين مستوى التسجيل
    log_level = level or LOGGING_CONFIG.get("level", "INFO")
    logger.setLevel(getattr(logging, log_level.upper()))
    
    # تجنب إضافة معالجات متعددة
    if logger.handlers:
        return logger
    
    # تنسيق الرسائل
    formatter = logging.Formatter(
        LOGGING_CONFIG.get("format", "%(asctime)s - %(name)s - %(levelname)s - %(message)s"),
        datefmt="%Y-%m-%d %H:%M:%S"
    )
    
    # معالج الملف مع التدوير
    file_handler = logging.handlers.RotatingFileHandler(
        filename=LOGGING_CONFIG.get("file_path", LOGS_DIR / "app.log"),
        maxBytes=LOGGING_CONFIG.get("max_file_size", 10 * 1024 * 1024),  # 10 MB
        backupCount=LOGGING_CONFIG.get("backup_count", 5),
        encoding='utf-8'
    )
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    
    # معالج وحدة التحكم (اختياري) - مع ترميز آمن
    if LOGGING_CONFIG.get("console_output", True):
        console_handler = logging.StreamHandler(sys.stdout)
        # تنسيق مبسط للكونسول لتجنب مشاكل الترميز
        simple_formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S"
        )
        console_handler.setFormatter(simple_formatter)
        logger.addHandler(console_handler)
    
    # معالج الأخطاء الفادحة
    error_handler = logging.handlers.RotatingFileHandler(
        filename=LOGS_DIR / "errors.log",
        maxBytes=5 * 1024 * 1024,  # 5 MB
        backupCount=3,
        encoding='utf-8'
    )
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(formatter)
    logger.addHandler(error_handler)
    
    return logger

class DatabaseLogger:
    """مسجل قاعدة البيانات"""
    
    def __init__(self, db_manager):
        self.db_manager = db_manager
        self.logger = setup_logger("DatabaseLogger")
    
    def log_query(self, query: str, params: tuple = None, execution_time: float = None):
        """تسجيل استعلام قاعدة البيانات"""
        try:
            log_message = f"Query: {query}"
            if params:
                log_message += f" | Params: {params}"
            if execution_time:
                log_message += f" | Time: {execution_time:.3f}s"
            
            self.logger.debug(log_message)
            
        except Exception as e:
            self.logger.error(f"خطأ في تسجيل الاستعلام: {e}")
    
    def log_error(self, error: Exception, query: str = None, params: tuple = None):
        """تسجيل خطأ قاعدة البيانات"""
        try:
            error_message = f"Database Error: {error}"
            if query:
                error_message += f" | Query: {query}"
            if params:
                error_message += f" | Params: {params}"
            
            self.logger.error(error_message)
            
        except Exception as e:
            self.logger.error(f"خطأ في تسجيل خطأ قاعدة البيانات: {e}")

class UserActivityLogger:
    """مسجل أنشطة المستخدم"""
    
    def __init__(self, db_manager):
        self.db_manager = db_manager
        self.logger = setup_logger("UserActivityLogger")
    
    def log_login(self, user_id: int, username: str, success: bool, ip_address: str = None):
        """تسجيل محاولة تسجيل الدخول"""
        try:
            status = "نجح" if success else "فشل"
            message = f"تسجيل دخول {status} - المستخدم: {username}"
            if ip_address:
                message += f" | IP: {ip_address}"
            
            self.logger.info(message)
            
            # حفظ في قاعدة البيانات
            if success:
                self.save_activity(user_id, "تسجيل دخول", "users", user_id)
            
        except Exception as e:
            self.logger.error(f"خطأ في تسجيل محاولة الدخول: {e}")
    
    def log_logout(self, user_id: int, username: str):
        """تسجيل تسجيل الخروج"""
        try:
            message = f"تسجيل خروج - المستخدم: {username}"
            self.logger.info(message)
            
            # حفظ في قاعدة البيانات
            self.save_activity(user_id, "تسجيل خروج", "users", user_id)
            
        except Exception as e:
            self.logger.error(f"خطأ في تسجيل الخروج: {e}")
    
    def log_action(self, user_id: int, action: str, table_name: str = None, 
                   record_id: int = None, details: str = None):
        """تسجيل عمل المستخدم"""
        try:
            message = f"عمل المستخدم - ID: {user_id} | العمل: {action}"
            if table_name:
                message += f" | الجدول: {table_name}"
            if record_id:
                message += f" | السجل: {record_id}"
            if details:
                message += f" | التفاصيل: {details}"
            
            self.logger.info(message)
            
            # حفظ في قاعدة البيانات
            self.save_activity(user_id, action, table_name, record_id, details)
            
        except Exception as e:
            self.logger.error(f"خطأ في تسجيل عمل المستخدم: {e}")
    
    def save_activity(self, user_id: int, action: str, table_name: str = None, 
                     record_id: int = None, details: str = None):
        """حفظ النشاط في قاعدة البيانات"""
        try:
            self.db_manager.execute_query("""
                INSERT INTO audit_log (user_id, action, table_name, record_id, new_values)
                VALUES (?, ?, ?, ?, ?)
            """, (user_id, action, table_name, record_id, details))
            
        except Exception as e:
            self.logger.error(f"خطأ في حفظ النشاط: {e}")

class PerformanceLogger:
    """مسجل الأداء"""
    
    def __init__(self):
        self.logger = setup_logger("PerformanceLogger")
        self.start_times = {}
    
    def start_timer(self, operation_name: str):
        """بدء مؤقت العملية"""
        self.start_times[operation_name] = datetime.now()
    
    def end_timer(self, operation_name: str, log_level: str = "INFO"):
        """إنهاء مؤقت العملية وتسجيل الوقت"""
        try:
            if operation_name not in self.start_times:
                self.logger.warning(f"لم يتم العثور على مؤقت للعملية: {operation_name}")
                return
            
            start_time = self.start_times[operation_name]
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            message = f"العملية: {operation_name} | المدة: {duration:.3f} ثانية"
            
            log_method = getattr(self.logger, log_level.lower(), self.logger.info)
            log_method(message)
            
            # إزالة المؤقت
            del self.start_times[operation_name]
            
            return duration
            
        except Exception as e:
            self.logger.error(f"خطأ في إنهاء المؤقت: {e}")
    
    def log_memory_usage(self):
        """تسجيل استخدام الذاكرة"""
        try:
            import psutil
            import os
            
            process = psutil.Process(os.getpid())
            memory_info = process.memory_info()
            
            message = f"استخدام الذاكرة - RSS: {memory_info.rss / 1024 / 1024:.2f} MB | VMS: {memory_info.vms / 1024 / 1024:.2f} MB"
            self.logger.info(message)
            
        except ImportError:
            self.logger.warning("مكتبة psutil غير متوفرة لتسجيل استخدام الذاكرة")
        except Exception as e:
            self.logger.error(f"خطأ في تسجيل استخدام الذاكرة: {e}")

def log_exception(logger: logging.Logger, exception: Exception, context: str = None):
    """تسجيل استثناء مع السياق"""
    try:
        import traceback
        
        error_message = f"استثناء: {type(exception).__name__}: {exception}"
        if context:
            error_message = f"{context} - {error_message}"
        
        # تسجيل الرسالة الأساسية
        logger.error(error_message)
        
        # تسجيل تفاصيل المكدس
        logger.error("تفاصيل المكدس:")
        for line in traceback.format_exc().splitlines():
            logger.error(line)
            
    except Exception as e:
        logger.error(f"خطأ في تسجيل الاستثناء: {e}")

def create_daily_log_file():
    """إنشاء ملف سجل يومي"""
    try:
        today = datetime.now().strftime("%Y-%m-%d")
        daily_log_path = LOGS_DIR / f"daily_{today}.log"
        
        # إنشاء مسجل يومي
        daily_logger = logging.getLogger(f"DailyLog_{today}")
        daily_logger.setLevel(logging.INFO)
        
        # تجنب إضافة معالجات متعددة
        if not daily_logger.handlers:
            handler = logging.FileHandler(daily_log_path, encoding='utf-8')
            formatter = logging.Formatter(
                "%(asctime)s - %(levelname)s - %(message)s",
                datefmt="%H:%M:%S"
            )
            handler.setFormatter(formatter)
            daily_logger.addHandler(handler)
        
        return daily_logger
        
    except Exception as e:
        print(f"خطأ في إنشاء ملف السجل اليومي: {e}")
        return None
