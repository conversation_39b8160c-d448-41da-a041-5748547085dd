"""
نافذة تسجيل الدخول - تطبيق إدارة المخازن
Login Window - Desktop Stores Management System
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from pathlib import Path
import json
import os
from datetime import datetime

from config import APP_CONFIG, UI_CONFIG, get_message
from models import User
from ui.global_shortcuts import GlobalShortcuts, ContextHandler



class LoginWindow:
    """نافذة تسجيل الدخول"""
    
    def __init__(self, parent, auth_manager):
        self.parent = parent
        self.auth_manager = auth_manager
        self.login_window = None
        self.username_var = None
        self.password_var = None
        self.remember_var = None
        self.on_login_success = None
        self.is_closed = False
        self.error_label = None  # لعرض رسائل الخطأ

        self.setup_login_window()

        # تفعيل مفاتيح الاختصار
        self.setup_shortcuts()
    
    def setup_login_window(self):
        """إعداد نافذة تسجيل الدخول"""
        # إنشاء نافذة منبثقة منفصلة بحجم صغير
        self.login_window = tk.Toplevel(self.parent)
        self.login_window.title("تسجيل الدخول")

        # جعل النافذة modal
        self.login_window.transient(self.parent)
        self.login_window.grab_set()

        # تعيين أيقونة النافذة
        self.set_window_icon()

        # إعداد المحتوى أولاً
        self.setup_content()

        # إعداد معالجات الأحداث
        self.setup_event_handlers()

        # تعيين الحجم بقوة بعد إنشاء المحتوى
        self.login_window.geometry("450x650")
        self.login_window.resizable(False, False)
        self.login_window.minsize(450, 650)
        self.login_window.maxsize(450, 650)

        # توسيط النافذة
        self.center_window()

        # إجبار التحديث والإظهار
        self.login_window.update_idletasks()
        self.login_window.geometry("450x650")  # إعادة تعيين الحجم

        # جعل النافذة في المقدمة وضمان ظهورها
        self.login_window.deiconify()
        self.login_window.lift()
        self.login_window.attributes('-topmost', True)
        self.login_window.focus_force()
        self.login_window.attributes('-topmost', False)


    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        # أبعاد النافذة
        window_width = 450
        window_height = 650

        # تحديث النافذة للحصول على الأبعاد الصحيحة
        self.login_window.update_idletasks()

        # الحصول على أبعاد الشاشة
        screen_width = self.login_window.winfo_screenwidth()
        screen_height = self.login_window.winfo_screenheight()

        # حساب موضع النافذة للتوسيط المثالي
        x = max(0, (screen_width - window_width) // 2)
        y = max(0, (screen_height - window_height) // 2)

        # تعيين موضع النافذة مع التأكد من التوسيط
        self.login_window.geometry(f"{window_width}x{window_height}+{x}+{y}")

        # تحديث إضافي للتأكد من التطبيق
        self.login_window.update()
    
    def set_window_icon(self):
        """تعيين أيقونة النافذة"""
        try:
            icon_path = Path(__file__).parent.parent / "assets" / "icons" / "app_icon.ico"
            if icon_path.exists():
                self.login_window.iconbitmap(str(icon_path))
        except Exception:
            pass
    
    def setup_content(self):
        """إعداد محتوى نافذة تسجيل الدخول"""
        # الإطار الرئيسي - تخطيط مبسط
        main_frame = ttk_bs.Frame(self.login_window)
        main_frame.pack(fill=BOTH, expand=True, padx=30, pady=30)

        # إطار الشعار والعنوان
        header_frame = ttk_bs.Frame(main_frame)
        header_frame.pack(pady=10)
        
        # شعار التطبيق
        self.setup_logo(header_frame)
        
        # عنوان التطبيق
        title_label = ttk_bs.Label(
            header_frame,
            text=APP_CONFIG["app_name"],
            bootstyle="primary"
        )
        title_label.pack(pady=10)

        # عنوان فرعي
        subtitle_label = ttk_bs.Label(
            header_frame,
            text="تسجيل الدخول إلى النظام",
            bootstyle="secondary"
        )
        subtitle_label.pack()

        # منطقة عرض رسائل الخطأ
        error_frame = ttk_bs.Frame(main_frame)
        error_frame.pack(pady=(10, 0), fill=X)

        self.error_label = ttk_bs.Label(
            error_frame,
            text="",
            font=("Arial", 11, "bold"),
            wraplength=350,
            justify="center",
            background="#f8d7da",
            foreground="#721c24",
            relief="solid",
            borderwidth=1,
            padding=(10, 8),
            cursor="hand2"
        )
        # إضافة حدث النقر لإخفاء رسالة الخطأ
        self.error_label.bind("<Button-1>", lambda e: self.hide_error())
        # لا نعرض الـ label في البداية
        # self.error_label.pack()

        # إطار النموذج
        form_frame = ttk_bs.Frame(main_frame)
        form_frame.pack(pady=15, fill=X)
        
        # حقل اسم المستخدم
        username_frame = ttk_bs.Frame(form_frame)
        username_frame.pack(fill=X, pady=10)
        
        username_label = ttk_bs.Label(
            username_frame,
            text="اسم المستخدم:",
            bootstyle="primary"
        )
        username_label.pack(anchor=E, pady=(0, 5))

        self.username_var = tk.StringVar()
        self.username_entry = ttk_bs.Entry(
            username_frame,
            textvariable=self.username_var,
            bootstyle="primary",
            width=30
        )
        self.username_entry.pack(fill=X)

        # إخفاء رسالة الخطأ عند الكتابة في حقل اسم المستخدم
        self.username_entry.bind('<KeyPress>', lambda e: self.hide_error())
        # التنقل إلى حقل كلمة المرور عند الضغط على Enter
        self.username_entry.bind('<Return>', self.focus_password)

        # حقل كلمة المرور
        password_frame = ttk_bs.Frame(form_frame)
        password_frame.pack(fill=X, pady=10)

        password_label = ttk_bs.Label(
            password_frame,
            text="كلمة المرور:",
            bootstyle="primary"
        )
        password_label.pack(anchor=E, pady=(0, 5))

        self.password_var = tk.StringVar()
        self.password_entry = ttk_bs.Entry(
            password_frame,
            textvariable=self.password_var,
            bootstyle="primary",
            show="*",
            width=30
        )
        self.password_entry.pack(fill=X)

        # إخفاء رسالة الخطأ عند الكتابة في حقل كلمة المرور
        self.password_entry.bind('<KeyPress>', lambda e: self.hide_error())
        # التنقل إلى زر تسجيل الدخول عند الضغط على Enter
        self.password_entry.bind('<Return>', self.focus_login_button)

        # خيار تذكر المستخدم
        remember_frame = ttk_bs.Frame(form_frame)
        remember_frame.pack(fill=X, pady=10)

        self.remember_var = tk.BooleanVar()
        remember_check = ttk_bs.Checkbutton(
            remember_frame,
            text="تذكر بيانات الدخول",
            variable=self.remember_var,
            bootstyle="primary"
        )
        remember_check.pack(anchor=E)

        # تحميل بيانات الدخول المحفوظة بعد إنشاء جميع المتغيرات
        self.load_saved_credentials()
        
        # أزرار العمليات - تخطيط مبسط ومضمون
        buttons_frame = ttk_bs.Frame(main_frame)
        buttons_frame.pack(pady=30, fill=X)

        # زر تسجيل الدخول
        self.login_button = ttk_bs.Button(
            buttons_frame,
            text="🔑 تسجيل الدخول",
            command=self.login,
            bootstyle="primary",
            width=20
        )
        self.login_button.pack(pady=10)

        # إضافة معالج Enter لزر تسجيل الدخول
        self.login_button.bind('<Return>', lambda e: self.login())

        # زر إعادة تعيين كلمة المرور
        reset_button = ttk_bs.Button(
            buttons_frame,
            text="🔄 نسيت كلمة المرور؟",
            command=self.reset_password,
            bootstyle="info-outline",
            width=22
        )
        reset_button.pack(pady=5)

        # زر الخروج
        exit_button = ttk_bs.Button(
            buttons_frame,
            text="❌ خروج",
            command=self.exit_application,
            bootstyle="danger-outline",
            width=18
        )
        exit_button.pack(pady=5)
        
        # معلومات المطور
        footer_frame = ttk_bs.Frame(main_frame)
        footer_frame.pack(side=BOTTOM, pady=10)
        
        developer_label = ttk_bs.Label(
            footer_frame,
            text=f"تطوير: {APP_CONFIG['app_author']} | {APP_CONFIG['app_contact']}",
            bootstyle="secondary"
        )
        developer_label.pack()
        
        # تعيين التركيز على حقل اسم المستخدم
        self.username_entry.focus_set()
    
    def setup_logo(self, parent):
        """إعداد شعار التطبيق"""
        try:
            logo_path = Path(__file__).parent.parent / "assets" / "icons" / "logo.png"
            if logo_path.exists():
                from PIL import Image, ImageTk
                image = Image.open(logo_path)
                image = image.resize((100, 100), Image.Resampling.LANCZOS)
                self.logo_image = ImageTk.PhotoImage(image)
                logo_label = ttk_bs.Label(parent, image=self.logo_image)
                logo_label.pack()
            else:
                # استخدام أيقونة نصية إذا لم يتم العثور على الشعار
                logo_label = ttk_bs.Label(
                    parent,
                    text="🏪",
                    bootstyle="primary"
                )
                logo_label.pack()
        except Exception:
            # استخدام أيقونة نصية في حالة الخطأ
            logo_label = ttk_bs.Label(
                parent,
                text="🏪",
                bootstyle="primary"
            )
            logo_label.pack()
    
    def focus_password(self, event=None):
        """التنقل إلى حقل كلمة المرور"""
        try:
            self.password_entry.focus_set()
            return "break"  # منع معالجة الحدث مرة أخرى
        except Exception as e:
            print(f"خطأ في التنقل إلى حقل كلمة المرور: {e}")

    def focus_login_button(self, event=None):
        """التنقل إلى زر تسجيل الدخول"""
        try:
            self.login_button.focus_set()
            return "break"  # منع معالجة الحدث مرة أخرى
        except Exception as e:
            print(f"خطأ في التنقل إلى زر تسجيل الدخول: {e}")

    def setup_event_handlers(self):
        """إعداد معالجات الأحداث"""
        # معالج إغلاق النافذة
        self.login_window.protocol("WM_DELETE_WINDOW", self.exit_application)

        # اختصار Escape للخروج
        self.login_window.bind("<Escape>", lambda e: self.exit_application())

    def show_error(self, message: str):
        """عرض رسالة خطأ في أعلى الشاشة"""
        if self.error_label:
            self.error_label.config(text=f"⚠️ {message}\n(انقر هنا أو ابدأ الكتابة لإخفاء الرسالة)")
            self.error_label.pack(pady=(5, 10), fill=X)
            # إخفاء الرسالة تلقائياً بعد 6 ثوان
            self.login_window.after(6000, self.hide_error)

    def hide_error(self):
        """إخفاء رسالة الخطأ"""
        if self.error_label:
            self.error_label.config(text="")
            self.error_label.pack_forget()
    
    def login(self):
        """تسجيل الدخول"""
        try:
            # إخفاء أي رسائل خطأ سابقة
            self.hide_error()

            username = self.username_var.get().strip()
            password = self.password_var.get()

            # التحقق من صحة البيانات
            if not username:
                self.show_error("يرجى إدخال اسم المستخدم")
                return

            if not password:
                self.show_error("يرجى إدخال كلمة المرور")
                return

            # محاولة تسجيل الدخول
            success, message, user = self.auth_manager.authenticate(username, password)

            if success and user:
                # نجح تسجيل الدخول - بدون رسالة نجاح

                # حفظ بيانات الدخول إذا كان المستخدم يريد ذلك
                if self.remember_var.get():
                    self.save_credentials(username)

                # إخفاء نافذة تسجيل الدخول مباشرة
                self.hide()

                # استدعاء دالة نجاح تسجيل الدخول
                if self.on_login_success:
                    self.on_login_success(user)
            else:
                # فشل تسجيل الدخول - عرض الرسالة في أعلى الشاشة
                self.show_error(message)

                # مسح كلمة المرور
                self.password_var.set("")

        except Exception as e:
            self.show_error(f"حدث خطأ في تسجيل الدخول: {e}")
    
    def reset_password(self):
        """إعادة تعيين كلمة المرور"""
        try:
            # نافذة إدخال اسم المستخدم
            username = simpledialog.askstring(
                "إعادة تعيين كلمة المرور",
                "أدخل اسم المستخدم:",
                parent=self.login_window
            )
            
            if not username:
                return
            
            # محاولة إعادة تعيين كلمة المرور
            success, message, temp_password = self.auth_manager.reset_password(username)
            
            if success:
                messagebox.showinfo(
                    "نجح",
                    f"{message}\n\nكلمة المرور المؤقتة: {temp_password}\n\nيرجى تغيير كلمة المرور بعد تسجيل الدخول"
                )
                
                # تعبئة اسم المستخدم وكلمة المرور المؤقتة
                self.username_var.set(username)
                self.password_var.set(temp_password)
            else:
                messagebox.showerror("خطأ", message)
        
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في إعادة تعيين كلمة المرور: {e}")
    
    def save_credentials(self, username: str):
        """حفظ بيانات الدخول"""
        try:
            credentials_file = Path("data") / "saved_credentials.json"

            # إنشاء مجلد data إذا لم يكن موجوداً
            credentials_file.parent.mkdir(exist_ok=True)

            # حفظ اسم المستخدم فقط (لا نحفظ كلمة المرور لأسباب أمنية)
            credentials_data = {
                "username": username,
                "remember": True,
                "last_login": str(datetime.now())
            }

            with open(credentials_file, 'w', encoding='utf-8') as f:
                json.dump(credentials_data, f, ensure_ascii=False, indent=2)

            print(f"تم حفظ بيانات الدخول للمستخدم: {username}")

        except Exception as e:
            print(f"خطأ في حفظ بيانات الدخول: {e}")

    def load_saved_credentials(self):
        """تحميل بيانات الدخول المحفوظة"""
        try:
            # التحقق من وجود المتغيرات أولاً
            if not hasattr(self, 'username_var') or not hasattr(self, 'remember_var'):
                print("تحذير: المتغيرات غير جاهزة لتحميل بيانات الدخول")
                return

            if self.username_var is None or self.remember_var is None:
                print("تحذير: المتغيرات لم يتم إنشاؤها بعد")
                return

            credentials_file = Path("data") / "saved_credentials.json"

            if credentials_file.exists():
                with open(credentials_file, 'r', encoding='utf-8') as f:
                    credentials_data = json.load(f)

                # تحميل اسم المستخدم المحفوظ
                if credentials_data.get("remember", False):
                    saved_username = credentials_data.get("username", "")
                    if saved_username:
                        self.username_var.set(saved_username)
                        self.remember_var.set(True)
                        print(f"تم تحميل بيانات الدخول المحفوظة للمستخدم: {saved_username}")

        except Exception as e:
            print(f"خطأ في تحميل بيانات الدخول: {e}")

    def clear_saved_credentials(self):
        """مسح بيانات الدخول المحفوظة"""
        try:
            credentials_file = Path("data") / "saved_credentials.json"
            if credentials_file.exists():
                credentials_file.unlink()
                print("تم مسح بيانات الدخول المحفوظة")
        except Exception as e:
            print(f"خطأ في مسح بيانات الدخول: {e}")
    


    def exit_application(self):
        """الخروج من التطبيق"""
        if messagebox.askyesno("تأكيد الخروج", "هل تريد الخروج من التطبيق؟"):
            self.parent.quit()
    
    def show(self):
        """عرض نافذة تسجيل الدخول"""
        if self.login_window:
            self.login_window.deiconify()
            self.login_window.focus_force()
    
    def hide(self):
        """إخفاء نافذة تسجيل الدخول"""
        self.is_closed = True
        if self.login_window:
            self.login_window.withdraw()
    
    def destroy(self):
        """إغلاق نافذة تسجيل الدخول"""
        if self.login_window:
            self.login_window.destroy()
            self.login_window = None

    def setup_shortcuts(self):
        """إعداد مفاتيح الاختصار"""
        try:
            # إنشاء معالج السياق
            self.context_handler = ContextHandler()
            
            # تعيين دوال العمليات
            self.context_handler.set_save_callback(self.shortcut_save)
            self.context_handler.set_delete_callback(self.shortcut_delete)
            self.context_handler.set_copy_callback(self.shortcut_copy)
            self.context_handler.set_paste_callback(self.shortcut_paste)
            
            # تفعيل مفاتيح الاختصار
            target_window = getattr(self, 'login_window', None)
            if target_window:
                self.global_shortcuts = GlobalShortcuts(target_window, self.context_handler)
        except Exception as e:
            print(f"خطأ في إعداد مفاتيح الاختصار: {e}")
    
    def shortcut_save(self):
        """عملية الحفظ (F1)"""
        try:
            # البحث عن دوال الحفظ المتاحة
            save_methods = ['save_data', 'save_changes', 'save_item', 'save', 'add_item']
            for method_name in save_methods:
                if hasattr(self, method_name):
                    getattr(self, method_name)()
                    print(f"تم تنفيذ {method_name} بمفتاح F1")
                    return
            print("عملية الحفظ غير متاحة")
        except Exception as e:
            print(f"خطأ في عملية الحفظ: {e}")
    
    def shortcut_delete(self):
        """عملية الحذف (F2)"""
        try:
            # البحث عن دوال الحذف المتاحة
            delete_methods = ['delete_selected', 'delete_item', 'delete_data', 'delete']
            for method_name in delete_methods:
                if hasattr(self, method_name):
                    getattr(self, method_name)()
                    print(f"تم تنفيذ {method_name} بمفتاح F2")
                    return
            print("عملية الحذف غير متاحة")
        except Exception as e:
            print(f"خطأ في عملية الحذف: {e}")
    
    def shortcut_copy(self):
        """عملية النسخ (F3)"""
        try:
            # البحث عن دوال النسخ المتاحة
            copy_methods = ['copy_data', 'copy_selected', 'copy_item']
            for method_name in copy_methods:
                if hasattr(self, method_name):
                    getattr(self, method_name)()
                    print(f"تم تنفيذ {method_name} بمفتاح F3")
                    return
            
            # نسخ عامة
            import pyperclip
            pyperclip.copy("تم النسخ من النافذة")
            print("تم نسخ البيانات العامة")
        except Exception as e:
            print(f"خطأ في عملية النسخ: {e}")
    
    def shortcut_paste(self):
        """عملية اللصق (F4)"""
        try:
            # البحث عن دوال اللصق المتاحة
            paste_methods = ['paste_data', 'paste_item']
            for method_name in paste_methods:
                if hasattr(self, method_name):
                    getattr(self, method_name)()
                    print(f"تم تنفيذ {method_name} بمفتاح F4")
                    return
            
            # لصق عام
            import pyperclip
            clipboard_text = pyperclip.paste()
            if clipboard_text:
                print(f"تم لصق: {clipboard_text[:50]}")
            else:
                print("لا توجد بيانات في الحافظة")
        except Exception as e:
            print(f"خطأ في عملية اللصق: {e}")