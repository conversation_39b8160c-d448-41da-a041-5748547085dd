#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
مهيئ التطبيق المحسن - Enhanced App Initializer
يحتوي على تحسينات لبدء التطبيق وحل مشاكل الأداء
"""

import sys
import os
import threading
import time
import tkinter as tk
from pathlib import Path

def suppress_ui_errors_enhanced():
    """قمع رسائل أخطاء واجهة المستخدم المحسن"""
    
    # إعادة تعريف messagebox.showerror لتكون صامتة
    try:
        from tkinter import messagebox
        original_showerror = messagebox.showerror

        def silent_showerror(title, message, **kwargs):
            # تسجيل الخطأ في ملف السجل فقط
            try:
                from utils.logger import setup_logger
                logger = setup_logger()
                logger.error(f"UI Error - {title}: {message}")
            except (ImportError, AttributeError):
                pass
            # لا تظهر أي رسالة للمستخدم
            return None

        # استبدال دالة عرض الأخطاء
        messagebox.showerror = silent_showerror
    except ImportError:
        pass

    # معالجة أخطاء Tkinter العامة المحسنة
    def handle_tk_error_enhanced(exc, val, tb):
        error_msg = str(val).lower()
        
        # قائمة موسعة من الأخطاء المراد تجاهلها
        silent_errors = [
            "invalid command name",
            "bad window path name", 
            "application has been destroyed",
            "toplevel",
            "treeview",
            "display column",
            "can't invoke",
            "widget has been destroyed",
            "bad screen distance",
            "unknown option",
            "invalid boolean operator",
            "couldn't recognize data",
            "image doesn't exist",
            "font doesn't exist",
            "bad relief",
            "bad anchor",
            "bad justify",
            "bad state",
            "bad wrap mode",
            "bad tab alignment",
            "bad scrollbar element",
            "bad menu entry index",
            "bad listbox index",
            "bad text index",
            "bad canvas item",
            "bad scale value",
            "bad spinbox format",
            "bad progressbar mode"
        ]

        if any(silent_error in error_msg for silent_error in silent_errors):
            # تسجيل صامت فقط
            try:
                from utils.logger import setup_logger
                logger = setup_logger()
                logger.debug(f"Suppressed UI Error: {error_msg}")
            except (ImportError, AttributeError):
                pass
            return

        # للأخطاء الأخرى، استخدم المعالج الافتراضي
        sys.__excepthook__(exc, val, tb)

    # تعيين معالج الأخطاء الجديد
    def callback_exception_handler_enhanced(self, exc, val, tb):
        return handle_tk_error_enhanced(exc, val, tb)

    try:
        tk.Tk.report_callback_exception = callback_exception_handler_enhanced
    except:
        pass

def optimize_python_environment():
    """تحسين بيئة Python للأداء الأفضل"""
    try:
        # تحسين garbage collection
        import gc
        gc.set_threshold(700, 10, 10)  # تحسين عتبات garbage collection
        
        # تحسين threading
        threading.stack_size(32768)  # تقليل حجم stack للخيوط
        
        # تحسين sys.path
        # إزالة المسارات المكررة
        seen = set()
        sys.path = [x for x in sys.path if not (x in seen or seen.add(x))]
        
        print("تم تحسين بيئة Python")
        
    except Exception as e:
        print(f"تحذير: فشل في تحسين بيئة Python: {e}")

def setup_enhanced_environment():
    """إعداد بيئة التطبيق المحسنة"""
    try:
        # استيراد المسارات من config
        from config import (
            BASE_DIR, DATA_DIR, REPORTS_DIR, BACKUPS_DIR,
            LOGS_DIR, ASSETS_DIR, ICONS_DIR
        )

        # إنشاء المجلدات المطلوبة
        directories = [
            DATA_DIR, REPORTS_DIR, BACKUPS_DIR,
            LOGS_DIR, ASSETS_DIR, ICONS_DIR
        ]

        for directory in directories:
            try:
                directory.mkdir(exist_ok=True, parents=True)
            except Exception as e:
                print(f"تحذير: فشل في إنشاء المجلد {directory}: {e}")

        print(f"[نجح] تم إعداد بيئة التطبيق المحسنة في: {BASE_DIR}")
        return True

    except Exception as e:
        print(f"خطأ في إعداد بيئة التطبيق: {e}")
        return False

def check_and_install_dependencies_enhanced():
    """التحقق من المكتبات المطلوبة وتثبيتها - محسن"""
    required_packages = {
        'ttkbootstrap': 'ttkbootstrap',
        'bcrypt': 'bcrypt', 
        'pillow': 'PIL',
        'reportlab': 'reportlab',
        'openpyxl': 'openpyxl',
        'pandas': 'pandas',
        'psutil': 'psutil'  # إضافة psutil لمراقبة الأداء
    }

    missing_packages = []
    
    for package_name, import_name in required_packages.items():
        try:
            __import__(import_name)
        except ImportError:
            missing_packages.append(package_name)

    if missing_packages:
        print(f"المكتبات المفقودة: {missing_packages}")
        
        # محاولة التثبيت التلقائي
        try:
            import subprocess
            for package in missing_packages:
                print(f"تثبيت {package}...")
                result = subprocess.run(
                    [sys.executable, "-m", "pip", "install", package],
                    capture_output=True,
                    text=True,
                    timeout=300  # 5 دقائق timeout
                )
                
                if result.returncode == 0:
                    print(f"تم تثبيت {package} بنجاح")
                else:
                    print(f"فشل في تثبيت {package}: {result.stderr}")
                    return False
            
            print("تم تثبيت جميع المكتبات المطلوبة")
            return True
            
        except Exception as e:
            print(f"خطأ في التثبيت التلقائي: {e}")
            return False

    return True

def initialize_database_enhanced():
    """تهيئة قاعدة البيانات المحسنة"""
    try:
        from database import db_manager
        from utils.performance_optimizer import PerformanceOptimizer
        
        # تحسين إعدادات قاعدة البيانات
        PerformanceOptimizer.optimize_database_queries()
        
        # التحقق من سلامة قاعدة البيانات
        try:
            db_manager.execute_query("PRAGMA integrity_check")
            print("تم التحقق من سلامة قاعدة البيانات")
        except Exception as e:
            print(f"تحذير: مشكلة في سلامة قاعدة البيانات: {e}")
        
        # إنشاء الجداول إذا لم تكن موجودة
        db_manager.init_database()
        
        print("تم تهيئة قاعدة البيانات بنجاح")
        return True
        
    except Exception as e:
        print(f"خطأ في تهيئة قاعدة البيانات: {e}")
        return False

def preload_critical_modules():
    """تحميل الوحدات الحرجة مسبقاً"""
    critical_modules = [
        'models',
        'auth_manager', 
        'ui.main_window',
        'utils.logger',
        'utils.threading_manager',
        'utils.performance_optimizer'
    ]
    
    loaded_modules = []
    failed_modules = []
    
    for module_name in critical_modules:
        try:
            __import__(module_name)
            loaded_modules.append(module_name)
        except Exception as e:
            failed_modules.append((module_name, str(e)))
    
    print(f"تم تحميل {len(loaded_modules)} وحدة حرجة")
    if failed_modules:
        print(f"فشل في تحميل {len(failed_modules)} وحدة")
        for module, error in failed_modules:
            print(f"  - {module}: {error}")
    
    return len(failed_modules) == 0

def initialize_performance_monitoring():
    """تهيئة مراقبة الأداء"""
    try:
        from utils.performance_optimizer import performance_optimizer
        
        # بدء مراقبة الذاكرة
        performance_optimizer.start_memory_monitor()
        
        print("تم تفعيل مراقبة الأداء")
        return True
        
    except Exception as e:
        print(f"تحذير: فشل في تفعيل مراقبة الأداء: {e}")
        return False

def enhanced_app_initialization():
    """تهيئة التطبيق المحسنة الشاملة"""
    print("=" * 50)
    print("بدء تهيئة التطبيق المحسنة...")
    print("=" * 50)
    
    initialization_steps = [
        ("قمع أخطاء واجهة المستخدم", suppress_ui_errors_enhanced),
        ("تحسين بيئة Python", optimize_python_environment),
        ("إعداد بيئة التطبيق", setup_enhanced_environment),
        ("التحقق من المكتبات", check_and_install_dependencies_enhanced),
        ("تهيئة قاعدة البيانات", initialize_database_enhanced),
        ("تحميل الوحدات الحرجة", preload_critical_modules),
        ("تفعيل مراقبة الأداء", initialize_performance_monitoring)
    ]
    
    success_count = 0
    total_steps = len(initialization_steps)
    
    for step_name, step_function in initialization_steps:
        try:
            print(f"[{success_count + 1}/{total_steps}] {step_name}...")
            
            start_time = time.time()
            result = step_function()
            end_time = time.time()
            
            if result:
                success_count += 1
                print(f"  ✅ نجح في {end_time - start_time:.2f} ثانية")
            else:
                print(f"  ❌ فشل في {end_time - start_time:.2f} ثانية")
                
        except Exception as e:
            print(f"  ❌ خطأ: {e}")
    
    print("=" * 50)
    print(f"انتهت التهيئة: {success_count}/{total_steps} خطوات نجحت")
    print("=" * 50)
    
    return success_count == total_steps

def cleanup_on_exit():
    """تنظيف الموارد عند الخروج"""
    try:
        from utils.performance_optimizer import performance_optimizer
        
        # إيقاف مراقبة الأداء
        performance_optimizer.stop_memory_monitor()
        
        # تنظيف الذاكرة
        performance_optimizer.cleanup_memory()
        
        print("تم تنظيف الموارد بنجاح")
        
    except Exception as e:
        print(f"خطأ في تنظيف الموارد: {e}")

# تسجيل دالة التنظيف للتشغيل عند الخروج
import atexit
atexit.register(cleanup_on_exit)