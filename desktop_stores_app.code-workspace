{"folders": [{"name": "Desktop Stores App", "path": "."}], "settings": {"python.defaultInterpreterPath": "./.venv/Scripts/python.exe", "python.terminal.activateEnvironment": true, "files.encoding": "utf8", "files.eol": "\r\n", "editor.tabSize": 4, "editor.insertSpaces": true, "python.linting.enabled": true, "python.linting.pylintEnabled": false, "python.linting.flake8Enabled": true, "python.formatting.provider": "black", "files.associations": {"*.py": "python", "*.md": "markdown"}, "python.analysis.extraPaths": ["./", "./ui", "./utils"], "python.analysis.autoImportCompletions": true, "python.analysis.typeCheckingMode": "basic", "terminal.integrated.cwd": "${workspaceFolder}", "zencoder.enableRepoIndexing": true}, "extensions": {"recommendations": ["ms-python.python", "ms-python.flake8", "ms-python.black-formatter"]}}