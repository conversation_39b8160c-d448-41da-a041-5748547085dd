#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import sys
import os

# إضافة مسار المشروع
sys.path.append('.')

try:
    from database import db_manager
    
    print('🗑️ حذف البيانات التجريبية...')
    print('=' * 50)

    # بدء معاملة قاعدة البيانات
    try:
        # حذف أصناف المعاملة التجريبية
        print('🔄 حذف أصناف المعاملة التجريبية...')
        deleted_items = db_manager.execute_query('''
            DELETE FROM transaction_items
            WHERE transaction_id = 2
        ''')
        print(f'✅ تم حذف أصناف المعاملة')

        # حذف المعاملة التجريبية
        print('🔄 حذف المعاملة التجريبية...')
        deleted_transaction = db_manager.execute_query('''
            DELETE FROM transactions
            WHERE id = 2 AND transaction_number = 'TR-000001'
        ''')
        print(f'✅ تم حذف المعاملة TR-000001')

        # حذف المستفيد التجريبي
        print('🔄 حذف المستفيد التجريبي...')
        deleted_beneficiary = db_manager.execute_query('''
            DELETE FROM beneficiaries
            WHERE id = 3 AND name = 'مستفيد تجريبي'
        ''')
        print(f'✅ تم حذف المستفيد التجريبي')

        # حذف أي حركات مخزون تجريبية
        print('🔄 البحث عن حركات مخزون تجريبية...')
        test_movements = db_manager.fetch_all('''
            SELECT id, item_number, movement_type, quantity, organization_name, notes
            FROM inventory_movements_new
            WHERE organization_name LIKE '%تجريب%' OR notes LIKE '%تجريب%' 
               OR notes LIKE '%اختبار%' OR notes LIKE '%test%'
        ''')

        if test_movements:
            print(f'📦 تم العثور على {len(test_movements)} حركة مخزون تجريبية:')
            for mov in test_movements:
                print(f'  🆔 {mov["id"]} | 📦 {mov["item_number"]} | 🔄 {mov["movement_type"]} | 📊 {mov["quantity"]} | 📝 {mov["notes"]}')
            
            # حذف حركات المخزون التجريبية
            deleted_movements = db_manager.execute_query('''
                DELETE FROM inventory_movements_new
                WHERE organization_name LIKE '%تجريب%' OR notes LIKE '%تجريب%' 
                   OR notes LIKE '%اختبار%' OR notes LIKE '%test%'
            ''')
            print(f'✅ تم حذف حركات المخزون التجريبية')
        else:
            print('❌ لم يتم العثور على حركات مخزون تجريبية')

        # التحقق من النتائج
        print('\n🔍 التحقق من النتائج...')
        print('=' * 50)

        # التحقق من المعاملات
        remaining_transactions = db_manager.fetch_all('''
            SELECT id, transaction_number
            FROM transactions
            ORDER BY id
        ''')
        print(f'📋 المعاملات المتبقية: {len(remaining_transactions)}')
        for trans in remaining_transactions:
            print(f'  🆔 {trans["id"]} | 📄 {trans["transaction_number"]}')

        # التحقق من المستفيدين
        remaining_beneficiaries = db_manager.fetch_all('''
            SELECT id, name
            FROM beneficiaries
            ORDER BY id
        ''')
        print(f'\n👥 المستفيدين المتبقيين: {len(remaining_beneficiaries)}')
        for ben in remaining_beneficiaries:
            print(f'  🆔 {ben["id"]} | 👤 {ben["name"]}')

        # التحقق من حركات المخزون
        remaining_movements = db_manager.fetch_all('''
            SELECT COUNT(*) as count
            FROM inventory_movements_new
        ''')
        print(f'\n📦 حركات المخزون المتبقية: {remaining_movements[0]["count"]}')

        print('\n🎉 تم حذف جميع البيانات التجريبية بنجاح!')

    except Exception as e:
        print(f'❌ خطأ في حذف البيانات: {e}')
        import traceback
        traceback.print_exc()

except Exception as e:
    print(f'❌ خطأ عام: {e}')
    import traceback
    traceback.print_exc()
