#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import sys
import os

# إضافة مسار المشروع
sys.path.append('.')

try:
    from database import db_manager
    
    print('🔍 البحث عن المعاملات التجريبية...')
    print('=' * 50)

    # البحث في جدول transactions
    try:
        transactions = db_manager.fetch_all('''
            SELECT id, transaction_number, beneficiary_name, transaction_date, created_at
            FROM transactions
            WHERE beneficiary_name LIKE '%خالد%' OR beneficiary_name LIKE '%تجرب%'
               OR beneficiary_name LIKE '%test%'
            ORDER BY created_at DESC
        ''')
        
        if transactions:
            print(f'📦 تم العثور على {len(transactions)} معاملة تجريبية:')
            for trans in transactions:
                print(f'  🆔 معرف المعاملة: {trans["id"]}')
                print(f'  📄 رقم المعاملة: {trans["transaction_number"]}')
                print(f'  👤 اسم المستفيد: {trans["beneficiary_name"]}')
                print(f'  📅 تاريخ المعاملة: {trans["transaction_date"]}')
                print(f'  ⏰ تاريخ الإنشاء: {trans["created_at"]}')
                
                # البحث عن أصناف هذه المعاملة
                items = db_manager.fetch_all('''
                    SELECT ti.id, ti.item_number, ti.quantity, ai.item_name
                    FROM transaction_items ti
                    LEFT JOIN added_items ai ON ti.item_number = ai.item_number
                    WHERE ti.transaction_id = ?
                ''', [trans["id"]])
                
                if items:
                    print(f'    📦 الأصناف ({len(items)}):')
                    for item in items:
                        print(f'      - {item["item_number"]}: {item["item_name"]} (كمية: {item["quantity"]})')
                
                print('-' * 30)
        else:
            print('❌ لم يتم العثور على معاملات تجريبية في transactions')
    except Exception as e:
        print(f'❌ خطأ في البحث في transactions: {e}')

    # البحث في جدول حركات المخزون
    try:
        movements = db_manager.fetch_all('''
            SELECT im.id, im.movement_date, im.item_number, im.movement_type, 
                   im.quantity, im.organization_name, im.notes,
                   ai.item_name
            FROM inventory_movements_new im
            LEFT JOIN added_items ai ON im.item_number = ai.item_number
            WHERE im.organization_name LIKE '%خالد%' OR im.notes LIKE '%تجرب%'
               OR im.notes LIKE '%test%'
            ORDER BY im.movement_date DESC
        ''')

        if movements:
            print(f'\n📦 تم العثور على {len(movements)} حركة مخزون تجريبية:')
            for mov in movements:
                print(f'  🆔 معرف الحركة: {mov["id"]}')
                print(f'  📅 التاريخ: {mov["movement_date"]}')
                print(f'  📦 رقم الصنف: {mov["item_number"]} - {mov["item_name"]}')
                print(f'  🔄 نوع الحركة: {mov["movement_type"]}')
                print(f'  📊 الكمية: {mov["quantity"]}')
                print(f'  🏢 الجهة: {mov["organization_name"]}')
                print(f'  📝 الملاحظات: {mov["notes"]}')
                print('-' * 30)
        else:
            print('\n❌ لم يتم العثور على حركات مخزون تجريبية')
    except Exception as e:
        print(f'❌ خطأ في البحث في inventory_movements_new: {e}')

    # عرض آخر المعاملات
    try:
        print('\n🕐 آخر 5 معاملات:')
        print('=' * 50)
        recent_transactions = db_manager.fetch_all('''
            SELECT id, transaction_number, beneficiary_name, transaction_date, created_at
            FROM transactions
            ORDER BY created_at DESC
            LIMIT 5
        ''')
        
        for trans in recent_transactions:
            print(f'  🆔 {trans["id"]} | 📄 {trans["transaction_number"]} | 👤 {trans["beneficiary_name"]} | 📅 {trans["transaction_date"]}')
    except Exception as e:
        print(f'❌ خطأ في عرض آخر المعاملات: {e}')

except Exception as e:
    print(f'❌ خطأ عام: {e}')
    import traceback
    traceback.print_exc()
