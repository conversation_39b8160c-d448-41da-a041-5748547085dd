#!/usr/bin/env python3
"""
مراقب النشاط والإغلاق التلقائي - تطبيق إدارة المخازن
Activity Monitor & Auto Logout - Desktop Stores Management System
"""

import tkinter as tk
from datetime import datetime
import threading
import time
import logging

logger = logging.getLogger(__name__)


class ActivityMonitor:
    """مراقب النشاط والإغلاق التلقائي"""

    def __init__(self, main_window, app_instance, timeout_minutes=5):
        self.main_window = main_window
        self.app_instance = app_instance
        self.timeout_minutes = timeout_minutes
        self.timeout_seconds = timeout_minutes * 60

        # إذا كان timeout_minutes = 0، فلا تبدأ المراقبة
        if timeout_minutes <= 0:
            self.is_monitoring = False
            return

        # تحميل إعدادات التحذير
        try:
            from config import SECURITY_CONFIG
            self.show_warning = SECURITY_CONFIG.get(
                "show_warning_before_logout", True
            )
            self.warning_time = SECURITY_CONFIG.get("warning_time_seconds", 60)
        except (ImportError, AttributeError, KeyError):
            self.show_warning = True
            self.warning_time = 60

        self.last_activity = datetime.now()
        self.is_monitoring = False
        self.monitor_thread = None
        self.warning_shown = False
        self.warning_window = None

        # قائمة النوافذ المراقبة
        self.monitored_windows = set()

        # أحداث النشاط المراقبة
        self.activity_events = [
            '<Motion>',     # حركة الماوس
            '<Button-1>',   # النقر بالماوس الأيسر
            '<Button-2>',   # النقر بالماوس الأوسط
            '<Button-3>',   # النقر بالماوس الأيمن
            '<Key>',        # الضغط على أي مفتاح
            '<MouseWheel>', # عجلة الماوس
            '<FocusIn>',    # التركيز على النافذة
            '<Enter>',      # دخول الماوس للنافذة
        ]

        self.start_monitoring()

    def start_monitoring(self):
        """بدء مراقبة النشاط"""
        try:
            # لا تبدأ المراقبة إذا كان timeout_minutes = 0 أو أقل
            if self.timeout_minutes <= 0:
                logger.info("مراقبة النشاط معطلة (timeout_minutes = 0)")
                return

            if not self.is_monitoring:
                self.is_monitoring = True
                self.last_activity = datetime.now()
                self.warning_shown = False

                # ربط أحداث النشاط بالنافذة الرئيسية
                self.add_window_to_monitoring(self.main_window.parent)

                # بدء خيط المراقبة
                self.monitor_thread = threading.Thread(
                    target=self._monitor_activity, daemon=True
                )
                self.monitor_thread.start()

                # بدء خيط مراقبة النوافذ الجديدة
                self.window_monitor_thread = threading.Thread(
                    target=self._monitor_new_windows, daemon=True
                )
                self.window_monitor_thread.start()

                logger.info(
                    f"تم بدء مراقبة النشاط - مهلة الإغلاق: {self.timeout_minutes} دقائق"
                )

        except Exception as e:
            logger.error(f"خطأ في بدء مراقبة النشاط: {e}")

    def stop_monitoring(self):
        """إيقاف مراقبة النشاط"""
        try:
            self.is_monitoring = False

            # إلغاء ربط الأحداث من جميع النوافذ المراقبة
            for window in list(self.monitored_windows):
                self.remove_window_from_monitoring(window)

            # إغلاق نافذة التحذير إن وجدت
            if self.warning_window:
                try:
                    self.warning_window.destroy()
                except tk.TclError:
                    pass
                self.warning_window = None

            logger.info("تم إيقاف مراقبة النشاط")

        except Exception as e:
            logger.error(f"خطأ في إيقاف مراقبة النشاط: {e}")

    def add_window_to_monitoring(self, window):
        """إضافة نافذة لمراقبة النشاط"""
        try:
            # التحقق من صحة النافذة قبل إضافتها
            if window and window not in self.monitored_windows:
                # فحص إضافي للتأكد من أن النافذة صالحة
                try:
                    # محاولة الوصول لخصائص النافذة للتأكد من صحتها
                    if not window.winfo_exists():
                        return

                    # محاولة الحصول على العنوان بأمان
                    window_title = 'نافذة غير معروفة'
                    try:
                        window_title = window.title()
                    except (tk.TclError, AttributeError):
                        pass

                    # إضافة النافذة للمراقبة
                    self.monitored_windows.add(window)
                    self.bind_activity_events(window)
                    logger.debug(f"تم إضافة نافذة للمراقبة: {window_title}")

                except tk.TclError as tcl_error:
                    logger.debug(f"نافذة غير صالحة، تم تجاهلها: {tcl_error}")
                except Exception as window_error:
                    logger.debug(f"خطأ في فحص النافذة: {window_error}")

        except Exception as e:
            logger.debug(f"خطأ في إضافة نافذة للمراقبة: {e}")

    def remove_window_from_monitoring(self, window):
        """إزالة نافذة من مراقبة النشاط"""
        try:
            if window in self.monitored_windows:
                self.monitored_windows.remove(window)
                self.unbind_activity_events(window)
                window_title = (window.title() if hasattr(window, 'title')
                               else 'نافذة غير معروفة')
                logger.debug(f"تم إزالة نافذة من المراقبة: {window_title}")
        except Exception as e:
            logger.debug(f"خطأ في إزالة نافذة من المراقبة: {e}")

    def _monitor_new_windows(self):
        """مراقبة النوافذ الجديدة وإضافتها للمراقبة"""

        while self.is_monitoring:
            try:
                if tk._default_root:
                    # البحث عن جميع النوافذ المفتوحة
                    all_toplevels = [
                        widget for widget in tk._default_root.winfo_children()
                        if isinstance(widget, tk.Toplevel)
                    ]

                    # إضافة النوافذ الجديدة للمراقبة
                    for toplevel in all_toplevels:
                        try:
                            # التحقق من صحة النافذة أولاً
                            if not toplevel.winfo_exists():
                                continue

                            # محاولة الحصول على العنوان بأمان
                            window_title = ""
                            try:
                                window_title = toplevel.title().lower()
                            except tk.TclError:
                                # تجاهل النوافذ التي لا يمكن الوصول لعنوانها
                                continue
                            except Exception:
                                continue

                            # تجاهل نوافذ تسجيل الدخول ونوافذ التحذير
                            if ("تسجيل الدخول" in window_title or
                                "login" in window_title or
                                "تحذير" in window_title or
                                toplevel == self.warning_window):
                                continue

                            # إضافة النافذة للمراقبة إذا لم تكن مراقبة بالفعل
                            if toplevel not in self.monitored_windows:
                                self.add_window_to_monitoring(toplevel)

                        except Exception as e:
                            logger.debug(f"خطأ في فحص نافذة جديدة: {e}")

                # فحص كل 2 ثانية
                time.sleep(2)

            except Exception as e:
                logger.debug(f"خطأ في مراقبة النوافذ الجديدة: {e}")
                time.sleep(2)

    def bind_activity_events(self, widget):
        """ربط أحداث النشاط بالعنصر وجميع عناصره الفرعية"""
        try:
            # التحقق من صحة العنصر أولاً
            if not widget.winfo_exists():
                return

            # ربط الأحداث بالعنصر الحالي
            for event in self.activity_events:
                try:
                    widget.bind(event, self.on_activity, add='+')
                except tk.TclError:
                    # تجاهل الأخطاء في ربط الأحداث
                    continue

            # ربط الأحداث بجميع العناصر الفرعية
            try:
                for child in widget.winfo_children():
                    self.bind_activity_events(child)
            except tk.TclError:
                # تجاهل أخطاء الوصول للعناصر الفرعية
                pass

        except Exception as e:
            logger.debug(f"تحذير في ربط أحداث النشاط: {e}")

    def unbind_activity_events(self, widget):
        """إلغاء ربط أحداث النشاط"""
        try:
            # التحقق من صحة العنصر أولاً
            if not widget.winfo_exists():
                return

            # إلغاء ربط الأحداث من العنصر الحالي
            for event in self.activity_events:
                try:
                    widget.unbind(event)
                except tk.TclError:
                    # تجاهل الأخطاء في إلغاء ربط الأحداث
                    continue

            # إلغاء ربط الأحداث من جميع العناصر الفرعية
            try:
                for child in widget.winfo_children():
                    self.unbind_activity_events(child)
            except tk.TclError:
                # تجاهل أخطاء الوصول للعناصر الفرعية
                pass

        except Exception as e:
            logger.debug(f"تحذير في إلغاء ربط أحداث النشاط: {e}")

    def on_activity(self, event=None):
        """معالج النشاط"""
        try:
            self.last_activity = datetime.now()
            self.warning_shown = False

            # إغلاق نافذة التحذير إن كانت مفتوحة
            if self.warning_window:
                try:
                    self.warning_window.destroy()
                except tk.TclError:
                    pass
                self.warning_window = None

        except Exception as e:
            logger.debug(f"خطأ في معالج النشاط: {e}")

    def _monitor_activity(self):
        """خيط مراقبة النشاط"""
        while self.is_monitoring:
            try:
                current_time = datetime.now()
                time_since_activity = (
                    current_time - self.last_activity
                ).total_seconds()

                # التحقق من انتهاء المهلة
                if time_since_activity >= self.timeout_seconds:
                    # إغلاق تلقائي
                    self.main_window.parent.after(0, self.auto_logout)
                    break

                # عرض تحذير قبل الوقت المحدد من الإغلاق
                elif (self.show_warning and
                      time_since_activity >= (
                          self.timeout_seconds - self.warning_time
                      ) and not self.warning_shown):
                    self.warning_shown = True
                    self.main_window.parent.after(0, self.show_warning_dialog)

                # فحص كل ثانية
                time.sleep(1)

            except Exception as e:
                logger.error(f"خطأ في خيط مراقبة النشاط: {e}")
                break

    def show_warning_dialog(self):
        """عرض تحذير قبل الإغلاق التلقائي"""
        try:
            if self.warning_window:
                return

            # إنشاء نافذة التحذير
            self.warning_window = tk.Toplevel(self.main_window.parent)
            self.warning_window.title("تحذير - إغلاق تلقائي")
            self.warning_window.geometry("400x200")
            self.warning_window.resizable(False, False)
            self.warning_window.configure(bg='#fff3cd')

            # توسيط النافذة
            self.warning_window.transient(self.main_window.parent)
            self.warning_window.grab_set()

            # تحديد موقع النافذة
            parent_x = self.main_window.parent.winfo_x()
            parent_y = self.main_window.parent.winfo_y()
            parent_width = self.main_window.parent.winfo_width()
            parent_height = self.main_window.parent.winfo_height()
            x = parent_x + (parent_width // 2) - 200
            y = parent_y + (parent_height // 2) - 100
            self.warning_window.geometry(f"400x200+{x}+{y}")

            # محتوى النافذة
            main_frame = tk.Frame(self.warning_window, bg='#fff3cd', padx=20, pady=20)
            main_frame.pack(fill=tk.BOTH, expand=True)

            # أيقونة التحذير
            warning_label = tk.Label(
                main_frame,
                text="⚠️",
                font=("Arial", 24),
                bg='#fff3cd',
                fg='#856404'
            )
            warning_label.pack(pady=(0, 10))

            # رسالة التحذير
            warning_seconds = self.warning_time
            if warning_seconds >= 60:
                time_text = f"{warning_seconds // 60} دقيقة"
            else:
                time_text = f"{warning_seconds} ثانية"

            warning_text = (
                f"⚠️ تحذير إغلاق تلقائي ⚠️\n\n"
                f"سيتم إغلاق جميع النوافذ والعودة\n"
                f"لشاشة تسجيل الدخول خلال {time_text}\n"
                f"بسبب عدم النشاط"
            )
            message_label = tk.Label(
                main_frame,
                text=warning_text,
                font=("Arial", 11, "bold"),
                bg='#fff3cd',
                fg='#856404',
                justify=tk.CENTER
            )
            message_label.pack(pady=(0, 15))

            # أزرار
            buttons_frame = tk.Frame(main_frame, bg='#fff3cd')
            buttons_frame.pack()

            # زر البقاء متصل
            stay_button = tk.Button(
                buttons_frame,
                text="البقاء متصل",
                command=self.extend_session,
                bg='#28a745',
                fg='white',
                font=("Arial", 10, "bold"),
                padx=20,
                pady=5
            )
            stay_button.pack(side=tk.LEFT, padx=5)

            # زر تسجيل الخروج
            logout_button = tk.Button(
                buttons_frame,
                text="تسجيل الخروج",
                command=self.manual_logout,
                bg='#dc3545',
                fg='white',
                font=("Arial", 10, "bold"),
                padx=20,
                pady=5
            )
            logout_button.pack(side=tk.LEFT, padx=5)

            # جعل النافذة في المقدمة
            self.warning_window.lift()
            self.warning_window.focus_force()

        except Exception as e:
            logger.error(f"خطأ في عرض تحذير الإغلاق التلقائي: {e}")

    def extend_session(self):
        """تمديد الجلسة"""
        try:
            self.last_activity = datetime.now()
            self.warning_shown = False

            if self.warning_window:
                self.warning_window.destroy()
                self.warning_window = None

            logger.info("تم تمديد الجلسة بواسطة المستخدم")

        except Exception as e:
            logger.error(f"خطأ في تمديد الجلسة: {e}")

    def manual_logout(self):
        """تسجيل خروج يدوي"""
        try:
            if self.warning_window:
                self.warning_window.destroy()
                self.warning_window = None

            self.logout_user()

        except Exception as e:
            logger.error(f"خطأ في تسجيل الخروج اليدوي: {e}")

    def auto_logout(self):
        """الإغلاق التلقائي"""
        try:
            logger.info("تم تفعيل الإغلاق التلقائي بسبب عدم النشاط")
            self.logout_user()

        except Exception as e:
            logger.error(f"خطأ في الإغلاق التلقائي: {e}")

    def logout_user(self):
        """تسجيل خروج المستخدم وإغلاق جميع النوافذ والعودة لشاشة تسجيل الدخول فقط"""
        try:
            # إيقاف المراقبة
            self.stop_monitoring()

            # تسجيل خروج المستخدم الحالي
            if self.main_window.current_user:
                self.app_instance.auth_manager.logout(
                    self.main_window.current_user
                )
                username = self.main_window.current_user.username
                logger.info(f"تم تسجيل خروج المستخدم: {username}")

            # إغلاق جميع النوافذ المفتوحة
            self.close_all_windows()

            # إخفاء النافذة الرئيسية
            self.main_window.parent.withdraw()

            # تنظيف المتغيرات
            self.main_window.current_user = None

            # عرض نافذة تسجيل الدخول مرة أخرى
            self.app_instance.show_login_window()

            logger.info(
                "تم تسجيل الخروج التلقائي وإغلاق جميع النوافذ "
                "والعودة لشاشة تسجيل الدخول"
            )

        except Exception as e:
            logger.error(f"خطأ في تسجيل خروج المستخدم: {e}")

    def close_all_windows(self):
        """إغلاق جميع النوافذ المفتوحة باستثناء نافذة تسجيل الدخول"""
        try:
            total_closed = 0

            # 1. استخدام مدير النوافذ العالمي
            try:
                from window_manager import WindowManager
                global_closed = WindowManager.force_close_all_windows()
                total_closed += global_closed
                logger.info(
                    f"مدير النوافذ العالمي: تم إغلاق {global_closed} نافذة"
                )
            except ImportError:
                logger.warning("مدير النوافذ العالمي غير متوفر، استخدام الطريقة التقليدية")

            # 2. إغلاق النوافذ المسجلة في النافذة الرئيسية
            if hasattr(self.main_window, 'close_all_registered_windows'):
                registered_closed = self.main_window.close_all_registered_windows()
                total_closed += registered_closed
                logger.info(
                    f"النافذة الرئيسية: تم إغلاق {registered_closed} نافذة مسجلة"
                )

            # 3. البحث الشامل عن النوافذ المتبقية وإغلاقها
            remaining_closed = 0

            try:
                if tk._default_root:
                    all_toplevels = [
                        widget for widget in tk._default_root.winfo_children()
                        if isinstance(widget, tk.Toplevel)
                    ]

                    for toplevel in all_toplevels:
                        try:
                            # التحقق من صحة النافذة أولاً
                            if not toplevel.winfo_exists():
                                continue

                            # محاولة الحصول على العنوان بأمان
                            window_title = ""
                            try:
                                window_title = toplevel.title().lower()
                            except tk.TclError:
                                # إغلاق النافذة حتى لو لم نتمكن من الحصول على العنوان
                                try:
                                    toplevel.destroy()
                                    remaining_closed += 1
                                except tk.TclError:
                                    pass
                                continue
                            except Exception:
                                continue

                            # تجاهل نوافذ تسجيل الدخول
                            if ("تسجيل الدخول" in window_title or
                                "login" in window_title):
                                continue

                            # إغلاق النافذة
                            try:
                                toplevel.destroy()
                                remaining_closed += 1
                                logger.debug(f"تم إغلاق نافذة متبقية: {window_title}")
                            except tk.TclError:
                                pass

                        except Exception as e:
                            logger.debug(f"تحذير في إغلاق نافذة متبقية: {e}")

                total_closed += remaining_closed
                if remaining_closed > 0:
                    logger.info(
                        f"البحث الشامل: تم إغلاق {remaining_closed} نافذة متبقية"
                    )

            except Exception as e:
                logger.debug(f"تحذير في البحث الشامل: {e}")

            # 4. محاولة أخيرة لإغلاق أي نوافذ عالقة
            try:
                import gc
                gc.collect()  # تنظيف الذاكرة

                # البحث عن نوافذ عالقة
                if tk._default_root:
                    stuck_windows = [
                        widget for widget in tk._default_root.winfo_children()
                        if isinstance(widget, (tk.Toplevel, tk.Tk))
                    ]

                    stuck_closed = 0
                    for window in stuck_windows:
                        try:
                            # التحقق من صحة النافذة
                            if not window.winfo_exists():
                                continue

                            # محاولة الحصول على العنوان بأمان
                            window_title = ""
                            try:
                                window_title = window.title().lower()
                            except tk.TclError:
                                # إغلاق النافذة حتى لو لم نتمكن من الحصول على العنوان
                                try:
                                    app_root = getattr(self.app_instance, 'root', None)
                                    if window != app_root:
                                        window.quit()
                                        stuck_closed += 1
                                except tk.TclError:
                                    pass
                                continue
                            except Exception:
                                continue

                            try:
                                app_root = getattr(self.app_instance, 'root', None)
                                if ("تسجيل الدخول" not in window_title and
                                    "login" not in window_title and
                                    window != app_root):
                                    window.quit()  # محاولة quit بدلاً من destroy
                                    stuck_closed += 1
                            except tk.TclError:
                                pass
                        except Exception:
                            pass

                    if stuck_closed > 0:
                        total_closed += stuck_closed
                        logger.info(f"إغلاق طارئ: تم إغلاق {stuck_closed} نافذة عالقة")

            except Exception as e:
                logger.debug(f"تحذير في الإغلاق الطارئ: {e}")

            logger.info(f"🎯 إجمالي النوافذ المُغلقة: {total_closed}")

        except Exception as e:
            logger.error(f"خطأ في إغلاق النوافذ: {e}")

    def reset_timer(self):
        """إعادة تعيين مؤقت النشاط"""
        self.on_activity()

    def get_time_remaining(self):
        """الحصول على الوقت المتبقي قبل الإغلاق التلقائي"""
        try:
            current_time = datetime.now()
            time_since_activity = (current_time - self.last_activity).total_seconds()
            remaining = self.timeout_seconds - time_since_activity
            return max(0, remaining)
        except (AttributeError, TypeError):
            return 0

# مثيل عالمي لمراقب النشاط
_global_activity_monitor = None

def set_global_activity_monitor(monitor):
    """تعيين مراقب النشاط العالمي"""
    global _global_activity_monitor
    _global_activity_monitor = monitor

def register_window_for_activity_monitoring(window):
    """تسجيل نافذة في مراقبة النشاط العالمية"""
    global _global_activity_monitor
    if _global_activity_monitor:
        _global_activity_monitor.add_window_to_monitoring(window)

def unregister_window_from_activity_monitoring(window):
    """إلغاء تسجيل نافذة من مراقبة النشاط العالمية"""
    global _global_activity_monitor
    if _global_activity_monitor:
        _global_activity_monitor.remove_window_from_monitoring(window)
