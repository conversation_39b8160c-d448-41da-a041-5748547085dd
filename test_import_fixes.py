#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار إصلاحات استيراد Excel للجدول التنظيمي والمستفيدين
Test Excel Import Fixes for Organizational Chart and Beneficiaries
"""

import os
import sys
import tempfile
import pandas as pd
from pathlib import Path

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import db_manager
from models import OrganizationalChart, Beneficiary, Department, Unit

def test_organizational_chart_import():
    """اختبار استيراد الجدول التنظيمي"""
    print("=" * 60)
    print("🧪 اختبار استيراد الجدول التنظيمي")
    print("=" * 60)
    
    try:
        # إنشاء ملف Excel تجريبي
        test_data = [
            {
                'اسم الصنف': 'جهاز كمبيوتر محمول',
                'رقم الصنف': 'LAPTOP001',
                'اسم المعدة': 'لابتوب ديل',
                'الكمية': 5,
                'ملاحظات': 'للاستخدام الإداري'
            },
            {
                'اسم الصنف': 'طابعة ليزر',
                'رقم الصنف': 'PRINTER001',
                'اسم المعدة': 'طابعة HP',
                'الكمية': 3,
                'ملاحظات': 'للمكاتب'
            },
            {
                'اسم الصنف': 'شاشة عرض',
                'رقم الصنف': 'MONITOR001',
                'اسم المعدة': 'شاشة سامسونج',
                'الكمية': 10,
                'ملاحظات': 'شاشات 24 بوصة'
            }
        ]
        
        # إنشاء ملف Excel مؤقت
        temp_file = tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False)
        temp_file_path = temp_file.name
        temp_file.close()
        
        df = pd.DataFrame(test_data)
        df.to_excel(temp_file_path, index=False)
        
        print(f"📁 تم إنشاء ملف Excel: {temp_file_path}")
        
        # تنظيف البيانات التجريبية السابقة
        db_manager.execute_query("DELETE FROM organizational_chart WHERE item_code LIKE 'LAPTOP%' OR item_code LIKE 'PRINTER%' OR item_code LIKE 'MONITOR%'")
        print("🧹 تم تنظيف البيانات التجريبية السابقة")
        
        # فحص الحالة قبل الاستيراد
        before_count = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart WHERE is_active = 1")[0]
        print(f"📊 البيانات النشطة قبل الاستيراد: {before_count}")
        
        # تنفيذ الاستيراد
        print("\n📥 بدء عملية الاستيراد...")
        from utils.organizational_chart_import import import_organizational_chart_from_excel
        
        def progress_callback(progress, message):
            print(f"   📊 {progress:.1f}% - {message}")
        
        def cancel_check():
            return False
        
        result = import_organizational_chart_from_excel(
            temp_file_path, 
            progress_callback=progress_callback,
            cancel_check=cancel_check
        )
        
        print(f"\n📊 نتائج الاستيراد:")
        print(f"   ✅ نجح: {result.success_count}")
        print(f"   🔄 مكرر: {result.duplicate_count}")
        print(f"   ❌ أخطاء: {result.error_count}")
        
        if result.errors:
            print("   📝 تفاصيل الأخطاء:")
            for error in result.errors:
                print(f"      • {error}")
        
        # فحص الحالة بعد الاستيراد
        after_count = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart WHERE is_active = 1")[0]
        print(f"\n📊 البيانات النشطة بعد الاستيراد: {after_count}")
        print(f"📈 الزيادة: {after_count - before_count}")
        
        # فحص البيانات المستوردة
        imported_items = db_manager.fetch_all("""
            SELECT item_name, item_code, quantity, is_active 
            FROM organizational_chart 
            WHERE item_code IN ('LAPTOP001', 'PRINTER001', 'MONITOR001')
            ORDER BY item_code
        """)
        
        print(f"\n📋 البيانات المستوردة ({len(imported_items)} عنصر):")
        for item in imported_items:
            status = "نشط" if item[3] else "غير نشط"
            print(f"   • {item[0]} ({item[1]}) - كمية: {item[2]} - حالة: {status}")
        
        # تنظيف الملف المؤقت
        os.unlink(temp_file_path)
        print(f"\n🗑️ تم حذف الملف المؤقت")
        
        return result.success_count > 0
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الجدول التنظيمي: {e}")
        import traceback
        print(f"تفاصيل الخطأ: {traceback.format_exc()}")
        return False

def test_beneficiaries_import():
    """اختبار استيراد المستفيدين"""
    print("\n" + "=" * 60)
    print("🧪 اختبار استيراد المستفيدين")
    print("=" * 60)
    
    try:
        # التأكد من وجود إدارة ووحدة للاختبار
        test_dept = Department(name="إدارة تجريبية", is_active=True)
        test_dept.save()
        
        test_unit = Unit(name="وحدة تجريبية", is_active=True)
        test_unit.save()
        
        # إنشاء ملف Excel تجريبي
        test_data = [
            {
                'الاسم': 'أحمد محمد علي',
                'الرقم العام': 'BEN001',
                'الرتبة': 'نقيب',
                'الإدارة': 'إدارة تجريبية',
                'الوحدة': 'وحدة تجريبية'
            },
            {
                'الاسم': 'محمد أحمد حسن',
                'الرقم العام': 'BEN002',
                'الرتبة': 'ملازم',
                'الإدارة': 'إدارة تجريبية',
                'الوحدة': 'وحدة تجريبية'
            },
            {
                'الاسم': 'علي حسن محمد',
                'الرقم العام': 'BEN003',
                'الرتبة': 'رقيب',
                'الإدارة': 'إدارة تجريبية',
                'الوحدة': 'وحدة تجريبية'
            }
        ]
        
        # إنشاء ملف Excel مؤقت
        temp_file = tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False)
        temp_file_path = temp_file.name
        temp_file.close()
        
        df = pd.DataFrame(test_data)
        df.to_excel(temp_file_path, index=False)
        
        print(f"📁 تم إنشاء ملف Excel: {temp_file_path}")
        
        # تنظيف البيانات التجريبية السابقة
        db_manager.execute_query("DELETE FROM beneficiaries WHERE number LIKE 'BEN%'")
        print("🧹 تم تنظيف البيانات التجريبية السابقة")
        
        # فحص الحالة قبل الاستيراد
        before_count = db_manager.fetch_one("SELECT COUNT(*) FROM beneficiaries WHERE is_active = 1")[0]
        print(f"📊 المستفيدين النشطين قبل الاستيراد: {before_count}")
        
        # تنفيذ الاستيراد
        print("\n📥 بدء عملية الاستيراد...")
        from utils.excel_import_manager import ExcelImportManager
        
        def progress_callback(progress, message):
            print(f"   📊 {progress:.1f}% - {message}")
        
        def cancel_check():
            return False
        
        result = ExcelImportManager.import_beneficiaries_from_excel(
            temp_file_path, 
            progress_callback=progress_callback,
            cancel_check=cancel_check
        )
        
        print(f"\n📊 نتائج الاستيراد:")
        print(f"   ✅ نجح: {result.success_count}")
        print(f"   🔄 مكرر: {result.duplicate_count}")
        print(f"   ❌ أخطاء: {result.error_count}")
        
        if result.errors:
            print("   📝 تفاصيل الأخطاء:")
            for error in result.errors:
                print(f"      • {error}")
        
        # فحص الحالة بعد الاستيراد
        after_count = db_manager.fetch_one("SELECT COUNT(*) FROM beneficiaries WHERE is_active = 1")[0]
        print(f"\n📊 المستفيدين النشطين بعد الاستيراد: {after_count}")
        print(f"📈 الزيادة: {after_count - before_count}")
        
        # فحص البيانات المستوردة
        imported_beneficiaries = db_manager.fetch_all("""
            SELECT name, number, rank, is_active 
            FROM beneficiaries 
            WHERE number IN ('BEN001', 'BEN002', 'BEN003')
            ORDER BY number
        """)
        
        print(f"\n📋 البيانات المستوردة ({len(imported_beneficiaries)} مستفيد):")
        for ben in imported_beneficiaries:
            status = "نشط" if ben[3] else "غير نشط"
            print(f"   • {ben[0]} ({ben[1]}) - رتبة: {ben[2]} - حالة: {status}")
        
        # تنظيف الملف المؤقت
        os.unlink(temp_file_path)
        print(f"\n🗑️ تم حذف الملف المؤقت")
        
        # تنظيف البيانات التجريبية
        db_manager.execute_query("DELETE FROM beneficiaries WHERE number LIKE 'BEN%'")
        db_manager.execute_query("DELETE FROM departments WHERE name = 'إدارة تجريبية'")
        db_manager.execute_query("DELETE FROM units WHERE name = 'وحدة تجريبية'")
        print("🧹 تم تنظيف البيانات التجريبية")
        
        return result.success_count > 0
        
    except Exception as e:
        print(f"❌ خطأ في اختبار المستفيدين: {e}")
        import traceback
        print(f"تفاصيل الخطأ: {traceback.format_exc()}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار إصلاحات استيراد Excel")
    print("=" * 60)
    
    # اختبار الجدول التنظيمي
    org_chart_success = test_organizational_chart_import()
    
    # اختبار المستفيدين
    beneficiaries_success = test_beneficiaries_import()
    
    # النتائج النهائية
    print("\n" + "=" * 60)
    print("📊 ملخص نتائج الاختبار")
    print("=" * 60)
    print(f"🏢 الجدول التنظيمي: {'✅ نجح' if org_chart_success else '❌ فشل'}")
    print(f"👥 المستفيدين: {'✅ نجح' if beneficiaries_success else '❌ فشل'}")
    
    if org_chart_success and beneficiaries_success:
        print("\n🎉 جميع الاختبارات نجحت! الإصلاحات تعمل بشكل صحيح.")
    else:
        print("\n⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
