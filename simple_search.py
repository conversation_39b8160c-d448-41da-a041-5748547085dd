#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import sys
import os

# إضافة مسار المشروع
sys.path.append('.')

try:
    from database import db_manager
    
    print('🔍 البحث البسيط عن البيانات التجريبية...')
    print('=' * 50)

    # فحص بنية جدول beneficiaries
    try:
        structure = db_manager.fetch_all('''
            PRAGMA table_info(beneficiaries)
        ''')
        
        print('📋 أعمدة جدول beneficiaries:')
        for col in structure:
            print(f'  📄 {col["name"]} ({col["type"]})')
    except Exception as e:
        print(f'❌ خطأ في فحص بنية beneficiaries: {e}')

    # البحث في حركات المخزون بطريقة بسيطة
    try:
        print('\n🔍 حركات المخزون الأخيرة:')
        print('=' * 50)
        movements = db_manager.fetch_all('''
            SELECT *
            FROM inventory_movements_new
            ORDER BY id DESC
            LIMIT 10
        ''')

        if movements:
            print(f'📦 تم العثور على {len(movements)} حركة:')
            for mov in movements:
                print(f'  🆔 {mov["id"]} | 📦 {mov["item_number"]} | 🔄 {mov["movement_type"]} | 📊 {mov["quantity"]} | 🏢 {mov["organization_name"]}')
        else:
            print('❌ لم يتم العثور على حركات')
    except Exception as e:
        print(f'❌ خطأ في البحث في inventory_movements_new: {e}')

    # البحث في المستفيدين
    try:
        print('\n🔍 المستفيدين:')
        print('=' * 50)
        beneficiaries = db_manager.fetch_all('''
            SELECT *
            FROM beneficiaries
            ORDER BY id DESC
            LIMIT 10
        ''')

        if beneficiaries:
            print(f'👥 تم العثور على {len(beneficiaries)} مستفيد:')
            for ben in beneficiaries:
                print(f'  🆔 {ben["id"]} | 👤 {ben["name"]} | 📄 {ben.get("general_number", "N/A")}')
        else:
            print('❌ لم يتم العثور على مستفيدين')
    except Exception as e:
        print(f'❌ خطأ في البحث في beneficiaries: {e}')

    # البحث في المعاملات
    try:
        print('\n🔍 المعاملات الأخيرة:')
        print('=' * 50)
        transactions = db_manager.fetch_all('''
            SELECT *
            FROM transactions
            ORDER BY id DESC
            LIMIT 5
        ''')

        if transactions:
            print(f'📋 تم العثور على {len(transactions)} معاملة:')
            for trans in transactions:
                print(f'  🆔 {trans["id"]} | 📄 {trans["transaction_number"]} | 👤 مستفيد ID: {trans["beneficiary_id"]}')
        else:
            print('❌ لم يتم العثور على معاملات')
    except Exception as e:
        print(f'❌ خطأ في البحث في transactions: {e}')

except Exception as e:
    print(f'❌ خطأ عام: {e}')
    import traceback
    traceback.print_exc()
