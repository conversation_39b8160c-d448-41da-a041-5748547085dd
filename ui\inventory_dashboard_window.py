#!/usr/bin/env python3
"""
لوحة تحكم المخزون
Inventory Dashboard Window
"""

import tkinter as tk
from tkinter import ttk, messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from datetime import datetime, date
import threading

from config import APP_CONFIG, UI_CONFIG, get_message
from models import Item, Category, OrganizationalChart, AddedItem
from database import db_manager

class InventoryDashboardWindow:
    """لوحة تحكم المخزون"""
    
    def __init__(self, parent, main_window):
        self.parent = parent
        self.main_window = main_window
        self.dashboard_data = {}
        
        # مسح المحتوى الحالي وعرض لوحة التحكم
        self.setup_dashboard()
        self.load_dashboard_data()
    
    def setup_dashboard(self):
        """إعداد لوحة تحكم المخزون"""
        # مسح المحتوى الحالي
        self.main_window.clear_main_content()
        
        # تحديث شريط الحالة
        if hasattr(self.main_window, 'status_var'):
            self.main_window.status_var.set("لوحة تحكم المخزون")
        
        # العنوان الرئيسي
        header_frame = ttk_bs.Frame(self.main_window.main_frame)
        header_frame.pack(fill=X, pady=10)
        
        # العنوان مع الأيقونة
        title_label = ttk_bs.Label(
            header_frame,
            text="📦 لوحة تحكم المخزون",
            font=("Arial", 18, "bold"),
            bootstyle="primary"
        )
        title_label.pack(side=LEFT)
        
        # أزرار الأدوات
        tools_frame = ttk_bs.Frame(header_frame)
        tools_frame.pack(side=RIGHT)
        
        # زر طباعة تقرير المخزون
        print_btn = ttk_bs.Button(
            tools_frame,
            text="🖨️ طباعة تقرير المخزون",
            command=self.print_inventory_report,
            bootstyle="outline-info",
            width=25
        )
        print_btn.pack(side=RIGHT, padx=5)
        
        # زر إخلاء حالة المخزون
        clear_btn = ttk_bs.Button(
            tools_frame,
            text="🗑️ إخلاء حالة المخزون",
            command=self.clear_inventory_status,
            bootstyle="outline-warning",
            width=25
        )
        clear_btn.pack(side=RIGHT, padx=5)
        
        # زر تحديث
        refresh_btn = ttk_bs.Button(
            tools_frame,
            text="🔄 تحديث",
            command=self.load_dashboard_data,
            bootstyle="outline-primary",
            width=15
        )
        refresh_btn.pack(side=RIGHT, padx=5)
        
        # بطاقات الإحصائيات
        self.create_stats_cards()
        
        # إضافة وظيفة البحث
        self.add_search_functionality()

        # جدول بيانات الأصناف
        self.create_items_table()

        # إنشاء اختصارات لوحة المفاتيح
        self.create_keyboard_shortcuts()

        # بدء التحديث التلقائي
        self.refresh_data_auto()

        # تحميل البيانات عند فتح النافذة
        self.load_dashboard_data()
        print("✅ تم تحميل لوحة تحكم المخزون")

        # تسجيل النافذة في النظام العام
        try:
            from ui.add_inventory_movement_window import register_inventory_window
            register_inventory_window(self)
        except Exception as e:
            print(f"⚠️ خطأ في تسجيل لوحة تحكم المخزون: {e}")
    
    def create_stats_cards(self):
        """إنشاء بطاقات الإحصائيات"""
        cards_frame = ttk_bs.Frame(self.main_window.main_frame)
        cards_frame.pack(fill=X, pady=20)

        # بطاقة إجمالي الأصناف (يمين)
        total_card = ttk_bs.Frame(cards_frame, bootstyle="primary")
        total_card.pack(side=RIGHT, fill=BOTH, expand=True, padx=10)

        total_content = ttk_bs.Frame(total_card, bootstyle="light")
        total_content.pack(fill=BOTH, expand=True, padx=3, pady=3)

        ttk_bs.Label(
            total_content,
            text="إجمالي الأصناف",
            bootstyle="secondary",
            anchor=CENTER
        ).pack(pady=5)

        # أيقونة إجمالي الأصناف
        ttk_bs.Label(
            total_content,
            text="📦",
            font=("Arial", 24),
            bootstyle="primary",
            anchor=CENTER
        ).pack(pady=5)

        self.total_items_var = tk.StringVar(value="0")
        ttk_bs.Label(
            total_content,
            textvariable=self.total_items_var,
            font=("Arial", 18, "bold"),
            bootstyle="primary",
            anchor=CENTER
        ).pack(pady=5)

        ttk_bs.Button(
            total_content,
            text="عرض الأصناف",
            command=self.show_all_items,
            bootstyle="primary",
            width=20
        ).pack(pady=10)

        # بطاقة الأصناف منخفضة المخزون (وسط)
        low_stock_card = ttk_bs.Frame(cards_frame, bootstyle="warning")
        low_stock_card.pack(side=RIGHT, fill=BOTH, expand=True, padx=10)

        low_content = ttk_bs.Frame(low_stock_card, bootstyle="light")
        low_content.pack(fill=BOTH, expand=True, padx=3, pady=3)

        ttk_bs.Label(
            low_content,
            text="أصناف منخفضة المخزون",
            bootstyle="secondary",
            anchor=CENTER
        ).pack(pady=5)

        # أيقونة الأصناف منخفضة المخزون
        ttk_bs.Label(
            low_content,
            text="⚠️",
            font=("Arial", 24),
            bootstyle="warning",
            anchor=CENTER
        ).pack(pady=5)

        self.low_stock_items_var = tk.StringVar(value="0")
        ttk_bs.Label(
            low_content,
            textvariable=self.low_stock_items_var,
            font=("Arial", 18, "bold"),
            bootstyle="warning",
            anchor=CENTER
        ).pack(pady=5)

        ttk_bs.Button(
            low_content,
            text="عرض الأصناف المنخفضة",
            command=self.show_low_stock_items,
            bootstyle="warning",
            width=22
        ).pack(pady=10)

        # بطاقة حركة المخزون (يسار)
        movement_card = ttk_bs.Frame(cards_frame, bootstyle="info")
        movement_card.pack(side=RIGHT, fill=BOTH, expand=True, padx=10)

        movement_content = ttk_bs.Frame(movement_card, bootstyle="light")
        movement_content.pack(fill=BOTH, expand=True, padx=3, pady=3)

        ttk_bs.Label(
            movement_content,
            text="حركة المخزون",
            bootstyle="secondary",
            anchor=CENTER
        ).pack(pady=5)

        # أيقونة حركة المخزون
        ttk_bs.Label(
            movement_content,
            text="🔄",
            font=("Arial", 24),
            bootstyle="info",
            anchor=CENTER
        ).pack(pady=5)

        ttk_bs.Button(
            movement_content,
            text="عرض حركة المخزون",
            command=self.show_inventory_movements,
            bootstyle="info",
            width=22
        ).pack(pady=10)
    
    def create_items_table(self):
        """إنشاء جدول بيانات الأصناف"""
        table_frame = ttk_bs.LabelFrame(
            self.main_window.main_frame,
            text="📋 بيانات الأصناف",
            bootstyle="primary"
        )
        table_frame.pack(fill=BOTH, expand=True, pady=20)

        # إنشاء Treeview مع الأعمدة المطلوبة
        columns = ("sequence", "item_number", "item_name", "custody_type", "classification",
                  "entered_qty", "dispensed_qty", "current_qty", "unit")
        self.items_tree = ttk.Treeview(
            table_frame,
            columns=columns,
            show="headings",
            height=12
        )

        # تعيين عناوين الأعمدة
        headings = {
            "sequence": "تسلسل",
            "item_number": "رقم الصنف",
            "item_name": "اسم الصنف",
            "custody_type": "نوع العهدة",
            "classification": "التصنيف",
            "entered_qty": "الكمية المدخلة",
            "dispensed_qty": "الكمية المصروفة",
            "current_qty": "الكمية الحالية",
            "unit": "وحدة الصرف"
        }
        
        for col, heading in headings.items():
            self.items_tree.heading(col, text=heading)
            self.items_tree.column(col, width=120, anchor="center")
        
        # تعيين عرض أعمدة محددة
        self.items_tree.column("sequence", width=80)
        self.items_tree.column("item_number", width=120)
        self.items_tree.column("item_name", width=200)
        self.items_tree.column("custody_type", width=120)
        self.items_tree.column("classification", width=120)
        self.items_tree.column("entered_qty", width=120)
        self.items_tree.column("dispensed_qty", width=120)
        self.items_tree.column("current_qty", width=120)
        self.items_tree.column("unit", width=100)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient=VERTICAL, command=self.items_tree.yview)
        self.items_tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.items_tree.pack(side=LEFT, fill=BOTH, expand=True, padx=10, pady=10)
        scrollbar.pack(side=RIGHT, fill=Y, pady=10)
        
        # زر عرض جميع الأصناف
        button_frame = ttk_bs.Frame(table_frame)
        button_frame.pack(fill=X, padx=10, pady=5)
        
        ttk_bs.Button(
            button_frame,
            text="عرض جميع الأصناف",
            command=self.show_all_items_detailed,
            bootstyle="outline-primary",
            width=22
        ).pack()
        
        # ربط الأحداث
        self.items_tree.bind('<Double-1>', self.on_item_double_click)
        self.items_tree.bind('<Return>', self.on_item_double_click)  # Enter key

        # إنشاء قائمة السياق
        self.create_context_menu()
    
    def load_dashboard_data(self):
        """تحميل بيانات لوحة التحكم"""
        try:
            # تحديث شريط الحالة
            if hasattr(self.main_window, 'status_var'):
                self.main_window.status_var.set("جاري تحميل بيانات المخزون...")

            # تحميل الأصناف المضافة من شاشة إضافة الأصناف (فقط)
            added_items = []
            try:
                added_items = AddedItem.get_all()
                print(f"✅ تم تحميل {len(added_items)} صنف مضاف")
            except Exception as e:
                print(f"خطأ في تحميل الأصناف المضافة: {e}")
                # إنشاء بيانات تجريبية إذا لم توجد بيانات
                added_items = self.create_sample_added_items()

            # تحويل الأصناف إلى تنسيق العرض مع حساب الكميات
            display_items = self.convert_added_items_to_display(added_items)

            # حساب الإحصائيات بناءً على الكمية الحالية المحسوبة
            total_items = len(display_items)
            low_stock_count = 0

            # حساب الأصناف منخفضة المخزون بناءً على الكمية الحالية
            for item in display_items:
                if hasattr(item, 'current_quantity') and item.current_quantity <= 5:
                    low_stock_count += 1

            # تحديث البطاقات
            self.total_items_var.set(str(total_items))
            self.low_stock_items_var.set(str(low_stock_count))

            # تحديث الجدول
            self.update_items_table(display_items)

            print(f"✅ تم تحديث لوحة التحكم: {total_items} صنف، {low_stock_count} منخفض")

            # تحديث شريط الحالة
            if hasattr(self.main_window, 'status_var'):
                self.main_window.status_var.set(f"تم تحميل {total_items} صنف - منخفض: {low_stock_count}")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل بيانات لوحة التحكم: {e}")
            if hasattr(self.main_window, 'status_var'):
                self.main_window.status_var.set("خطأ في تحميل البيانات")

    def create_sample_data(self):
        """إنشاء بيانات تجريبية للعرض"""
        sample_data = []

        # البيانات التجريبية معطلة - ستبدأ قائمة الأصناف فارغة
        # sample_items = [
        #     {
        #         'sequence_number': 1,
        #         'item_number': '708002',
        #         'item_name': 'جهاز حاسب آلي',
        #         'equipment_name': 'كمبيوتر',
        #         'category': 'أجهزة تقنيات',
        #         'current_quantity': 49,
        #         'unit': 'عدد',
        #         'is_active': True
        #     },
        #     {
        #         'sequence_number': 2,
        #         'item_number': '708003',
        #         'item_name': 'طابعة ليزر',
        #         'equipment_name': 'طابعة',
        #         'category': 'أجهزة مكتبية',
        #         'current_quantity': 25,
        #         'unit': 'عدد',
        #         'is_active': True
        #     },
        #     {
        #         'sequence_number': 3,
        #         'item_number': '708004',
        #         'item_name': 'شاشة عرض',
        #         'equipment_name': 'شاشة',
        #         'category': 'أجهزة تقنيات',
        #         'current_quantity': 15,
        #         'unit': 'عدد',
        #         'is_active': True
        #     },
        #     {
        #         'sequence_number': 4,
        #         'item_number': '708005',
        #         'item_name': 'لوحة مفاتيح',
        #         'equipment_name': 'كيبورد',
        #         'category': 'ملحقات',
        #         'current_quantity': 100,
        #         'unit': 'عدد',
        #         'is_active': True
        #     },
        #     {
        #         'sequence_number': 5,
        #         'item_number': '708006',
        #         'item_name': 'فأرة حاسوب',
        #         'equipment_name': 'ماوس',
        #         'category': 'ملحقات',
        #         'current_quantity': 80,
        #         'unit': 'عدد',
        #         'is_active': True
        #     }
        # ]

        # تحويل البيانات إلى كائنات (معطل)
        # class SampleItem:
        #     def __init__(self, data):
        #         for key, value in data.items():
        #             setattr(self, key, value)

        # for item_data in sample_items:
        #     sample_data.append(SampleItem(item_data))

        return sample_data

    def get_dispensed_quantity_for_item(self, item_number):
        """حساب الكمية المصروفة لصنف معين من حركات المخزون"""
        try:
            from database import db_manager

            # حساب مجموع الكميات المصروفة من جدول حركات المخزون الجديد
            query = """
                SELECT COALESCE(SUM(quantity), 0) as total_dispensed
                FROM inventory_movements_new
                WHERE item_number = ? AND movement_type = 'صرف' AND is_active = 1
            """
            result = db_manager.fetch_one(query, (item_number,))

            if result and result['total_dispensed']:
                return float(result['total_dispensed'])
            else:
                return 0.0

        except Exception as e:
            print(f"خطأ في حساب الكمية المصروفة للصنف {item_number}: {e}")
            return 0.0

    def get_entered_quantity_for_item(self, item_number):
        """حساب الكمية المدخلة لصنف معين من حركات المخزون"""
        try:
            from database import db_manager

            # حساب مجموع الكميات المدخلة من جدول حركات المخزون الجديد
            query = """
                SELECT COALESCE(SUM(quantity), 0) as total_entered
                FROM inventory_movements_new
                WHERE item_number = ? AND movement_type = 'إضافة' AND is_active = 1
            """
            result = db_manager.fetch_one(query, (item_number,))

            if result and result['total_entered']:
                return float(result['total_entered'])
            else:
                return 0.0

        except Exception as e:
            print(f"خطأ في حساب الكمية المدخلة للصنف {item_number}: {e}")
            return 0.0

    def get_total_additions_for_item(self, item_number):
        """حساب إجمالي الإضافات للصنف من حركات المخزون"""
        try:
            query = """
                SELECT COALESCE(SUM(quantity), 0)
                FROM inventory_movements_new
                WHERE item_number = ? AND movement_type = 'إضافة' AND is_active = 1
            """
            result = db_manager.fetch_one(query, (item_number,))
            return float(result[0]) if result else 0.0
        except Exception as e:
            print(f"خطأ في حساب إجمالي الإضافات للصنف {item_number}: {e}")
            return 0.0

    def convert_added_items_to_display(self, added_items):
        """تحويل الأصناف المضافة إلى تنسيق العرض مع حساب الكميات المحدث"""
        display_items = []

        for index, item in enumerate(added_items, 1):
            # حساب الكمية المصروفة من جدول حركات المخزون
            dispensed_qty = self.get_dispensed_quantity_for_item(item.item_number)

            # حساب إجمالي الإضافات من حركات المخزون (بما في ذلك الاستيراد)
            total_additions = self.get_total_additions_for_item(item.item_number)

            # الكمية المدخلة = إجمالي الإضافات (بما في ذلك الاستيراد الأولي)
            # إذا لم توجد حركات، نستخدم الكمية الأصلية من الجدول
            entered_qty = total_additions if total_additions > 0 else item.current_quantity

            # حساب الكمية الحالية الفعلية (إجمالي الإضافات - المصروفة)
            current_qty = entered_qty - dispensed_qty

            # إنشاء كائن للعرض
            class DisplayItem:
                def __init__(self, added_item, sequence, entered, dispensed, current):
                    self.sequence_number = sequence
                    self.item_number = added_item.item_number
                    self.item_name = added_item.item_name
                    self.custody_type = added_item.custody_type
                    self.classification = added_item.classification
                    self.entered_quantity = entered  # الكمية المدخلة (إجمالي الإضافات)
                    self.dispensed_quantity = dispensed  # الكمية المصروفة
                    self.current_quantity = current  # الكمية الحالية المحسوبة
                    self.unit = added_item.unit
                    self.is_active = added_item.is_active

            display_items.append(DisplayItem(item, index, entered_qty, dispensed_qty, current_qty))

        return display_items

    def create_sample_added_items(self):
        """إنشاء بيانات تجريبية للأصناف المضافة (معطل - يبدأ فارغ)"""
        # تم تعطيل البيانات التجريبية - ستبدأ قائمة الأصناف فارغة
        # يمكن للمستخدم إضافة الأصناف حسب الحاجة
        sample_data = []

        # البيانات التجريبية معطلة
        # sample_items_data = [
        #     {
        #         'id': 1,
        #         'item_number': '708001',
        #         'item_name': 'جهاز حاسب آلي',
        #         'custody_type': 'مستديمة',
        #         'classification': 'أجهزة كهربائية',
        #         'unit': 'عدد',
        #         'current_quantity': 5,
        #         'data_entry_user': 'المدير',
        #         'entry_date': '2024-01-15',
        #         'is_active': True
        #     },
        #     {
        #         'id': 2,
        #         'item_number': '708002',
        #         'item_name': 'طابعة ليزر',
        #         'custody_type': 'مستديمة',
        #         'classification': 'أجهزة مكتبية',
        #         'unit': 'عدد',
        #         'current_quantity': 3,
        #         'data_entry_user': 'المدير',
        #         'entry_date': '2024-01-16',
        #         'is_active': True
        #     }
        # ]

        # تحويل البيانات إلى كائنات (معطل)
        # class SampleAddedItem:
        #     def __init__(self, data):
        #         for key, value in data.items():
        #             setattr(self, key, value)

        # for item_data in sample_items_data:
        #     sample_data.append(SampleAddedItem(item_data))

        return sample_data
    
    def update_items_table(self, items):
        """تحديث جدول الأصناف مع البيانات الجديدة"""
        # مسح البيانات الحالية
        for item in self.items_tree.get_children():
            self.items_tree.delete(item)

        # إضافة البيانات الجديدة مع ألوان مختلفة حسب الحالة
        for item in items:
            current_qty = getattr(item, 'current_quantity', 0)
            entered_qty = getattr(item, 'entered_quantity', 0)
            dispensed_qty = getattr(item, 'dispensed_quantity', 0)

            # تحديد لون الصف حسب الكمية الحالية
            tags = []
            if isinstance(current_qty, (int, float)):
                if current_qty <= 0:
                    tags.append("out_of_stock")
                elif current_qty <= 5:
                    tags.append("low_stock")
                elif current_qty >= 50:
                    tags.append("high_stock")
                else:
                    tags.append("normal_stock")

            # إضافة تاج للحالة
            if not getattr(item, 'is_active', True):
                tags.append("inactive")

            # إدراج البيانات في الجدول
            item_id = self.items_tree.insert('', 'end', values=(
                getattr(item, 'sequence_number', ''),
                getattr(item, 'item_number', ''),
                getattr(item, 'item_name', ''),
                getattr(item, 'custody_type', ''),
                getattr(item, 'classification', ''),
                str(int(entered_qty)),  # الكمية المدخلة
                str(int(dispensed_qty)),  # الكمية المصروفة
                str(int(current_qty)),  # الكمية الحالية
                getattr(item, 'unit', 'قطعة')
            ), tags=tags)

        # تعيين ألوان الصفوف
        self.items_tree.tag_configure("out_of_stock", background="#ffebee", foreground="#c62828")
        self.items_tree.tag_configure("low_stock", background="#fff3e0", foreground="#ef6c00")
        self.items_tree.tag_configure("normal_stock", background="#e8f5e8", foreground="#2e7d32")
        self.items_tree.tag_configure("high_stock", background="#e3f2fd", foreground="#1565c0")
        self.items_tree.tag_configure("inactive", background="#f5f5f5", foreground="#757575")
    
    def on_item_double_click(self, event):
        """معالج النقر المزدوج على صنف"""
        selection = self.items_tree.selection()
        if selection:
            item = self.items_tree.item(selection[0])
            values = item['values']

            # عرض تفاصيل الصنف مع البيانات الجديدة
            details = f"""
تفاصيل الصنف:

تسلسل: {values[0]}
رقم الصنف: {values[1]}
اسم الصنف: {values[2]}
نوع العهدة: {values[3]}
التصنيف: {values[4]}
الكمية المدخلة: {values[5]}
الكمية المصروفة: {values[6]}
الكمية الحالية: {values[7]}
وحدة الصرف: {values[8]}
            """
            messagebox.showinfo("تفاصيل الصنف", details)

    def create_context_menu(self):
        """إنشاء قائمة السياق للجدول"""
        self.context_menu = tk.Menu(self.main_window.parent, tearoff=0)

        # إضافة عناصر القائمة
        self.context_menu.add_command(
            label="➕ إضافة حركة مخزون",
            command=self.add_movement_for_selected
        )

        self.context_menu.add_separator()

        self.context_menu.add_command(
            label="📋 عرض التفاصيل",
            command=self.show_item_details
        )

        self.context_menu.add_command(
            label="✏️ تعديل",
            command=self.edit_selected_item
        )

        self.context_menu.add_command(
            label="🔄 عرض الحركة",
            command=self.show_item_movements
        )

        self.context_menu.add_command(
            label="📊 الإحصائيات",
            command=self.show_item_statistics
        )

        self.context_menu.add_separator()

        self.context_menu.add_command(
            label="🖨️ طباعة",
            command=self.print_item_data
        )

        # ربط قائمة السياق بالجدول
        self.items_tree.bind("<Button-3>", self.show_context_menu)  # Right click

    def show_context_menu(self, event):
        """عرض قائمة السياق"""
        try:
            # تحديد العنصر المحدد
            item = self.items_tree.identify_row(event.y)
            if item:
                self.items_tree.selection_set(item)
                self.items_tree.focus(item)
                # عرض القائمة
                self.context_menu.post(event.x_root, event.y_root)
        except Exception as e:
            print(f"خطأ في عرض القائمة المنبثقة: {e}")
        finally:
            try:
                self.context_menu.grab_release()
            except:
                pass

    def add_movement_for_selected(self):
        """إضافة حركة مخزون للصنف المحدد"""
        selection = self.items_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار صنف من الجدول أولاً")
            return

        try:
            # الحصول على بيانات الصنف المحدد
            item = self.items_tree.item(selection[0])
            values = item['values']
            item_number = values[1]  # رقم الصنف
            item_name = values[2]    # اسم الصنف

            # فتح شاشة إضافة حركة مخزون للصنف المحدد
            # استخدام النافذة الرئيسية بدلاً من self.parent لتجنب إخفاء لوحة التحكم
            from ui.add_inventory_movement_window import AddInventoryMovementWindow
            AddInventoryMovementWindow(
                self.main_window.parent,  # استخدام النافذة الرئيسية بدلاً من self.parent
                self.main_window,
                item_number,
                item_name
            )
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح شاشة إضافة حركة المخزون: {e}")
            print(f"خطأ في فتح شاشة الحركة: {e}")
            import traceback
            traceback.print_exc()

    def show_item_details(self):
        """عرض تفاصيل الصنف المحدد"""
        selection = self.items_tree.selection()
        if selection:
            self.on_item_double_click(None)

    def edit_selected_item(self):
        """تعديل الصنف المحدد"""
        selection = self.items_tree.selection()
        if selection:
            messagebox.showinfo("تعديل", "سيتم فتح شاشة تعديل الصنف")
            # هنا يمكن فتح شاشة التعديل

    def show_item_movements(self):
        """عرض حركة الصنف المحدد"""
        selection = self.items_tree.selection()
        if selection:
            item = self.items_tree.item(selection[0])
            item_name = item['values'][2]  # اسم الصنف
            messagebox.showinfo("حركة الصنف", f"عرض حركة الصنف: {item_name}")

    def show_item_statistics(self):
        """عرض إحصائيات الصنف"""
        selection = self.items_tree.selection()
        if selection:
            item = self.items_tree.item(selection[0])
            values = item['values']

            stats_text = f"""
إحصائيات الصنف: {values[2]}

تسلسل: {values[0]}
رقم الصنف: {values[1]}
الكمية المدخلة: {values[5]}
الكمية المصروفة: {values[6]}
الكمية الحالية: {values[7]}
وحدة الصرف: {values[8]}

إحصائيات إضافية:
• متوسط الاستهلاك الشهري: غير متوفر
• آخر حركة: غير متوفر
• عدد مرات الصرف: غير متوفر
            """

            messagebox.showinfo("إحصائيات الصنف", stats_text)

    def print_item_data(self):
        """طباعة بيانات الصنف"""
        selection = self.items_tree.selection()
        if selection:
            item = self.items_tree.item(selection[0])
            item_name = item['values'][2]
            messagebox.showinfo("طباعة", f"تم إرسال بيانات الصنف '{item_name}' للطابعة")

    def show_all_items(self):
        """عرض جميع الأصناف"""
        try:
            from ui.inventory_window import InventoryWindow
            # إنشاء نافذة إدارة الأصناف مع تحديث البيانات
            inventory_window = InventoryWindow(self.parent, self.main_window)
            # تحديث البيانات فور فتح النافذة
            inventory_window.refresh_data()
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح شاشة الأصناف: {e}")
    
    def show_all_items_detailed(self):
        """عرض جميع الأصناف بالتفصيل"""
        self.show_all_items()
    
    def show_low_stock_items(self):
        """عرض شاشة حالة المخزون (الأصناف منخفضة المخزون)"""
        try:
            from ui.inventory_status_window import InventoryStatusWindow
            InventoryStatusWindow(self.parent, self.main_window)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح شاشة حالة المخزون: {e}")
    
    def show_inventory_movements(self):
        """عرض شاشة حركة المخزون"""
        try:
            from ui.inventory_movements_window import InventoryMovementsWindow
            InventoryMovementsWindow(self.parent, self.main_window)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح شاشة حركة المخزون: {e}")
    
    def print_inventory_report(self):
        """طباعة تقرير المخزون"""
        try:
            # إنشاء نافذة معاينة التقرير
            report_window = tk.Toplevel(self.parent)
            report_window.title("📊 تقرير المخزون")
            report_window.geometry("800x600")
            report_window.resizable(True, True)

            # توسيط النافذة
            report_window.update_idletasks()
            x = (report_window.winfo_screenwidth() - 800) // 2
            y = (report_window.winfo_screenheight() - 600) // 2
            report_window.geometry(f"800x600+{x}+{y}")

            # محتوى التقرير
            main_frame = ttk_bs.Frame(report_window)
            main_frame.pack(fill=BOTH, expand=True, padx=20, pady=20)

            # عنوان التقرير
            title_label = ttk_bs.Label(
                main_frame,
                text="📊 تقرير حالة المخزون",
                bootstyle="primary"
            )
            title_label.pack(pady=10)

            # تاريخ التقرير
            date_label = ttk_bs.Label(
                main_frame,
                text=f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M')}",
                bootstyle="secondary"
            )
            date_label.pack(pady=5)

            # إحصائيات التقرير
            stats_frame = ttk_bs.LabelFrame(main_frame, text="الإحصائيات العامة", bootstyle="info")
            stats_frame.pack(fill=X, pady=10)

            total_items = self.total_items_var.get()
            low_stock = self.low_stock_items_var.get()

            stats_text = f"""
إجمالي الأصناف: {total_items}
الأصناف منخفضة المخزون: {low_stock}
نسبة الأصناف المنخفضة: {(int(low_stock) / max(int(total_items), 1) * 100):.1f}%
تاريخ آخر تحديث: {datetime.now().strftime('%Y-%m-%d %H:%M')}
            """

            ttk_bs.Label(
                stats_frame,
                text=stats_text,
                bootstyle="secondary",
                justify=RIGHT
            ).pack(pady=10, padx=10)

            # أزرار التقرير
            buttons_frame = ttk_bs.Frame(main_frame)
            buttons_frame.pack(pady=20)

            ttk_bs.Button(
                buttons_frame,
                text="🖨️ طباعة",
                command=lambda: None,  # لا تعرض أي رسالة
                bootstyle="primary",
                width=15
            ).pack(side=LEFT, padx=10)

            ttk_bs.Button(
                buttons_frame,
                text="📤 تصدير PDF",
                command=lambda: None,  # لا تعرض أي رسالة
                bootstyle="success",
                width=20
            ).pack(side=LEFT, padx=10)

            ttk_bs.Button(
                buttons_frame,
                text="❌ إغلاق",
                command=report_window.destroy,
                bootstyle="secondary",
                width=18
            ).pack(side=LEFT, padx=10)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء تقرير المخزون: {e}")

    def clear_inventory_status(self):
        """إخلاء حالة المخزون"""
        if messagebox.askyesno(
            "تأكيد إخلاء المخزون",
            "هل أنت متأكد من إخلاء حالة المخزون؟\n\n"
            "⚠️ تحذير: هذا الإجراء سيقوم بـ:\n"
            "• إعادة تعيين جميع الكميات إلى الصفر\n"
            "• حذف سجلات الحركات القديمة\n"
            "• إنشاء نقطة بداية جديدة للمخزون\n\n"
            "هذا الإجراء لا يمكن التراجع عنه!"
        ):
            # نافذة تأكيد إضافية
            if messagebox.askyesno(
                "تأكيد نهائي",
                "هذا هو التأكيد الأخير!\n\n"
                "هل أنت متأكد تماماً من المتابعة؟"
            ):
                try:
                    # هنا يمكن إضافة كود إخلاء المخزون الفعلي
                    # مثل تحديث قاعدة البيانات

                    # عرض نافذة تقدم العملية
                    progress_window = tk.Toplevel(self.parent)
                    progress_window.title("جاري إخلاء المخزون...")
                    progress_window.geometry("400x150")
                    progress_window.resizable(False, False)

                    # توسيط النافذة
                    progress_window.update_idletasks()
                    x = (progress_window.winfo_screenwidth() - 400) // 2
                    y = (progress_window.winfo_screenheight() - 150) // 2
                    progress_window.geometry(f"400x150+{x}+{y}")

                    progress_frame = ttk_bs.Frame(progress_window)
                    progress_frame.pack(fill=BOTH, expand=True, padx=20, pady=20)

                    ttk_bs.Label(
                        progress_frame,
                        text="🔄 جاري إخلاء حالة المخزون..."
                    ).pack(pady=10)

                    progress_bar = ttk_bs.Progressbar(
                        progress_frame,
                        mode='indeterminate',
                        bootstyle="warning"
                    )
                    progress_bar.pack(fill=X, pady=10)
                    progress_bar.start()

                    # محاكاة عملية الإخلاء
                    def simulate_clear():
                        import time
                        time.sleep(3)  # محاكاة وقت المعالجة
                        progress_window.destroy()
                        messagebox.showinfo(
                            "تم بنجاح",
                            "✅ تم إخلاء حالة المخزون بنجاح!\n\n"
                            "يمكنك الآن البدء بإدخال البيانات الجديدة."
                        )
                        # إعادة تحميل البيانات
                        self.load_dashboard_data()

                    # تشغيل العملية في خيط منفصل
                    threading.Thread(target=simulate_clear, daemon=True).start()

                except Exception as e:
                    messagebox.showerror("خطأ", f"فشل في إخلاء المخزون: {e}")

    def refresh_data_auto(self):
        """تحديث البيانات تلقائياً كل فترة"""
        def auto_refresh():
            while True:
                try:
                    import time
                    time.sleep(300)  # تحديث كل 5 دقائق
                    self.load_dashboard_data()
                except:
                    break

        refresh_thread = threading.Thread(target=auto_refresh, daemon=True)
        refresh_thread.start()

    def export_data_excel(self):
        """تصدير البيانات إلى Excel"""
        try:
            from tkinter import filedialog

            # اختيار مكان الحفظ
            filename = filedialog.asksaveasfilename(
                title="حفظ تقرير المخزون",
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
            )

            if filename:
                # هنا يمكن إضافة كود تصدير Excel الفعلي
                messagebox.showinfo("نجح", f"تم تصدير البيانات إلى:\n{filename}")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصدير البيانات: {e}")




    def add_search_functionality(self):
        """إضافة وظيفة البحث"""
        search_frame = ttk_bs.LabelFrame(
            self.main_window.main_frame,
            text="🔍 البحث والفلترة",
            bootstyle="info"
        )
        search_frame.pack(fill=X, pady=5)

        # الصف الأول - البحث والفئة
        row1 = ttk_bs.Frame(search_frame)
        row1.pack(fill=X, padx=10, pady=5)

        # البحث
        ttk_bs.Label(row1, text="🔍 البحث:").pack(side=RIGHT, padx=5)

        self.search_var = tk.StringVar()
        search_entry = ttk_bs.Entry(
            row1,
            textvariable=self.search_var,
            width=30
        )
        search_entry.pack(side=RIGHT, padx=5)
        search_entry.bind('<KeyRelease>', self.filter_items)

        # الفئة
        ttk_bs.Label(row1, text="الفئة:").pack(side=RIGHT, padx=(20, 5))

        self.category_filter_var = tk.StringVar(value="الكل")
        category_combo = ttk_bs.Combobox(
            row1,
            textvariable=self.category_filter_var,
            values=["الكل", "مستهلكة", "مستديمة", "أخرى"],
            state="readonly",
            width=15
        )
        category_combo.pack(side=RIGHT, padx=5)
        category_combo.bind('<<ComboboxSelected>>', self.filter_items)

        # الصف الثاني - حالة المخزون والحالة
        row2 = ttk_bs.Frame(search_frame)
        row2.pack(fill=X, padx=10, pady=5)

        # حالة المخزون
        ttk_bs.Label(row2, text="حالة المخزون:").pack(side=RIGHT, padx=5)

        self.stock_filter_var = tk.StringVar(value="الكل")
        stock_combo = ttk_bs.Combobox(
            row2,
            textvariable=self.stock_filter_var,
            values=["الكل", "متوفر", "منخفض", "نفد"],
            state="readonly",
            width=15
        )
        stock_combo.pack(side=RIGHT, padx=5)
        stock_combo.bind('<<ComboboxSelected>>', self.filter_items)

        # الحالة
        ttk_bs.Label(row2, text="الحالة:").pack(side=RIGHT, padx=(20, 5))

        self.status_filter_var = tk.StringVar(value="الكل")
        status_combo = ttk_bs.Combobox(
            row2,
            textvariable=self.status_filter_var,
            values=["الكل", "نشط", "غير نشط"],
            state="readonly",
            width=15
        )
        status_combo.pack(side=RIGHT, padx=5)
        status_combo.bind('<<ComboboxSelected>>', self.filter_items)

        # زر مسح الفلاتر
        ttk_bs.Button(
            row2,
            text="🗑️ مسح الفلاتر",
            command=self.clear_search,
            bootstyle="outline-warning",
            width=18
        ).pack(side=LEFT, padx=5)

    def filter_items(self, event=None):
        """فلترة الأصناف حسب المعايير المختلفة"""
        try:
            search_text = self.search_var.get().lower() if hasattr(self, 'search_var') else ""
            category_filter = self.category_filter_var.get() if hasattr(self, 'category_filter_var') else "الكل"
            stock_filter = self.stock_filter_var.get() if hasattr(self, 'stock_filter_var') else "الكل"
            status_filter = self.status_filter_var.get() if hasattr(self, 'status_filter_var') else "الكل"

            # تحميل البيانات الأصلية
            added_items = []
            try:
                added_items = AddedItem.get_all()
            except Exception as e:
                print(f"خطأ في تحميل الأصناف المضافة: {e}")
                added_items = self.create_sample_added_items()

            # تحويل إلى تنسيق العرض
            display_items = self.convert_added_items_to_display(added_items)

            # تطبيق الفلاتر
            filtered_items = []
            for item in display_items:
                # فلتر البحث
                if search_text:
                    item_text = f"{getattr(item, 'item_name', '')} {getattr(item, 'item_number', '')}".lower()
                    if search_text not in item_text:
                        continue

                # فلتر الفئة (نوع العهدة)
                if category_filter != "الكل":
                    item_category = getattr(item, 'custody_type', 'مستهلكة')
                    if item_category != category_filter:
                        continue

                # فلتر حالة المخزون
                if stock_filter != "الكل":
                    current_qty = getattr(item, 'current_quantity', 1)
                    if isinstance(current_qty, (int, float)):
                        if stock_filter == "نفد" and current_qty > 0:
                            continue
                        elif stock_filter == "منخفض" and current_qty > 5:
                            continue
                        elif stock_filter == "متوفر" and current_qty <= 5:
                            continue

                # فلتر الحالة
                if status_filter != "الكل":
                    is_active = getattr(item, 'is_active', True)
                    if status_filter == "نشط" and not is_active:
                        continue
                    elif status_filter == "غير نشط" and is_active:
                        continue

                filtered_items.append(item)

            # تحديث الجدول بالبيانات المفلترة
            self.update_items_table(filtered_items)

            # تحديث شريط الحالة
            if hasattr(self.main_window, 'status_var'):
                total_count = len(display_items)
                filtered_count = len(filtered_items)
                self.main_window.status_var.set(f"عرض {filtered_count} من {total_count} صنف")

        except Exception as e:
            print(f"خطأ في الفلترة: {e}")
            self.load_dashboard_data()

    def clear_search(self):
        """مسح جميع الفلاتر"""
        if hasattr(self, 'search_var'):
            self.search_var.set("")
        if hasattr(self, 'category_filter_var'):
            self.category_filter_var.set("الكل")
        if hasattr(self, 'stock_filter_var'):
            self.stock_filter_var.set("الكل")
        if hasattr(self, 'status_filter_var'):
            self.status_filter_var.set("الكل")

        self.load_dashboard_data()

    def create_keyboard_shortcuts(self):
        """إنشاء اختصارات لوحة المفاتيح"""
        # F5 للتحديث
        self.main_window.parent.bind("<F5>", lambda e: self.load_dashboard_data())

        # Ctrl+P للطباعة
        self.main_window.parent.bind("<Control-p>", lambda e: self.print_inventory_report())

        # Ctrl+E للتصدير
        self.main_window.parent.bind("<Control-e>", lambda e: self.export_data_excel())

        # Delete لحذف العنصر المحدد
        self.items_tree.bind("<Delete>", self.delete_selected_item)

    def check_item_can_be_deleted(self, item_id):
        """التحقق من إمكانية حذف الصنف"""
        try:
            from database import db_manager

            # التحقق من وجود عمليات صرف مرتبطة بالصنف
            # البحث في جدول transaction_items مباشرة باستخدام item_id
            transactions_count = db_manager.fetch_one("""
                SELECT COUNT(DISTINCT transaction_id) as count
                FROM transaction_items
                WHERE item_id = ?
            """, (item_id,))

            if transactions_count and transactions_count['count'] > 0:
                # عرض رسالة خطأ باللون الأحمر
                self.show_error_message("الصنف مُسلم في عمليات صرف لمستفيدين ولا يمكن حذفه نهائياً")
                return False

            return True

        except Exception as e:
            print(f"خطأ في التحقق من إمكانية الحذف: {e}")
            # تسجيل الخطأ بدون عرض رسالة للمستخدم
            try:
                from utils.logger import setup_logger
                logger = setup_logger()
                logger.error(f"خطأ في التحقق من إمكانية الحذف: {e}")
            except:
                pass
            return False

    def show_error_message(self, message):
        """عرض رسالة خطأ باللون الأحمر تختفي بعد 3 ثواني"""
        # إنشاء نافذة رسالة مؤقتة
        error_window = tk.Toplevel(self.parent)
        error_window.title("تحذير")
        error_window.configure(bg='white')
        error_window.resizable(False, False)

        # تحديد موقع النافذة في وسط الشاشة
        error_window.geometry("400x100")
        error_window.transient(self.parent)
        error_window.grab_set()

        # إنشاء إطار للرسالة
        message_frame = tk.Frame(error_window, bg='white', padx=20, pady=20)
        message_frame.pack(fill=tk.BOTH, expand=True)

        # عرض الرسالة باللون الأحمر
        message_label = tk.Label(
            message_frame,
            text=message,
            font=("Arial", 12, "bold"),
            fg="red",
            bg="white",
            wraplength=350,
            justify="center"
        )
        message_label.pack(expand=True)

        # إغلاق النافذة بعد 3 ثواني
        error_window.after(3000, error_window.destroy)

        # إغلاق النافذة عند النقر في أي مكان
        def close_on_click(event):
            error_window.destroy()

        error_window.bind("<Button-1>", close_on_click)
        message_label.bind("<Button-1>", close_on_click)

    def delete_selected_item(self, event=None):
        """حذف العنصر المحدد"""
        selection = self.items_tree.selection()
        if selection:
            item = self.items_tree.item(selection[0])
            item_id = item['values'][0]  # معرف الصنف
            item_name = item['values'][2]  # اسم الصنف

            # التحقق من إمكانية حذف الصنف
            if not self.check_item_can_be_deleted(item_id):
                return

            if messagebox.askyesno(
                "تأكيد الحذف",
                f"هل أنت متأكد من حذف الصنف '{item_name}'؟\n\nهذا الإجراء لا يمكن التراجع عنه."
            ):
                try:
                    from database import db_manager

                    # الحصول على بيانات الصنف قبل الحذف لتسجيل الحركة
                    item_data = db_manager.fetch_one("""
                        SELECT item_number, item_name, current_quantity
                        FROM added_items WHERE id = ?
                    """, (item_id,))

                    if item_data:
                        # تسجيل حركة الحذف في جدول حركات المخزون
                        self.record_delete_movement(item_data)

                    # حذف من جدول الأصناف المضافة
                    db_manager.execute_query("DELETE FROM added_items WHERE id = ?", (item_id,))
                    messagebox.showinfo("تم الحذف", f"تم حذف الصنف '{item_name}' بنجاح")
                    self.load_dashboard_data()
                except Exception as e:
                    messagebox.showerror("خطأ", f"حدث خطأ: {e}")

    def record_delete_movement(self, item_data):
        """تسجيل حركة الحذف في جدول حركات المخزون"""
        try:
            from database import db_manager
            from datetime import datetime

            # الحصول على معرف المستخدم الحالي
            user_id = None
            try:
                from auth_manager import auth_manager
                if auth_manager.current_user:
                    user_id = auth_manager.current_user.id
            except:
                user_id = 1  # المستخدم الافتراضي

            # تسجيل حركة الحذف
            notes = f"حذف الصنف: {item_data['item_name']} (الكمية المحذوفة: {item_data['current_quantity']})"

            db_manager.execute_query("""
                INSERT INTO inventory_movements_new
                (item_number, movement_type, quantity, organization_type,
                 organization_name, notes, user_id, movement_date, is_active)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                item_data['item_number'],    # رقم الصنف
                "حذف",                       # نوع الحركة
                -item_data['current_quantity'],  # الكمية المحذوفة (سالبة)
                "حذف بيانات",               # نوع المنظمة
                "إدارة النظام",             # اسم المنظمة
                notes,                      # الملاحظات
                user_id,                    # معرف المستخدم
                datetime.now(),             # تاريخ الحركة
                1                           # نشط
            ))

            print("✅ تم تسجيل حركة الحذف بنجاح")

        except Exception as e:
            print(f"❌ خطأ في تسجيل حركة الحذف: {e}")
            import traceback
            traceback.print_exc()

# اختبار النافذة
if __name__ == "__main__":
    root = tk.Tk()
    root.withdraw()  # إخفاء النافذة الرئيسية

    # إنشاء نافذة الاختبار
    window = InventoryDashboardWindow(root, None)
    root.mainloop()
