"""
شاشة البداية - تطبيق إدارة المخازن
Splash Screen - Desktop Stores Management System
"""

import tkinter as tk
from tkinter import ttk
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from pathlib import Path
import threading
import time

from config import APP_CONFIG, UI_CONFIG

class SplashScreen:
    """شاشة البداية للتطبيق"""
    
    def __init__(self, parent):
        self.parent = parent
        self.splash_window = None
        self.progress_var = None
        self.status_var = None
        self.setup_splash()
    
    def setup_splash(self):
        """إعداد شاشة البداية"""
        # إنشاء نافذة منبثقة
        self.splash_window = tk.Toplevel(self.parent)
        self.splash_window.title("")
        self.splash_window.geometry("500x300")
        self.splash_window.resizable(False, False)
        
        # إزالة شريط العنوان
        self.splash_window.overrideredirect(True)
        
        # توسيط النافذة
        self.center_window()
        
        # إعداد المحتوى
        self.setup_content()
        
        # جعل النافذة في المقدمة
        self.splash_window.lift()
        self.splash_window.attributes("-topmost", True)
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        # تحديث النافذة أولاً
        self.splash_window.update_idletasks()

        # الحصول على أبعاد الشاشة
        screen_width = self.splash_window.winfo_screenwidth()
        screen_height = self.splash_window.winfo_screenheight()

        # أبعاد النافذة
        window_width = 500
        window_height = 300

        # حساب موضع النافذة للتوسيط المثالي
        x = max(0, (screen_width - window_width) // 2)
        y = max(0, (screen_height - window_height) // 2)

        # تعيين موضع النافذة مع التأكد من التوسيط
        self.splash_window.geometry(f"{window_width}x{window_height}+{x}+{y}")

        # تحديث إضافي للتأكد من التوسيط
        self.splash_window.update()

        # إعادة حساب التوسيط بعد التحديث
        self.splash_window.update_idletasks()
        actual_x = (screen_width - window_width) // 2
        actual_y = (screen_height - window_height) // 2
        self.splash_window.geometry(f"{window_width}x{window_height}+{actual_x}+{actual_y}")
    
    def setup_content(self):
        """إعداد محتوى شاشة البداية"""
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.splash_window, bootstyle="primary")
        main_frame.pack(fill=BOTH, expand=True, padx=2, pady=2)
        
        # إطار المحتوى
        content_frame = ttk_bs.Frame(main_frame, bootstyle="light")
        content_frame.pack(fill=BOTH, expand=True, padx=2, pady=2)
        
        # شعار التطبيق
        logo_frame = ttk_bs.Frame(content_frame)
        logo_frame.pack(pady=20)
        
        # محاولة تحميل الشعار
        try:
            logo_path = Path(__file__).parent.parent / "assets" / "icons" / "logo.png"
            if logo_path.exists():
                from PIL import Image, ImageTk
                image = Image.open(logo_path)
                image = image.resize((80, 80), Image.Resampling.LANCZOS)
                self.logo_image = ImageTk.PhotoImage(image)
                logo_label = ttk_bs.Label(logo_frame, image=self.logo_image)
                logo_label.pack()
        except:
            # إذا فشل تحميل الشعار، استخدم أيقونة نصية
            logo_label = ttk_bs.Label(
                logo_frame,
                text="🏪",
                bootstyle="primary"
            )
            logo_label.pack()
        
        # عنوان التطبيق
        title_label = ttk_bs.Label(
            content_frame,
            text=APP_CONFIG["app_name"],
            bootstyle="primary"
        )
        title_label.pack(pady=10)
        
        # إصدار التطبيق
        version_label = ttk_bs.Label(
            content_frame,
            text=f"الإصدار {APP_CONFIG['app_version']}",
            bootstyle="secondary"
        )
        version_label.pack()
        
        # شريط التقدم
        progress_frame = ttk_bs.Frame(content_frame)
        progress_frame.pack(pady=20, padx=40, fill=X)
        
        self.progress_var = tk.DoubleVar()
        progress_bar = ttk_bs.Progressbar(
            progress_frame,
            variable=self.progress_var,
            maximum=100,
            bootstyle="primary-striped",
            length=400
        )
        progress_bar.pack(fill=X)
        
        # حالة التحميل
        self.status_var = tk.StringVar(value="جاري التحميل...")
        status_label = ttk_bs.Label(
            content_frame,
            textvariable=self.status_var,
            bootstyle="secondary"
        )
        status_label.pack(pady=5)
        
        # معلومات المطور
        developer_frame = ttk_bs.Frame(content_frame)
        developer_frame.pack(side=BOTTOM, pady=10)
        
        developer_label = ttk_bs.Label(
            developer_frame,
            text=f"تطوير: {APP_CONFIG['app_author']}",
            bootstyle="secondary"
        )
        developer_label.pack()
        
        contact_label = ttk_bs.Label(
            developer_frame,
            text=f"للتواصل: {APP_CONFIG['app_contact']}",
            bootstyle="secondary"
        )
        contact_label.pack()
    
    def show(self):
        """عرض شاشة البداية"""
        if self.splash_window:
            self.splash_window.deiconify()
            self.splash_window.focus_force()
    
    def hide(self):
        """إخفاء شاشة البداية"""
        if self.splash_window:
            self.splash_window.withdraw()
    
    def destroy(self):
        """إغلاق شاشة البداية"""
        if self.splash_window:
            self.splash_window.destroy()
            self.splash_window = None
    
    def update_progress(self, value: float, status: str = None):
        """تحديث شريط التقدم"""
        if self.progress_var:
            self.progress_var.set(value)
        
        if status and self.status_var:
            self.status_var.set(status)
        
        if self.splash_window:
            self.splash_window.update()
    
    def simulate_loading(self, callback=None):
        """محاكاة عملية التحميل"""
        def loading_thread():
            steps = [
                (10, "تحميل الإعدادات..."),
                (25, "الاتصال بقاعدة البيانات..."),
                (40, "تحميل البيانات الأساسية..."),
                (60, "إعداد واجهة المستخدم..."),
                (80, "تحميل الوحدات..."),
                (95, "الانتهاء من التحميل..."),
                (100, "مرحباً بك!")
            ]
            
            for progress, status in steps:
                self.update_progress(progress, status)
                time.sleep(0.3)  # تأخير للمحاكاة
            
            # استدعاء الدالة المرجعية إذا كانت موجودة
            if callback:
                callback()
        
        # بدء خيط التحميل
        loading_thread = threading.Thread(target=loading_thread, daemon=True)
        loading_thread.start()
        
        return loading_thread
