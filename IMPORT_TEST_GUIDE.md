# دليل استخدام شاشة اختبار استيراد المستفيدين
## Beneficiaries Import Test Window Guide

### 🎯 الهدف من الشاشة:
هذه الشاشة مصممة خصيصاً لاختبار وتشخيص مشاكل استيراد المستفيدين من ملفات Excel.

### 🚀 كيفية التشغيل:

#### الطريقة الأولى - تشغيل مباشر:
```bash
python run_import_test.py
```

#### الطريقة الثانية - تشغيل الملف الأساسي:
```bash
python test_beneficiaries_import_window.py
```

### 🔧 مميزات الشاشة:

#### 1. **إنشاء ملف Excel للاختبار** 📝
- ينشئ ملف Excel يحتوي على بيانات تجريبية
- يتضمن 3 مستفيدين للاختبار
- يحفظ الملف في المكان الذي تختاره

#### 2. **استيراد من Excel** 📥
- يستورد البيانات من أي ملف Excel
- يعرض تقدم العملية بالتفصيل
- يظهر النتائج (نجح/مكرر/أخطاء)

#### 3. **تحديث البيانات** 🔄
- يحدث عرض البيانات الحالية
- يعرض عدد المستفيدين النشطين

#### 4. **تنظيف البيانات التجريبية** 🧹
- يحذف جميع البيانات التي تبدأ بـ "TEST"
- مفيد لتنظيف البيانات بعد الاختبار

#### 5. **سجل مفصل للعمليات** 📋
- يعرض جميع العمليات مع الوقت
- يساعد في تشخيص المشاكل
- يطبع أيضاً في وحدة التحكم

### 📊 كيفية الاختبار:

#### الخطوة 1: تشغيل الشاشة
```bash
python run_import_test.py
```

#### الخطوة 2: إنشاء ملف اختبار
1. اضغط على "📝 إنشاء ملف Excel للاختبار"
2. اختر مكان حفظ الملف
3. سيتم إنشاء ملف يحتوي على 3 مستفيدين

#### الخطوة 3: اختبار الاستيراد
1. اضغط على "📥 استيراد من Excel"
2. اختر الملف الذي أنشأته
3. راقب رسائل التقدم في السجل
4. تحقق من النتائج

#### الخطوة 4: التحقق من النتائج
- ستظهر البيانات المستوردة في الجدول
- ستظهر رسالة نجاح مع الإحصائيات
- يمكنك مراجعة السجل للتفاصيل

### 🔍 تشخيص المشاكل:

#### إذا لم يعمل الاستيراد:
1. **تحقق من السجل** - ابحث عن رسائل الخطأ
2. **تحقق من تنسيق الملف** - تأكد أن الأعمدة صحيحة
3. **تحقق من البيانات** - تأكد أن الإدارات والوحدات موجودة

#### رسائل الخطأ الشائعة:
- **"فشل في قراءة الملف"** - تحقق من تنسيق Excel
- **"إدارة غير موجودة"** - أنشئ الإدارة أولاً
- **"وحدة غير موجودة"** - أنشئ الوحدة أولاً

### 📋 تنسيق ملف Excel المطلوب:

يجب أن يحتوي ملف Excel على الأعمدة التالية:
- **الاسم** - اسم المستفيد
- **الرقم العام** - رقم المستفيد الفريد
- **الرتبة** - رتبة المستفيد
- **الإدارة** - اسم الإدارة
- **الوحدة** - اسم الوحدة

### 🎯 نصائح للاستخدام:

1. **ابدأ بملف الاختبار** - استخدم الملف المُنشأ تلقائياً أولاً
2. **راقب السجل** - اقرأ جميع الرسائل لفهم ما يحدث
3. **نظف البيانات** - استخدم زر التنظيف بعد كل اختبار
4. **اختبر تدريجياً** - ابدأ بملف صغير ثم زد الحجم

### 🔧 إذا واجهت مشاكل:

#### مشكلة في التشغيل:
```bash
# تأكد من تثبيت المكتبات المطلوبة
pip install pandas openpyxl ttkbootstrap
```

#### مشكلة في قاعدة البيانات:
- تحقق من ملف `database.db`
- تأكد من وجود جداول `beneficiaries`, `departments`, `units`

#### مشكلة في الاستيراد:
- تحقق من تنسيق ملف Excel
- تأكد من وجود الأعمدة المطلوبة
- تحقق من أن الإدارات والوحدات موجودة

### 📞 للمساعدة:
إذا استمرت المشاكل، شارك محتوى السجل (النص في المربع العلوي) لتشخيص المشكلة بدقة.

---

**ملاحظة:** هذه الشاشة مصممة للاختبار والتشخيص فقط. استخدمها لفهم سبب عدم عمل الاستيراد في الشاشة الأساسية.
