#!/usr/bin/env python3
"""
شاشة تعديل حركة مخزون
Item Edit Window
"""

import tkinter as tk
from tkinter import ttk, messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *

class ItemEditWindow:
    def __init__(self, parent, item_data, refresh_callback=None):
        self.parent = parent
        self.item_data = item_data
        self.refresh_callback = refresh_callback
        self.window = None
        
        # متغيرات النموذج
        self.movement_type_var = tk.StringVar()
        self.department_var = tk.StringVar()
        self.quantity_var = tk.StringVar()
        self.notes_var = tk.StringVar()
        
        self.create_window()
        self.load_initial_data()

    def create_window(self):
        """إنشاء النافذة"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("تعديل حركة مخزون")
        self.window.geometry("600x500")
        self.window.resizable(False, False)
        
        # توسيط النافذة
        self.center_window()
        
        # تعيين النافذة كنافذة فرعية
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # إنشاء المحتوى
        self.create_content()

    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        width = 600
        height = 500
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")

    def create_content(self):
        """إنشاء محتوى النافذة"""
        # إطار رئيسي
        main_frame = ttk_bs.Frame(self.window)
        main_frame.pack(fill=BOTH, expand=True, padx=30, pady=30)
        
        # شريط العنوان
        self.create_header(main_frame)
        
        # النموذج
        self.create_form(main_frame)
        
        # أزرار التحكم
        self.create_control_buttons(main_frame)

    def create_header(self, parent):
        """إنشاء شريط العنوان"""
        header_frame = ttk_bs.Frame(parent)
        header_frame.pack(fill=X, pady=(0, 30))
        
        # زر العودة للقائمة
        back_btn = ttk_bs.Button(
            header_frame,
            text="← العودة للقائمة",
            style="secondary.TButton",
            width=22,
            command=self.window.destroy
        )
        back_btn.pack(side=LEFT)
        
        # العنوان
        title_label = ttk_bs.Label(
            header_frame,
            text="✏️ تعديل حركة مخزون",
            font=("Arial", 18, "bold")
        )
        title_label.pack(side=RIGHT)

    def create_form(self, parent):
        """إنشاء النموذج"""
        form_frame = ttk_bs.Frame(parent)
        form_frame.pack(fill=BOTH, expand=True, pady=(0, 30))
        
        # معلومات الصنف (للقراءة فقط)
        self.create_item_info(form_frame)
        
        # حقول التعديل
        self.create_edit_fields(form_frame)

    def create_item_info(self, parent):
        """إنشاء قسم معلومات الصنف"""
        # الصنف
        item_frame = ttk_bs.Frame(parent)
        item_frame.pack(fill=X, pady=15)
        
        item_label = ttk_bs.Label(
            item_frame,
            text="الصنف",
            font=("Arial", 12, "bold"),
            width=20,
            anchor=E
        )
        item_label.pack(side=RIGHT, padx=(0, 20))
        
        item_value = ttk_bs.Label(
            item_frame,
            text=f"{self.item_data.get('item_name', '')} ({self.item_data.get('item_number', '')})",
            font=("Arial", 12),
            foreground="#2c3e50",
            anchor=W
        )
        item_value.pack(side=RIGHT, fill=X, expand=True)

    def create_edit_fields(self, parent):
        """إنشاء حقول التعديل"""
        # نوع الحركة
        movement_frame = ttk_bs.Frame(parent)
        movement_frame.pack(fill=X, pady=15)
        
        movement_label = ttk_bs.Label(
            movement_frame,
            text="نوع الحركة",
            font=("Arial", 12, "bold"),
            width=20,
            anchor=E
        )
        movement_label.pack(side=RIGHT, padx=(0, 20))
        
        movement_combo = ttk_bs.Combobox(
            movement_frame,
            textvariable=self.movement_type_var,
            values=["إضافة", "صرف"],
            state="readonly",
            width=25
        )
        movement_combo.pack(side=RIGHT)
        
        # الكمية
        quantity_frame = ttk_bs.Frame(parent)
        quantity_frame.pack(fill=X, pady=15)
        
        quantity_label = ttk_bs.Label(
            quantity_frame,
            text="الكمية",
            font=("Arial", 12, "bold"),
            width=20,
            anchor=E
        )
        quantity_label.pack(side=RIGHT, padx=(0, 20))
        
        quantity_entry = ttk_bs.Entry(
            quantity_frame,
            textvariable=self.quantity_var,
            width=25,
            font=("Arial", 12)
        )
        quantity_entry.pack(side=RIGHT)
        
        # الهيئة أو الإدارة أو القسم
        dept_frame = ttk_bs.Frame(parent)
        dept_frame.pack(fill=X, pady=15)
        
        dept_label = ttk_bs.Label(
            dept_frame,
            text="الهيئة أو الإدارة أو القسم",
            font=("Arial", 12, "bold"),
            width=20,
            anchor=E
        )
        dept_label.pack(side=RIGHT, padx=(0, 20))
        
        # إطار للقائمة المنسدلة مع زر الإضافة
        dept_combo_frame = ttk_bs.Frame(dept_frame)
        dept_combo_frame.pack(side=RIGHT)
        
        # زر إضافة جديد
        add_dept_btn = ttk_bs.Button(
            dept_combo_frame,
            text="➕",
            style="success.TButton",
            width=15,
            command=self.add_new_department
        )
        add_dept_btn.pack(side=LEFT, padx=(0, 5))
        
        # القائمة المنسدلة
        dept_combo = ttk_bs.Combobox(
            dept_combo_frame,
            textvariable=self.department_var,
            values=["-- اختر الهيئة/الإدارة/القسم --"],
            state="readonly",
            width=22
        )
        dept_combo.pack(side=LEFT)
        
        # ملاحظات
        notes_frame = ttk_bs.Frame(parent)
        notes_frame.pack(fill=X, pady=15)
        
        notes_label = ttk_bs.Label(
            notes_frame,
            text="ملاحظات",
            font=("Arial", 12, "bold"),
            width=20,
            anchor=E
        )
        notes_label.pack(side=TOP, anchor=E, padx=(0, 20))
        
        # منطقة النص
        notes_text_frame = ttk_bs.Frame(notes_frame)
        notes_text_frame.pack(fill=BOTH, expand=True, pady=(10, 0))
        
        self.notes_text = tk.Text(
            notes_text_frame,
            height=6,
            width=50,
            font=("Arial", 11),
            wrap=tk.WORD
        )
        self.notes_text.pack(fill=BOTH, expand=True)

    def create_control_buttons(self, parent):
        """إنشاء أزرار التحكم"""
        buttons_frame = ttk_bs.Frame(parent)
        buttons_frame.pack(fill=X)
        
        # زر حفظ
        save_btn = ttk_bs.Button(
            buttons_frame,
            text="حفظ",
            style="primary.TButton",
            width=15,
            command=self.save_changes
        )
        save_btn.pack(side=LEFT, padx=(0, 10))
        
        # زر إلغاء
        cancel_btn = ttk_bs.Button(
            buttons_frame,
            text="إلغاء",
            style="secondary.TButton",
            width=15,
            command=self.window.destroy
        )
        cancel_btn.pack(side=RIGHT)

    def load_initial_data(self):
        """تحميل البيانات الأولية"""
        try:
            # تعيين القيم الافتراضية
            self.movement_type_var.set("إضافة")
            self.quantity_var.set("10")
            self.department_var.set("-- اختر الهيئة/الإدارة/القسم --")
            
        except Exception as e:
            print(f"خطأ في تحميل البيانات الأولية: {e}")

    def add_new_department(self):
        """إضافة قسم/إدارة جديدة"""
        # نافذة إدخال بسيطة
        new_dept = tk.simpledialog.askstring(
            "إضافة قسم جديد",
            "أدخل اسم الهيئة/الإدارة/القسم:",
            parent=self.window
        )
        
        if new_dept and new_dept.strip():
            # إضافة القسم الجديد للقائمة
            current_values = list(self.department_var.get())
            if new_dept.strip() not in current_values:
                current_values.append(new_dept.strip())
                # تحديث القائمة المنسدلة
                # يمكن تحسين هذا بحفظ القيم في قاعدة البيانات
                self.department_var.set(new_dept.strip())

    def save_changes(self):
        """حفظ التغييرات"""
        try:
            # التحقق من صحة البيانات
            if not self.movement_type_var.get():
                messagebox.showerror("خطأ", "يرجى اختيار نوع الحركة")
                return
            
            if not self.quantity_var.get().strip():
                messagebox.showerror("خطأ", "يرجى إدخال الكمية")
                return
            
            try:
                quantity = int(self.quantity_var.get())
                if quantity <= 0:
                    messagebox.showerror("خطأ", "يجب أن تكون الكمية أكبر من صفر")
                    return
            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال كمية صحيحة")
                return
            
            if self.department_var.get() == "-- اختر الهيئة/الإدارة/القسم --":
                messagebox.showerror("خطأ", "يرجى اختيار الهيئة/الإدارة/القسم")
                return
            
            # جمع البيانات
            movement_data = {
                'item_id': self.item_data.get('id'),
                'movement_type': self.movement_type_var.get(),
                'quantity': int(self.quantity_var.get()),
                'department': self.department_var.get(),
                'notes': self.notes_text.get("1.0", "end-1c").strip()
            }
            
            # هنا يمكن حفظ البيانات في قاعدة البيانات
            print(f"حفظ حركة المخزون: {movement_data}")
            
            # عرض رسالة نجاح
            success_msg = f"""تم حفظ حركة المخزون بنجاح!

الصنف: {self.item_data.get('item_name', '')}
نوع الحركة: {movement_data['movement_type']}
الكمية: {movement_data['quantity']}
الجهة: {movement_data['department']}"""
            
            messagebox.showinfo("نجح", success_msg)
            
            # تحديث البيانات في الشاشة الرئيسية
            if self.refresh_callback:
                self.refresh_callback()
            
            # إغلاق النافذة
            self.window.destroy()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ التغييرات: {e}")

# استيراد مطلوب لنافذة الإدخال
import tkinter.simpledialog
