#!/usr/bin/env python3
"""
تنظيف قاعدة البيانات - حذف جميع البيانات التجريبية
Database Cleaner - Remove all test data for distribution
"""

import sqlite3
import os
from pathlib import Path
import shutil

def clean_database_for_distribution():
    """تنظيف قاعدة البيانات للتوزيع"""
    
    # مسارات قواعد البيانات المحتملة
    db_paths = [
        "stores_management.db",
        "data/stores_management.db",
        Path.home() / "Documents" / "Desktop_Stores_Data" / "stores_management.db"
    ]
    
    for db_path in db_paths:
        if os.path.exists(db_path):
            try:
                print(f"تنظيف قاعدة البيانات: {db_path}")
                
                conn = sqlite3.connect(str(db_path))
                cursor = conn.cursor()
                
                # حذف البيانات التجريبية (الاحتفاظ بالهيكل فقط)
                tables_to_clean = [
                    'transactions',
                    'transaction_items', 
                    'inventory_movements',
                    'items',
                    'added_items',
                    'beneficiaries',
                    'departments',
                    'units',
                    'categories'
                ]
                
                # حذف البيانات من الجداول
                for table in tables_to_clean:
                    try:
                        cursor.execute(f"DELETE FROM {table}")
                        print(f"✅ تم تنظيف جدول {table}")
                    except sqlite3.Error as e:
                        print(f"⚠️ تعذر تنظيف جدول {table}: {e}")
                
                # إعادة تعيين المفاتيح الأساسية
                for table in tables_to_clean:
                    try:
                        cursor.execute(f"DELETE FROM sqlite_sequence WHERE name='{table}'")
                    except sqlite3.Error:
                        pass
                
                # الاحتفاظ بمستخدم admin فقط
                cursor.execute("DELETE FROM users WHERE username != 'admin'")
                
                # إعادة تعيين كلمة مرور admin
                import bcrypt
                admin_password = bcrypt.hashpw("admin".encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
                cursor.execute("UPDATE users SET password_hash = ? WHERE username = 'admin'", (admin_password,))
                
                conn.commit()
                conn.close()
                
                print(f"✅ تم تنظيف قاعدة البيانات: {db_path}")
                
            except Exception as e:
                print(f"❌ خطأ في تنظيف قاعدة البيانات {db_path}: {e}")

def clean_temp_files():
    """حذف الملفات المؤقتة"""
    temp_dirs = [
        "logs",
        "backups", 
        "reports",
        "__pycache__",
        ".pytest_cache"
    ]
    
    temp_files = [
        "*.log",
        "*.tmp",
        "*.pyc",
        "*.pyo"
    ]
    
    current_dir = Path(".")
    
    # حذف المجلدات المؤقتة
    for temp_dir in temp_dirs:
        dir_path = current_dir / temp_dir
        if dir_path.exists():
            try:
                shutil.rmtree(dir_path)
                print(f"✅ تم حذف مجلد: {temp_dir}")
            except Exception as e:
                print(f"⚠️ تعذر حذف مجلد {temp_dir}: {e}")
    
    # حذف الملفات المؤقتة
    import glob
    for pattern in temp_files:
        for file_path in glob.glob(pattern, recursive=True):
            try:
                os.remove(file_path)
                print(f"✅ تم حذف ملف: {file_path}")
            except Exception as e:
                print(f"⚠️ تعذر حذف ملف {file_path}: {e}")

def prepare_for_distribution():
    """إعداد التطبيق للتوزيع"""
    print("🧹 بدء تنظيف التطبيق للتوزيع...")
    print("=" * 50)
    
    # تنظيف قاعدة البيانات
    print("📊 تنظيف قاعدة البيانات...")
    clean_database_for_distribution()
    
    # حذف الملفات المؤقتة
    print("\n🗑️ حذف الملفات المؤقتة...")
    clean_temp_files()
    
    # إنشاء مجلدات فارغة مطلوبة
    required_dirs = ["data", "reports", "backups", "logs", "assets", "assets/icons"]
    for dir_name in required_dirs:
        dir_path = Path(dir_name)
        dir_path.mkdir(exist_ok=True)
        
        # إنشاء ملف .gitkeep للمجلدات الفارغة
        gitkeep_file = dir_path / ".gitkeep"
        gitkeep_file.write_text("# Keep this directory\n")
    
    print("\n✅ تم تنظيف التطبيق بنجاح!")
    print("🎯 التطبيق جاهز للتوزيع")

if __name__ == "__main__":
    prepare_for_distribution()