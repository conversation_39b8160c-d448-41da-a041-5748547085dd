#!/usr/bin/env python3
"""
شاشة التقارير
Reports Window
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from datetime import datetime, date, timedelta
import threading

from config import APP_CONFIG, UI_CONFIG, get_message
from database import db_manager

class ReportsWindow:
    """شاشة التقارير"""

    # متغير كلاس لتتبع النافذة المفتوحة
    _instance = None

    def __new__(cls, parent, main_window):
        # التحقق من وجود نافذة مفتوحة
        if cls._instance is not None:
            try:
                if cls._instance.reports_window.winfo_exists():
                    # إحضار النافذة للمقدمة
                    cls._instance.reports_window.lift()
                    cls._instance.reports_window.focus_force()
                    print("✅ تم إظهار نافذة التقارير الموجودة")
                    return cls._instance
                else:
                    cls._instance = None
            except:
                cls._instance = None

        # إنشاء نافذة جديدة
        instance = super().__new__(cls)
        cls._instance = instance
        return instance

    def __init__(self, parent, main_window):
        # تجنب إعادة التهيئة للنافذة الموجودة
        if hasattr(self, 'reports_window') and self.reports_window:
            return

        self.parent = parent
        self.main_window = main_window
        self.reports_window = None

        self.setup_window()

        # تفعيل مفاتيح الاختصار
        self.setup_shortcuts()
    
    def setup_window(self):
        """إعداد النافذة"""
        self.reports_window = tk.Toplevel(self.parent)
        self.reports_window.title("📊 التقارير")
        self.reports_window.geometry("900x700")
        self.reports_window.resizable(True, True)
        
        # توسيط النافذة
        self.center_window()
        
        # إعداد المحتوى
        self.setup_content()
        
        # جعل النافذة في المقدمة
        self.reports_window.lift()
        self.reports_window.focus_force()

        # ربط حدث الإغلاق لتنظيف المرجع
        self.reports_window.protocol("WM_DELETE_WINDOW", self.on_window_close)
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.reports_window.update_idletasks()
        
        screen_width = self.reports_window.winfo_screenwidth()
        screen_height = self.reports_window.winfo_screenheight()
        
        window_width = 900
        window_height = 700
        
        x = max(0, (screen_width - window_width) // 2)
        y = max(0, (screen_height - window_height) // 2)
        
        self.reports_window.geometry(f"{window_width}x{window_height}+{x}+{y}")

    def on_window_close(self):
        """معالج إغلاق النافذة"""
        # تنظيف المرجع
        ReportsWindow._instance = None
        # إغلاق النافذة
        self.reports_window.destroy()
    
    def setup_content(self):
        """إعداد محتوى النافذة"""
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.reports_window)
        main_frame.pack(fill=BOTH, expand=True, padx=20, pady=20)

        # شريط العنوان
        self.create_header(main_frame)

        # قسم التقارير بقائمة منسدلة
        self.create_reports_dropdown_section(main_frame)
    
    def create_header(self, parent):
        """إنشاء شريط العنوان"""
        header_frame = ttk_bs.Frame(parent)
        header_frame.pack(fill=X, pady=(0, 30))
        
        # العنوان
        title_label = ttk_bs.Label(
            header_frame,
            text="📊 التقارير",
            bootstyle="primary"
        )
        title_label.pack(side=LEFT)
        
        # معلومات إضافية
        info_label = ttk_bs.Label(
            header_frame,
            text="اختر نوع التقرير المطلوب",
            bootstyle="secondary"
        )
        info_label.pack(side=RIGHT)

    def create_reports_dropdown_section(self, parent):
        """إنشاء قسم التقارير بقائمة منسدلة"""
        # إطار التقارير الرئيسي
        reports_main_frame = ttk_bs.Frame(parent)
        reports_main_frame.pack(fill=BOTH, expand=True, pady=20)

        # إطار القائمة المنسدلة
        dropdown_frame = ttk_bs.LabelFrame(
            reports_main_frame,
            text="📊 التقارير",
            bootstyle="primary",
            padding=20
        )
        dropdown_frame.pack(fill=X, pady=(0, 20))

        # قائمة التقارير - تقارير مباشرة بدون قوائم فرعية
        self.reports_list = [
            {"text": "📊 تقرير المخزون", "icon": "📦", "command": self.generate_inventory_status_report},
            {"text": "📋 تقرير عمليات الصرف", "icon": "💳", "command": self.generate_transactions_operations_report},
            {"text": "👥 تقرير المستفيدين", "icon": "👥", "command": self.generate_beneficiaries_operations_report},
            {"text": "🔄 تقرير حركة المخزون", "icon": "📊", "command": self.generate_inventory_movement_report},
            {"text": "📈 تقرير المخزون الحالي", "icon": "📦", "command": self.generate_current_stock_report},
            {"text": "⚠️ تقرير المخزون المنخفض", "icon": "📦", "command": self.generate_low_stock_report},
            {"text": "🏆 الأصناف الأكثر استخداماً", "icon": "📊", "command": self.generate_popular_items_report},
            {"text": "📈 التقرير الإحصائي الشامل", "icon": "📊", "command": self.generate_statistics_report},
            {"text": "👤 تقرير المستخدمين", "icon": "👥", "command": self.generate_users_report}
        ]

        # إنشاء القوائم المنسدلة
        self.create_dropdown_menus(dropdown_frame)

        # منطقة عرض التقارير
        self.create_report_display_area(reports_main_frame)



    def create_dropdown_menus(self, parent):
        """إنشاء قائمة التقارير المباشرة"""
        # إطار القوائم
        menus_frame = ttk_bs.Frame(parent)
        menus_frame.pack(fill=X, pady=10)

        # إنشاء شبكة من الأزرار للتقارير
        buttons_per_row = 3
        current_row = 0
        current_col = 0

        for i, report in enumerate(self.reports_list):
            # إنشاء صف جديد عند الحاجة
            if current_col == 0:
                row_frame = ttk_bs.Frame(menus_frame)
                row_frame.pack(fill=X, pady=5)

            # زر التقرير
            report_button = ttk_bs.Button(
                row_frame,
                text=f"{report['icon']} {report['text']}",
                command=report['command'],
                bootstyle="outline-primary",
                width=35
            )
            report_button.pack(side=LEFT, padx=5, fill=X, expand=True)

            # تحديث العداد
            current_col += 1
            if current_col >= buttons_per_row:
                current_col = 0
                current_row += 1



    def create_report_display_area(self, parent):
        """إنشاء منطقة عرض التقارير"""
        # إطار عرض التقارير
        display_frame = ttk_bs.LabelFrame(
            parent,
            text="📋 معاينة التقرير",
            bootstyle="info",
            padding=10
        )
        display_frame.pack(fill=BOTH, expand=True)

        # رسالة ترحيبية
        welcome_label = ttk_bs.Label(
            display_frame,
            text="اختر نوع التقرير من القائمة أعلاه لعرضه هنا",
            font=("Arial", 12),
            bootstyle="secondary",
            anchor=CENTER
        )
        welcome_label.pack(expand=True)

        # حفظ مرجع لمنطقة العرض
        self.display_area = display_frame
        self.welcome_label = welcome_label





    
    def show_report_preview(self, title, data, columns=None):
        """عرض معاينة التقرير"""
        # إنشاء نافذة المعاينة
        preview_window = tk.Toplevel(self.reports_window)
        preview_window.title(f"معاينة: {title}")
        preview_window.geometry("1000x600")
        preview_window.resizable(True, True)
        
        # توسيط النافذة
        preview_window.update_idletasks()
        x = (preview_window.winfo_screenwidth() - 1000) // 2
        y = (preview_window.winfo_screenheight() - 600) // 2
        preview_window.geometry(f"1000x600+{x}+{y}")
        
        # إطار المحتوى
        main_frame = ttk_bs.Frame(preview_window)
        main_frame.pack(fill=BOTH, expand=True, padx=10, pady=10)
        
        # شريط العنوان
        header_frame = ttk_bs.Frame(main_frame)
        header_frame.pack(fill=X, pady=(0, 10))
        
        title_label = ttk_bs.Label(
            header_frame,
            text=title,
            bootstyle="primary"
        )
        title_label.pack(side=LEFT)
        
        # أزرار الأدوات
        tools_frame = ttk_bs.Frame(header_frame)
        tools_frame.pack(side=RIGHT)
        
        print_btn = ttk_bs.Button(
            tools_frame,
            text="🖨️ طباعة",
            command=lambda: self.print_report(title, data),
            bootstyle="outline-primary",
            width=15
        )
        print_btn.pack(side=RIGHT, padx=5)
        
        export_btn = ttk_bs.Button(
            tools_frame,
            text="📤 تصدير",
            command=lambda: self.export_report(title, data),
            bootstyle="outline-success",
            width=15
        )
        export_btn.pack(side=RIGHT, padx=5)
        
        # عرض البيانات
        if isinstance(data, str):
            # نص عادي
            text_widget = tk.Text(main_frame, wrap=tk.WORD)
            text_widget.pack(fill=BOTH, expand=True)
            text_widget.insert(tk.END, data)
            text_widget.config(state=tk.DISABLED)
        else:
            # جدول
            if columns is None:
                if data:
                    # الحصول على أسماء الأعمدة من sqlite3.Row
                    columns = list(data[0].keys())
                else:
                    columns = []
            
            tree = ttk.Treeview(main_frame, columns=columns, show="headings")
            tree.pack(fill=BOTH, expand=True)
            
            # تعيين العناوين
            for col in columns:
                tree.heading(col, text=col)
                tree.column(col, width=120, anchor="center")
            
            # إضافة البيانات
            for row in data:
                if isinstance(row, dict):
                    values = [row.get(col, '') for col in columns]
                else:
                    # تحويل sqlite3.Row إلى قائمة قيم
                    values = [row[col] for col in columns]
                tree.insert('', 'end', values=values)
            
            # شريط التمرير
            scrollbar = ttk.Scrollbar(main_frame, orient=VERTICAL, command=tree.yview)
            tree.configure(yscrollcommand=scrollbar.set)
            scrollbar.pack(side=RIGHT, fill=Y)
        
        # زر إغلاق
        close_btn = ttk_bs.Button(
            main_frame,
            text="إغلاق",
            command=preview_window.destroy,
            width=15,
            bootstyle="secondary"
        )
        close_btn.pack(pady=10)
    
    def generate_current_stock_report(self):
        """إنشاء تقرير المخزون الحالي"""
        try:
            # إنشاء نافذة التقرير
            report_window = tk.Toplevel(self.parent)
            report_window.title("📈 تقرير المخزون الحالي")
            report_window.geometry("1200x700")
            report_window.resizable(False, False)

            # توسيط النافذة
            report_window.update_idletasks()
            x = (report_window.winfo_screenwidth() - 1200) // 2
            y = (report_window.winfo_screenheight() - 700) // 2
            report_window.geometry(f"1200x700+{x}+{y}")

            # الإطار الرئيسي
            main_frame = ttk_bs.Frame(report_window)
            main_frame.pack(fill=BOTH, expand=True, padx=20, pady=20)

            # شريط العلوي
            header_frame = ttk_bs.Frame(main_frame)
            header_frame.pack(fill=X, pady=(0, 20))

            # العنوان
            title_label = ttk_bs.Label(
                header_frame,
                text="📈 تقرير المخزون الحالي",
                font=("Arial", 16, "bold"),
                bootstyle="primary"
            )
            title_label.pack(side=RIGHT, padx=20)

            # زر الطباعة
            print_btn = ttk_bs.Button(
                header_frame,
                text="🖨️ طباعة",
                width=15,
            command=lambda: self.print_current_stock_report(),
                bootstyle="success"
            )
            print_btn.pack(side=LEFT, padx=10)

            # إطار الجدول
            table_frame = ttk_bs.Frame(main_frame)
            table_frame.pack(fill=BOTH, expand=True)

            # أعمدة الجدول
            columns = ("م", "رقم الصنف", "اسم الصنف", "نوع العهدة", "التصنيف", "الكمية الحالية", "الوحدة", "الحالة")

            # إنشاء الجدول
            tree = ttk_bs.Treeview(table_frame, columns=columns, show="headings", height=20)

            # تعيين عناوين الأعمدة
            for col in columns:
                tree.heading(col, text=col)
                if col == "م":
                    tree.column(col, width=50, anchor=CENTER)
                elif col == "رقم الصنف":
                    tree.column(col, width=100, anchor=CENTER)
                elif col == "اسم الصنف":
                    tree.column(col, width=200, anchor=E)
                elif col == "نوع العهدة":
                    tree.column(col, width=120, anchor=CENTER)
                elif col == "التصنيف":
                    tree.column(col, width=120, anchor=CENTER)
                elif col == "الكمية الحالية":
                    tree.column(col, width=100, anchor=CENTER)
                elif col == "الوحدة":
                    tree.column(col, width=80, anchor=CENTER)
                elif col == "الحالة":
                    tree.column(col, width=100, anchor=CENTER)

            # شريط التمرير
            scrollbar = ttk_bs.Scrollbar(table_frame, orient=VERTICAL, command=tree.yview)
            tree.configure(yscrollcommand=scrollbar.set)

            tree.pack(side=LEFT, fill=BOTH, expand=True)
            scrollbar.pack(side=RIGHT, fill=Y)

            # جلب البيانات
            self.load_current_stock_data(tree)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء تقرير المخزون الحالي: {e}")

    def load_current_stock_data(self, tree):
        """تحميل بيانات المخزون الحالي"""
        try:
            query = """
                SELECT item_number, item_name, custody_type, classification,
                       current_quantity, unit
                FROM added_items
                ORDER BY item_name
            """

            data = db_manager.fetch_all(query)

            # إضافة البيانات
            for i, row in enumerate(data, 1):
                # تحديد الحالة بناءً على الكمية الحالية
                if row['current_quantity'] > 10:
                    status = "متوفر"
                    tag = 'available'
                elif row['current_quantity'] > 0:
                    status = "منخفض"
                    tag = 'low'
                else:
                    status = "غير متوفر"
                    tag = 'unavailable'

                tree.insert('', 'end', values=(
                    i,
                    row['item_number'],
                    row['item_name'],
                    row['custody_type'],
                    row['classification'],
                    int(row['current_quantity']),
                    row['unit'],
                    status
                ), tags=(tag,))

            # تنسيق الألوان
            tree.tag_configure('available', background='#e8f5e8')
            tree.tag_configure('low', background='#fff3cd')
            tree.tag_configure('unavailable', background='#ffebee')

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل بيانات المخزون الحالي: {e}")

    def print_current_stock_report(self):
        """طباعة تقرير المخزون الحالي"""
        try:
            from datetime import datetime

            # جلب البيانات
            query = """
                SELECT item_number, item_name, custody_type, classification,
                       current_quantity, unit
                FROM added_items
                ORDER BY item_name
            """

            data = db_manager.fetch_all(query)

            # إنشاء HTML للطباعة
            current_date = datetime.now().strftime('%Y-%m-%d')
            current_time = datetime.now().strftime('%H:%M:%S')

            html_content = f"""
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>تقرير المخزون الحالي</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; direction: rtl; }}
        .header {{ text-align: center; margin-bottom: 30px; }}
        .header h1 {{ color: #2c3e50; margin-bottom: 10px; }}
        .header p {{ color: #7f8c8d; margin: 5px 0; }}
        table {{ width: 100%; border-collapse: collapse; margin-top: 20px; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: center; }}
        th {{ background-color: #3498db; color: white; font-weight: bold; }}
        tr:nth-child(even) {{ background-color: #f2f2f2; }}
        .available {{ background-color: #d4edda !important; }}
        .low {{ background-color: #fff3cd !important; }}
        .unavailable {{ background-color: #f8d7da !important; }}
        @media print {{ body {{ margin: 0; }} }}
    </style>
    <script>window.onload = function() {{ setTimeout(function() {{ window.print(); }}, 500); }};</script>
</head>
<body>
<div class="header">
<h1>📈 تقرير المخزون الحالي</h1>
<p>تاريخ التقرير: {current_date} | وقت الطباعة: {current_time}</p>
</div>
<table>
<thead><tr><th>#</th><th>رقم الصنف</th><th>اسم الصنف</th><th>نوع العهدة</th><th>التصنيف</th><th>الكمية الحالية</th><th>الوحدة</th><th>الحالة</th></tr></thead>
<tbody>"""

            # إضافة البيانات
            for i, row in enumerate(data, 1):
                # تحديد الحالة والفئة
                if row['current_quantity'] > 10:
                    status = "متوفر"
                    css_class = "available"
                elif row['current_quantity'] > 0:
                    status = "منخفض"
                    css_class = "low"
                else:
                    status = "غير متوفر"
                    css_class = "unavailable"

                html_content += f"""
<tr class="{css_class}">
    <td>{i}</td>
    <td>{row['item_number']}</td>
    <td>{row['item_name']}</td>
    <td>{row['custody_type']}</td>
    <td>{row['classification']}</td>
    <td>{int(row['current_quantity'])}</td>
    <td>{row['unit']}</td>
    <td>{status}</td>
</tr>"""

            html_content += """
</tbody>
</table>
</body>
</html>"""

            # حفظ وفتح الملف للطباعة
            import tempfile
            import webbrowser

            with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as f:
                f.write(html_content)
                temp_file = f.name

            webbrowser.open(f'file://{temp_file}')

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في طباعة التقرير: {e}")
    
    def generate_low_stock_report(self):
        """إنشاء تقرير المخزون المنخفض"""
        try:
            # إنشاء نافذة التقرير
            report_window = tk.Toplevel(self.parent)
            report_window.title("⚠️ تقرير المخزون المنخفض")
            report_window.geometry("1000x600")
            report_window.resizable(False, False)

            # توسيط النافذة
            report_window.update_idletasks()
            x = (report_window.winfo_screenwidth() - 1000) // 2
            y = (report_window.winfo_screenheight() - 600) // 2
            report_window.geometry(f"1000x600+{x}+{y}")

            # الإطار الرئيسي
            main_frame = ttk_bs.Frame(report_window)
            main_frame.pack(fill=BOTH, expand=True, padx=20, pady=20)

            # شريط العلوي
            header_frame = ttk_bs.Frame(main_frame)
            header_frame.pack(fill=X, pady=(0, 20))

            # العنوان
            title_label = ttk_bs.Label(
                header_frame,
                text="⚠️ تقرير المخزون المنخفض",
                font=("Arial", 16, "bold"),
                bootstyle="warning"
            )
            title_label.pack(side=RIGHT, padx=20)

            # زر الطباعة
            print_btn = ttk_bs.Button(
                header_frame,
                text="🖨️ طباعة",
                width=15,
            command=lambda: self.print_low_stock_report(),
                bootstyle="warning"
            )
            print_btn.pack(side=LEFT, padx=10)

            # إطار الجدول
            table_frame = ttk_bs.Frame(main_frame)
            table_frame.pack(fill=BOTH, expand=True)

            # أعمدة الجدول
            columns = ("م", "رقم الصنف", "اسم الصنف", "الكمية الحالية", "الحد الأدنى", "الوحدة", "الحالة")

            # إنشاء الجدول
            tree = ttk_bs.Treeview(table_frame, columns=columns, show="headings", height=18)

            # تعيين عناوين الأعمدة
            for col in columns:
                tree.heading(col, text=col)
                if col == "م":
                    tree.column(col, width=50, anchor=CENTER)
                elif col == "رقم الصنف":
                    tree.column(col, width=120, anchor=CENTER)
                elif col == "اسم الصنف":
                    tree.column(col, width=250, anchor=E)
                elif col == "الكمية الحالية":
                    tree.column(col, width=120, anchor=CENTER)
                elif col == "الحد الأدنى":
                    tree.column(col, width=100, anchor=CENTER)
                elif col == "الوحدة":
                    tree.column(col, width=80, anchor=CENTER)
                elif col == "الحالة":
                    tree.column(col, width=120, anchor=CENTER)

            # شريط التمرير
            scrollbar = ttk_bs.Scrollbar(table_frame, orient=VERTICAL, command=tree.yview)
            tree.configure(yscrollcommand=scrollbar.set)

            tree.pack(side=LEFT, fill=BOTH, expand=True)
            scrollbar.pack(side=RIGHT, fill=Y)

            # جلب البيانات
            self.load_low_stock_data(tree)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء تقرير المخزون المنخفض: {e}")

    def load_low_stock_data(self, tree):
        """تحميل بيانات المخزون المنخفض"""
        try:
            threshold = 10  # الحد الأدنى للمخزون
            query = """
                SELECT item_number, item_name, current_quantity, unit
                FROM added_items
                WHERE current_quantity <= ?
                ORDER BY current_quantity ASC
            """

            data = db_manager.fetch_all(query, (threshold,))

            if not data:
                # إضافة رسالة إذا لم توجد أصناف منخفضة
                tree.insert('', 'end', values=(
                    "",
                    "",
                    "✅ جميع الأصناف فوق الحد الأدنى",
                    "",
                    "",
                    "",
                    "ممتاز"
                ), tags=('good',))
                tree.tag_configure('good', background='#d4edda')
                return

            # إضافة البيانات
            for i, row in enumerate(data, 1):
                # تحديد الحالة بناءً على الكمية الحالية
                if row['current_quantity'] == 0:
                    status = "نفدت الكمية"
                    tag = 'critical'
                elif row['current_quantity'] <= 5:
                    status = "حرج جداً"
                    tag = 'critical'
                else:
                    status = "منخفض"
                    tag = 'warning'

                tree.insert('', 'end', values=(
                    i,
                    row['item_number'],
                    row['item_name'],
                    int(row['current_quantity']),
                    threshold,
                    row['unit'],
                    status
                ), tags=(tag,))

            # تنسيق الألوان
            tree.tag_configure('critical', background='#f8d7da')
            tree.tag_configure('warning', background='#fff3cd')

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل بيانات المخزون المنخفض: {e}")

    def print_low_stock_report(self):
        """طباعة تقرير المخزون المنخفض"""
        try:
            from datetime import datetime

            # جلب البيانات
            threshold = 10
            query = """
                SELECT item_number, item_name, current_quantity, unit
                FROM added_items
                WHERE current_quantity <= ?
                ORDER BY current_quantity ASC
            """

            data = db_manager.fetch_all(query, (threshold,))

            # إنشاء HTML للطباعة
            current_date = datetime.now().strftime('%Y-%m-%d')
            current_time = datetime.now().strftime('%H:%M:%S')

            html_content = f"""
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>تقرير المخزون المنخفض</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; direction: rtl; }}
        .header {{ text-align: center; margin-bottom: 30px; }}
        .header h1 {{ color: #dc3545; margin-bottom: 10px; }}
        .header p {{ color: #7f8c8d; margin: 5px 0; }}
        table {{ width: 100%; border-collapse: collapse; margin-top: 20px; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: center; }}
        th {{ background-color: #dc3545; color: white; font-weight: bold; }}
        tr:nth-child(even) {{ background-color: #f2f2f2; }}
        .critical {{ background-color: #f8d7da !important; }}
        .warning {{ background-color: #fff3cd !important; }}
        .good {{ background-color: #d4edda !important; }}
        @media print {{ body {{ margin: 0; }} }}
    </style>
    <script>window.onload = function() {{ setTimeout(function() {{ window.print(); }}, 500); }};</script>
</head>
<body>
<div class="header">
<h1>⚠️ تقرير المخزون المنخفض</h1>
<p>تاريخ التقرير: {current_date} | وقت الطباعة: {current_time}</p>
<p>الحد الأدنى للمخزون: {threshold} وحدة</p>
</div>"""

            if not data:
                html_content += """
<div style="text-align: center; color: green; font-size: 18px; margin: 50px;">
    <h2>✅ ممتاز! جميع الأصناف فوق الحد الأدنى للمخزون</h2>
</div>"""
            else:
                html_content += """
<table>
<thead><tr><th>#</th><th>رقم الصنف</th><th>اسم الصنف</th><th>الكمية الحالية</th><th>الحد الأدنى</th><th>الوحدة</th><th>الحالة</th></tr></thead>
<tbody>"""

                # إضافة البيانات
                for i, row in enumerate(data, 1):
                    # تحديد الحالة والفئة
                    if row['current_quantity'] == 0:
                        status = "نفدت الكمية"
                        css_class = "critical"
                    elif row['current_quantity'] <= 5:
                        status = "حرج جداً"
                        css_class = "critical"
                    else:
                        status = "منخفض"
                        css_class = "warning"

                    html_content += f"""
<tr class="{css_class}">
    <td>{i}</td>
    <td>{row['item_number']}</td>
    <td>{row['item_name']}</td>
    <td>{int(row['current_quantity'])}</td>
    <td>{threshold}</td>
    <td>{row['unit']}</td>
    <td>{status}</td>
</tr>"""

                html_content += """
</tbody>
</table>"""

            html_content += """
</body>
</html>"""

            # حفظ وفتح الملف للطباعة
            import tempfile
            import webbrowser

            with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as f:
                f.write(html_content)
                temp_file = f.name

            webbrowser.open(f'file://{temp_file}')

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في طباعة التقرير: {e}")
    
    def generate_movement_report(self):
        """إنشاء تقرير حركة المخزون"""
        try:
            query = """
                SELECT im.movement_date as 'التاريخ', i.name as 'اسم الصنف',
                       im.movement_type as 'نوع الحركة', im.quantity as 'الكمية',
                       u.full_name as 'المستخدم'
                FROM inventory_movements im
                LEFT JOIN items i ON im.item_id = i.id
                LEFT JOIN users u ON im.user_id = u.id
                ORDER BY im.movement_date DESC
                LIMIT 100
            """
            data = db_manager.fetch_all(query)
            
            if data:
                self.show_report_preview("تقرير حركة المخزون (آخر 100 حركة)", data)
            else:
                messagebox.showinfo("تنبيه", "لا توجد حركات مخزون")
                
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء التقرير: {e}")
    
    def generate_popular_items_report(self):
        """إنشاء تقرير الأصناف الأكثر استخداماً"""
        try:
            # إنشاء نافذة التقرير
            report_window = tk.Toplevel(self.parent)
            report_window.title("🏆 الأصناف الأكثر استخداماً")
            report_window.geometry("1000x600")
            report_window.resizable(False, False)

            # توسيط النافذة
            report_window.update_idletasks()
            x = (report_window.winfo_screenwidth() - 1000) // 2
            y = (report_window.winfo_screenheight() - 600) // 2
            report_window.geometry(f"1000x600+{x}+{y}")

            # الإطار الرئيسي
            main_frame = ttk_bs.Frame(report_window)
            main_frame.pack(fill=BOTH, expand=True, padx=20, pady=20)

            # شريط العلوي
            header_frame = ttk_bs.Frame(main_frame)
            header_frame.pack(fill=X, pady=(0, 20))

            # العنوان
            title_label = ttk_bs.Label(
                header_frame,
                text="🏆 الأصناف الأكثر استخداماً",
                font=("Arial", 16, "bold"),
                bootstyle="success"
            )
            title_label.pack(side=RIGHT, padx=20)

            # زر الطباعة
            print_btn = ttk_bs.Button(
                header_frame,
                text="🖨️ طباعة",
                width=15,
            command=lambda: self.print_popular_items_report(),
                bootstyle="success"
            )
            print_btn.pack(side=LEFT, padx=10)

            # إطار الجدول
            table_frame = ttk_bs.Frame(main_frame)
            table_frame.pack(fill=BOTH, expand=True)

            # أعمدة الجدول
            columns = ("الترتيب", "رقم الصنف", "اسم الصنف", "إجمالي الكمية المصروفة", "عدد العمليات", "متوسط الكمية")

            # إنشاء الجدول
            tree = ttk_bs.Treeview(table_frame, columns=columns, show="headings", height=18)

            # تعيين عناوين الأعمدة
            for col in columns:
                tree.heading(col, text=col)
                if col == "الترتيب":
                    tree.column(col, width=80, anchor=CENTER)
                elif col == "رقم الصنف":
                    tree.column(col, width=120, anchor=CENTER)
                elif col == "اسم الصنف":
                    tree.column(col, width=250, anchor=E)
                elif col == "إجمالي الكمية المصروفة":
                    tree.column(col, width=150, anchor=CENTER)
                elif col == "عدد العمليات":
                    tree.column(col, width=120, anchor=CENTER)
                elif col == "متوسط الكمية":
                    tree.column(col, width=120, anchor=CENTER)

            # شريط التمرير
            scrollbar = ttk_bs.Scrollbar(table_frame, orient=VERTICAL, command=tree.yview)
            tree.configure(yscrollcommand=scrollbar.set)

            tree.pack(side=LEFT, fill=BOTH, expand=True)
            scrollbar.pack(side=RIGHT, fill=Y)

            # جلب البيانات
            self.load_popular_items_data(tree)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء تقرير الأصناف الأكثر استخداماً: {e}")

    def load_popular_items_data(self, tree):
        """تحميل بيانات الأصناف الأكثر استخداماً"""
        try:
            query = """
                SELECT ai.item_number, ai.item_name,
                       COALESCE(SUM(ti.quantity), 0) as total_dispensed,
                       COALESCE(COUNT(ti.id), 0) as operation_count
                FROM added_items ai
                LEFT JOIN items i ON ai.item_number = i.item_number
                LEFT JOIN transaction_items ti ON i.id = ti.item_id
                GROUP BY ai.item_number, ai.item_name
                HAVING total_dispensed > 0
                ORDER BY total_dispensed DESC
                LIMIT 20
            """

            data = db_manager.fetch_all(query)

            if not data:
                # إضافة رسالة إذا لم توجد بيانات
                tree.insert('', 'end', values=(
                    "",
                    "",
                    "لا توجد عمليات صرف مسجلة",
                    "",
                    "",
                    ""
                ), tags=('no_data',))
                tree.tag_configure('no_data', background='#f8f9fa')
                return

            # إضافة البيانات
            for i, row in enumerate(data, 1):
                total_dispensed = int(row['total_dispensed'])
                operation_count = int(row['operation_count'])

                # حساب متوسط الكمية
                avg_quantity = round(total_dispensed / operation_count, 2) if operation_count > 0 else 0

                # تحديد لون الصف حسب الترتيب
                if i <= 3:
                    tag = 'top3'
                elif i <= 10:
                    tag = 'top10'
                else:
                    tag = 'others'

                tree.insert('', 'end', values=(
                    f"#{i}",
                    row['item_number'],
                    row['item_name'],
                    total_dispensed,
                    operation_count,
                    avg_quantity
                ), tags=(tag,))

            # تنسيق الألوان
            tree.tag_configure('top3', background='#d4edda')  # أخضر للأعلى 3
            tree.tag_configure('top10', background='#fff3cd')  # أصفر للأعلى 10
            tree.tag_configure('others', background='#f8f9fa')  # رمادي للباقي

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل بيانات الأصناف الأكثر استخداماً: {e}")

    def print_popular_items_report(self):
        """طباعة تقرير الأصناف الأكثر استخداماً"""
        try:
            from datetime import datetime

            # جلب البيانات
            query = """
                SELECT ai.item_number, ai.item_name,
                       COALESCE(SUM(ti.quantity), 0) as total_dispensed,
                       COALESCE(COUNT(ti.id), 0) as operation_count
                FROM added_items ai
                LEFT JOIN items i ON ai.item_number = i.item_number
                LEFT JOIN transaction_items ti ON i.id = ti.item_id
                GROUP BY ai.item_number, ai.item_name
                HAVING total_dispensed > 0
                ORDER BY total_dispensed DESC
                LIMIT 20
            """

            data = db_manager.fetch_all(query)

            # إنشاء HTML للطباعة
            current_date = datetime.now().strftime('%Y-%m-%d')
            current_time = datetime.now().strftime('%H:%M:%S')

            html_content = f"""
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>الأصناف الأكثر استخداماً</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; direction: rtl; }}
        .header {{ text-align: center; margin-bottom: 30px; }}
        .header h1 {{ color: #28a745; margin-bottom: 10px; }}
        .header p {{ color: #7f8c8d; margin: 5px 0; }}
        table {{ width: 100%; border-collapse: collapse; margin-top: 20px; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: center; }}
        th {{ background-color: #28a745; color: white; font-weight: bold; }}
        tr:nth-child(even) {{ background-color: #f2f2f2; }}
        .top3 {{ background-color: #d4edda !important; }}
        .top10 {{ background-color: #fff3cd !important; }}
        .others {{ background-color: #f8f9fa !important; }}
        @media print {{ body {{ margin: 0; }} }}
    </style>
    <script>window.onload = function() {{ setTimeout(function() {{ window.print(); }}, 500); }};</script>
</head>
<body>
<div class="header">
<h1>🏆 الأصناف الأكثر استخداماً</h1>
<p>تاريخ التقرير: {current_date} | وقت الطباعة: {current_time}</p>
<p>أعلى 20 صنف من حيث الاستخدام</p>
</div>"""

            if not data:
                html_content += """
<div style="text-align: center; color: #6c757d; font-size: 18px; margin: 50px;">
    <h2>لا توجد عمليات صرف مسجلة</h2>
</div>"""
            else:
                html_content += """
<table>
<thead><tr><th>الترتيب</th><th>رقم الصنف</th><th>اسم الصنف</th><th>إجمالي الكمية المصروفة</th><th>عدد العمليات</th><th>متوسط الكمية</th></tr></thead>
<tbody>"""

                # إضافة البيانات
                for i, row in enumerate(data, 1):
                    total_dispensed = int(row['total_dispensed'])
                    operation_count = int(row['operation_count'])
                    avg_quantity = round(total_dispensed / operation_count, 2) if operation_count > 0 else 0

                    # تحديد الفئة
                    if i <= 3:
                        css_class = "top3"
                    elif i <= 10:
                        css_class = "top10"
                    else:
                        css_class = "others"

                    html_content += f"""
<tr class="{css_class}">
    <td>#{i}</td>
    <td>{row['item_number']}</td>
    <td>{row['item_name']}</td>
    <td>{total_dispensed}</td>
    <td>{operation_count}</td>
    <td>{avg_quantity}</td>
</tr>"""

                html_content += """
</tbody>
</table>"""

            html_content += """
</body>
</html>"""

            # حفظ وفتح الملف للطباعة
            import tempfile
            import webbrowser

            with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as f:
                f.write(html_content)
                temp_file = f.name

            webbrowser.open(f'file://{temp_file}')

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في طباعة التقرير: {e}")
    
    def generate_transactions_report(self):
        """إنشاء تقرير عمليات الصرف"""
        try:
            query = """
                SELECT t.id as 'رقم العملية', t.transaction_date as 'التاريخ',
                       b.name as 'المستفيد', t.status as 'الحالة',
                       u.full_name as 'المستخدم'
                FROM transactions t
                LEFT JOIN beneficiaries b ON t.beneficiary_id = b.id
                LEFT JOIN users u ON t.user_id = u.id
                ORDER BY t.transaction_date DESC
                LIMIT 100
            """
            data = db_manager.fetch_all(query)
            
            if data:
                self.show_report_preview("تقرير عمليات الصرف (آخر 100 عملية)", data)
            else:
                messagebox.showinfo("تنبيه", "لا توجد عمليات صرف")
                
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء التقرير: {e}")
    
    def generate_by_beneficiary_report(self):
        """إنشاء تقرير العمليات حسب المستفيد"""
        try:
            query = """
                SELECT b.name as 'المستفيد', b.number as 'الرقم',
                       COUNT(t.id) as 'عدد العمليات',
                       MAX(t.transaction_date) as 'آخر عملية'
                FROM beneficiaries b
                LEFT JOIN transactions t ON b.id = t.beneficiary_id
                WHERE b.is_active = 1
                GROUP BY b.id, b.name, b.number
                ORDER BY COUNT(t.id) DESC
            """
            data = db_manager.fetch_all(query)
            
            if data:
                self.show_report_preview("تقرير العمليات حسب المستفيد", data)
            else:
                messagebox.showinfo("تنبيه", "لا توجد بيانات لعرضها")
                
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء التقرير: {e}")
    
    def generate_by_date_report(self):
        """إنشاء تقرير العمليات حسب التاريخ"""
        messagebox.showinfo("قريباً", "تقرير العمليات حسب التاريخ قيد التطوير")
    
    def generate_users_report(self):
        """إنشاء تقرير المستخدمين"""
        try:
            query = """
                SELECT username as 'اسم المستخدم',
                       full_name as 'الاسم الكامل',
                       CASE WHEN is_admin = 1 THEN 'مدير' ELSE 'مستخدم' END as 'الدور',
                       CASE WHEN is_active = 1 THEN 'نشط' ELSE 'غير نشط' END as 'الحالة',
                       CASE
                           WHEN last_login IS NULL OR last_login = '' THEN 'لم يسجل دخول'
                           ELSE strftime('%Y-%m-%d %H:%M', last_login)
                       END as 'آخر دخول',
                       strftime('%Y-%m-%d', created_at) as 'تاريخ الإنشاء'
                FROM users
                ORDER BY full_name
            """
            data = db_manager.fetch_all(query)

            if data:
                self.show_report_preview("تقرير المستخدمين", data)
            else:
                messagebox.showinfo("تنبيه", "لا توجد بيانات لعرضها")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء التقرير: {e}")

    def get_current_user_name(self):
        """الحصول على اسم المستخدم الحالي"""
        try:
            # محاولة الحصول على المستخدم الحالي من النافذة الرئيسية
            if hasattr(self.parent, 'current_user') and self.parent.current_user:
                current_user = self.parent.current_user
                if hasattr(current_user, 'full_name') and current_user.full_name:
                    return current_user.full_name
                elif hasattr(current_user, 'username') and current_user.username:
                    return current_user.username

            # محاولة الحصول على المستخدم من قاعدة البيانات
            current_user_query = """
                SELECT full_name, username FROM users
                WHERE is_active = 1
                ORDER BY last_login DESC
                LIMIT 1
            """
            current_user = db_manager.fetch_one(current_user_query)
            if current_user:
                full_name = current_user.get('full_name')
                username = current_user.get('username')

                if full_name and full_name.strip():
                    return full_name.strip()
                elif username and username.strip():
                    return username.strip()

            # قيمة افتراضية
            return "مدير النظام"

        except Exception as e:
            print(f"خطأ في الحصول على المستخدم الحالي: {e}")
            return "مدير النظام"

    def generate_beneficiaries_report(self):
        """إنشاء تقرير المستفيدين"""
        try:
            query = """
                SELECT name as 'الاسم', number as 'الرقم', rank as 'الرتبة',
                       department as 'الإدارة', phone as 'الهاتف',
                       CASE WHEN is_active = 1 THEN 'نشط' ELSE 'غير نشط' END as 'الحالة'
                FROM beneficiaries
                ORDER BY name
            """
            data = db_manager.fetch_all(query)
            
            if data:
                self.show_report_preview("تقرير المستفيدين", data)
            else:
                messagebox.showinfo("تنبيه", "لا توجد بيانات لعرضها")
                
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء التقرير: {e}")
    
    def generate_audit_report(self):
        """إنشاء تقرير سجل الأنشطة"""
        messagebox.showinfo("قريباً", "تقرير سجل الأنشطة قيد التطوير")
    
    def generate_custom_date_report(self):
        """إنشاء تقرير مخصص بالتاريخ"""
        messagebox.showinfo("قريباً", "التقرير المخصص بالتاريخ قيد التطوير")
    
    def generate_statistics_report(self):
        """إنشاء تقرير إحصائي شامل"""
        try:
            # إنشاء نافذة التقرير
            report_window = tk.Toplevel(self.parent)
            report_window.title("📈 التقرير الإحصائي الشامل")
            report_window.geometry("1000x700")
            report_window.resizable(False, False)

            # توسيط النافذة
            report_window.update_idletasks()
            x = (report_window.winfo_screenwidth() - 1000) // 2
            y = (report_window.winfo_screenheight() - 700) // 2
            report_window.geometry(f"1000x700+{x}+{y}")

            # الإطار الرئيسي
            main_frame = ttk_bs.Frame(report_window)
            main_frame.pack(fill=BOTH, expand=True, padx=20, pady=20)

            # شريط العلوي
            header_frame = ttk_bs.Frame(main_frame)
            header_frame.pack(fill=X, pady=(0, 20))

            # العنوان
            title_label = ttk_bs.Label(
                header_frame,
                text="📈 التقرير الإحصائي الشامل",
                font=("Arial", 16, "bold"),
                bootstyle="primary"
            )
            title_label.pack(side=RIGHT, padx=20)

            # زر الطباعة
            print_btn = ttk_bs.Button(
                header_frame,
                text="🖨️ طباعة",
                width=15,
            command=lambda: self.print_statistics_report(),
                bootstyle="primary"
            )
            print_btn.pack(side=LEFT, padx=10)

            # إطار الإحصائيات
            stats_frame = ttk_bs.Frame(main_frame)
            stats_frame.pack(fill=BOTH, expand=True)

            # جلب وعرض الإحصائيات
            self.load_statistics_data(stats_frame)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء التقرير الإحصائي الشامل: {e}")

    def load_statistics_data(self, parent_frame):
        """تحميل وعرض البيانات الإحصائية"""
        try:
            from datetime import datetime

            # جمع الإحصائيات
            stats = {}

            # إحصائيات المخزون
            items_count = db_manager.fetch_one("SELECT COUNT(*) as count FROM added_items")
            stats['عدد الأصناف'] = items_count['count'] if items_count else 0

            low_stock_count = db_manager.fetch_one("SELECT COUNT(*) as count FROM added_items WHERE current_quantity <= 10")
            stats['أصناف منخفضة المخزون'] = low_stock_count['count'] if low_stock_count else 0

            total_quantity = db_manager.fetch_one("SELECT SUM(current_quantity) as total FROM added_items")
            stats['إجمالي الكميات'] = int(total_quantity['total']) if total_quantity and total_quantity['total'] else 0

            # إحصائيات المستخدمين
            users_count = db_manager.fetch_one("SELECT COUNT(*) as count FROM users WHERE is_active = 1")
            stats['عدد المستخدمين النشطين'] = users_count['count'] if users_count else 0

            # إحصائيات المستفيدين
            beneficiaries_count = db_manager.fetch_one("SELECT COUNT(*) as count FROM beneficiaries WHERE is_active = 1")
            stats['عدد المستفيدين النشطين'] = beneficiaries_count['count'] if beneficiaries_count else 0

            # إحصائيات العمليات
            transactions_count = db_manager.fetch_one("SELECT COUNT(*) as count FROM transactions")
            stats['إجمالي عمليات الصرف'] = transactions_count['count'] if transactions_count else 0

            total_dispensed = db_manager.fetch_one("SELECT SUM(quantity) as total FROM transaction_items")
            stats['إجمالي الكميات المصروفة'] = int(total_dispensed['total']) if total_dispensed and total_dispensed['total'] else 0

            # إنشاء بطاقات الإحصائيات
            self.create_statistics_cards(parent_frame, stats)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل البيانات الإحصائية: {e}")

    def create_statistics_cards(self, parent_frame, stats):
        """إنشاء بطاقات الإحصائيات"""
        try:
            # إطار البطاقات الرئيسي
            cards_main_frame = ttk_bs.Frame(parent_frame)
            cards_main_frame.pack(fill=BOTH, expand=True, pady=10)

            # الصف الأول - إحصائيات المخزون
            inventory_frame = ttk_bs.LabelFrame(cards_main_frame, text="📦 إحصائيات المخزون", bootstyle="primary")
            inventory_frame.pack(fill=X, pady=10)

            inv_cards_frame = ttk_bs.Frame(inventory_frame)
            inv_cards_frame.pack(fill=X, padx=10, pady=10)

            self.create_stat_card(inv_cards_frame, "عدد الأصناف", stats['عدد الأصناف'], "📦", "info")
            self.create_stat_card(inv_cards_frame, "أصناف منخفضة", stats['أصناف منخفضة المخزون'], "⚠️", "warning")
            self.create_stat_card(inv_cards_frame, "إجمالي الكميات", stats['إجمالي الكميات'], "📊", "success")

            # الصف الثاني - إحصائيات المستخدمين والمستفيدين
            users_frame = ttk_bs.LabelFrame(cards_main_frame, text="👥 إحصائيات المستخدمين والمستفيدين", bootstyle="success")
            users_frame.pack(fill=X, pady=10)

            users_cards_frame = ttk_bs.Frame(users_frame)
            users_cards_frame.pack(fill=X, padx=10, pady=10)

            self.create_stat_card(users_cards_frame, "المستخدمين النشطين", stats['عدد المستخدمين النشطين'], "👤", "primary")
            self.create_stat_card(users_cards_frame, "المستفيدين النشطين", stats['عدد المستفيدين النشطين'], "🏢", "info")

            # الصف الثالث - إحصائيات العمليات
            operations_frame = ttk_bs.LabelFrame(cards_main_frame, text="💳 إحصائيات العمليات", bootstyle="warning")
            operations_frame.pack(fill=X, pady=10)

            ops_cards_frame = ttk_bs.Frame(operations_frame)
            ops_cards_frame.pack(fill=X, padx=10, pady=10)

            self.create_stat_card(ops_cards_frame, "عمليات الصرف", stats['إجمالي عمليات الصرف'], "🔄", "danger")
            self.create_stat_card(ops_cards_frame, "الكميات المصروفة", stats['إجمالي الكميات المصروفة'], "📤", "secondary")

            # معلومات التقرير
            info_frame = ttk_bs.LabelFrame(cards_main_frame, text="ℹ️ معلومات التقرير", bootstyle="secondary")
            info_frame.pack(fill=X, pady=10)

            from datetime import datetime
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            info_text = f"""
تاريخ ووقت إنشاء التقرير: {current_time}
تم إنشاء هذا التقرير بواسطة نظام إدارة المخازن
جميع البيانات محدثة حتى لحظة إنشاء التقرير
            """

            info_label = ttk_bs.Label(
                info_frame,
                text=info_text,
                font=("Arial", 10),
                bootstyle="secondary",
                justify=CENTER
            )
            info_label.pack(pady=10)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء بطاقات الإحصائيات: {e}")

    def create_stat_card(self, parent, title, value, icon, style):
        """إنشاء بطاقة إحصائية واحدة"""
        card_frame = ttk_bs.Frame(parent, bootstyle=f"{style}")
        card_frame.pack(side=LEFT, fill=BOTH, expand=True, padx=5)

        content_frame = ttk_bs.Frame(card_frame, bootstyle="light")
        content_frame.pack(fill=BOTH, expand=True, padx=2, pady=2)

        # الأيقونة
        icon_label = ttk_bs.Label(
            content_frame,
            text=icon,
            font=("Arial", 20),
            bootstyle=style
        )
        icon_label.pack(pady=5)

        # القيمة
        value_label = ttk_bs.Label(
            content_frame,
            text=str(value),
            font=("Arial", 18, "bold"),
            bootstyle=style
        )
        value_label.pack()

        # العنوان
        title_label = ttk_bs.Label(
            content_frame,
            text=title,
            font=("Arial", 10),
            bootstyle="secondary"
        )
        title_label.pack(pady=2)

    def print_statistics_report(self):
        """طباعة التقرير الإحصائي الشامل"""
        try:
            from datetime import datetime

            # جمع الإحصائيات
            stats = {}

            # إحصائيات المخزون
            items_count = db_manager.fetch_one("SELECT COUNT(*) as count FROM added_items")
            stats['عدد الأصناف'] = items_count['count'] if items_count else 0

            low_stock_count = db_manager.fetch_one("SELECT COUNT(*) as count FROM added_items WHERE current_quantity <= 10")
            stats['أصناف منخفضة المخزون'] = low_stock_count['count'] if low_stock_count else 0

            total_quantity = db_manager.fetch_one("SELECT SUM(current_quantity) as total FROM added_items")
            stats['إجمالي الكميات'] = int(total_quantity['total']) if total_quantity and total_quantity['total'] else 0

            # إحصائيات المستخدمين
            users_count = db_manager.fetch_one("SELECT COUNT(*) as count FROM users WHERE is_active = 1")
            stats['عدد المستخدمين النشطين'] = users_count['count'] if users_count else 0

            # إحصائيات المستفيدين
            beneficiaries_count = db_manager.fetch_one("SELECT COUNT(*) as count FROM beneficiaries WHERE is_active = 1")
            stats['عدد المستفيدين النشطين'] = beneficiaries_count['count'] if beneficiaries_count else 0

            # إحصائيات العمليات
            transactions_count = db_manager.fetch_one("SELECT COUNT(*) as count FROM transactions")
            stats['إجمالي عمليات الصرف'] = transactions_count['count'] if transactions_count else 0

            total_dispensed = db_manager.fetch_one("SELECT SUM(quantity) as total FROM transaction_items")
            stats['إجمالي الكميات المصروفة'] = int(total_dispensed['total']) if total_dispensed and total_dispensed['total'] else 0

            # إنشاء HTML للطباعة
            current_date = datetime.now().strftime('%Y-%m-%d')
            current_time = datetime.now().strftime('%H:%M:%S')

            html_content = f"""
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>التقرير الإحصائي الشامل</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; direction: rtl; }}
        .header {{ text-align: center; margin-bottom: 40px; }}
        .header h1 {{ color: #2c3e50; margin-bottom: 10px; }}
        .header p {{ color: #7f8c8d; margin: 5px 0; }}
        .section {{ margin: 30px 0; padding: 20px; border: 2px solid #ddd; border-radius: 10px; }}
        .section h2 {{ color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 10px; }}
        .stats-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0; }}
        .stat-card {{ background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; border: 1px solid #dee2e6; }}
        .stat-value {{ font-size: 24px; font-weight: bold; color: #2c3e50; margin: 10px 0; }}
        .stat-title {{ color: #6c757d; font-size: 14px; }}
        .inventory {{ border-color: #3498db; }}
        .users {{ border-color: #28a745; }}
        .operations {{ border-color: #ffc107; }}
        @media print {{ body {{ margin: 0; }} }}
    </style>
    <script>window.onload = function() {{ setTimeout(function() {{ window.print(); }}, 500); }};</script>
</head>
<body>
<div class="header">
<h1>📈 التقرير الإحصائي الشامل</h1>
<p>تاريخ التقرير: {current_date} | وقت الطباعة: {current_time}</p>
<p>نظام إدارة المخازن - تقرير شامل لجميع الإحصائيات</p>
</div>

<div class="section inventory">
<h2>📦 إحصائيات المخزون</h2>
<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-value">{stats['عدد الأصناف']}</div>
        <div class="stat-title">عدد الأصناف</div>
    </div>
    <div class="stat-card">
        <div class="stat-value">{stats['أصناف منخفضة المخزون']}</div>
        <div class="stat-title">أصناف منخفضة المخزون</div>
    </div>
    <div class="stat-card">
        <div class="stat-value">{stats['إجمالي الكميات']:,}</div>
        <div class="stat-title">إجمالي الكميات</div>
    </div>
</div>
</div>

<div class="section users">
<h2>👥 إحصائيات المستخدمين والمستفيدين</h2>
<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-value">{stats['عدد المستخدمين النشطين']}</div>
        <div class="stat-title">المستخدمين النشطين</div>
    </div>
    <div class="stat-card">
        <div class="stat-value">{stats['عدد المستفيدين النشطين']}</div>
        <div class="stat-title">المستفيدين النشطين</div>
    </div>
</div>
</div>

<div class="section operations">
<h2>💳 إحصائيات العمليات</h2>
<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-value">{stats['إجمالي عمليات الصرف']}</div>
        <div class="stat-title">عمليات الصرف</div>
    </div>
    <div class="stat-card">
        <div class="stat-value">{stats['إجمالي الكميات المصروفة']:,}</div>
        <div class="stat-title">الكميات المصروفة</div>
    </div>
</div>
</div>

<div style="text-align: center; margin-top: 40px; color: #6c757d; font-size: 12px;">
<p>تم إنشاء هذا التقرير بواسطة نظام إدارة المخازن</p>
<p>جميع البيانات محدثة حتى لحظة إنشاء التقرير</p>
</div>

</body>
</html>"""

            # حفظ وفتح الملف للطباعة
            import tempfile
            import webbrowser

            with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as f:
                f.write(html_content)
                temp_file = f.name

            webbrowser.open(f'file://{temp_file}')

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في طباعة التقرير: {e}")
    
    def export_all_data(self):
        """تصدير جميع البيانات"""
        messagebox.showinfo("قريباً", "تصدير جميع البيانات قيد التطوير")

    def print_report(self, title, data):
        """طباعة التقرير"""
        try:
            from utils.print_manager import PrintManager
            from datetime import datetime
            import tempfile
            import webbrowser
            import os

            # إنشاء مدير الطباعة
            pm = PrintManager()

            # إنشاء محتوى HTML للتقرير
            html_content = self.create_report_html(title, data)

            # إنشاء ملف مؤقت
            with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as f:
                f.write(html_content)
                temp_file = f.name

            # فتح الملف في المتصفح للطباعة
            webbrowser.open(f'file://{temp_file}')

            # حذف الملف المؤقت بعد فترة
            def cleanup():
                try:
                    os.unlink(temp_file)
                except:
                    pass

            # تأخير الحذف لإعطاء وقت للمتصفح
            self.parent.after(10000, cleanup)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في طباعة التقرير: {e}")

    def create_report_html(self, title, data):
        """إنشاء محتوى HTML للتقرير"""
        try:
            from datetime import datetime
            import os

            # الحصول على اسم المستخدم الحالي
            current_user = self.get_current_user_name()

            html = f"""
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>{title}</title>
                <style>
                    @page {{
                        size: A4;
                        margin: 2cm;
                    }}
                    body {{
                        font-family: 'Arial', sans-serif;
                        direction: rtl;
                        text-align: right;
                        margin: 0;
                        padding: 20px;
                        background-color: white;
                    }}
                    .header {{
                        text-align: center;
                        border-bottom: 3px solid #2c3e50;
                        padding-bottom: 20px;
                        margin-bottom: 30px;
                    }}
                    .header h1 {{
                        color: #2c3e50;
                        font-size: 24px;
                        margin: 0;
                        font-weight: bold;
                    }}
                    .header h2 {{
                        color: #34495e;
                        font-size: 18px;
                        margin: 10px 0;
                    }}
                    .date-info {{
                        text-align: center;
                        margin: 20px 0;
                        font-size: 14px;
                        color: #7f8c8d;
                    }}
                    table {{
                        width: 100%;
                        border-collapse: collapse;
                        margin: 20px 0;
                        font-size: 12px;
                    }}
                    th, td {{
                        border: 1px solid #bdc3c7;
                        padding: 8px;
                        text-align: center;
                    }}
                    th {{
                        background-color: #3498db;
                        color: white;
                        font-weight: bold;
                    }}
                    tr:nth-child(even) {{
                        background-color: #f8f9fa;
                    }}
                    tr:nth-child(odd) {{
                        background-color: white;
                    }}
                    .footer {{
                        position: fixed;
                        bottom: 1cm;
                        left: 0;
                        right: 0;
                        text-align: center;
                        font-size: 12px;
                        background-color: white;
                        color: #2c3e50;
                        font-weight: bold;
                        padding: 10px;
                        border-top: 2px solid #3498db;
                    }}
                    @media print {{
                        .footer {{
                            position: fixed;
                            bottom: 0;
                        }}
                    }}
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>المملكة العربية السعودية</h1>
                    <h2>وزارة الدفاع - رئاسة هيئة الأركان العامة</h2>
                    <h2>قوات الدفاع الجوي الملكي السعودي</h2>
                    <h1>{title}</h1>
                </div>

                <div class="date-info">
                    <p>تاريخ الطباعة: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                    <p>عدد السجلات: {len(data)}</p>
                </div>

                <table>
                    <thead>
                        <tr>
            """

            # إضافة رؤوس الأعمدة
            if data:
                columns = list(data[0].keys())
                for col in columns:
                    html += f"<th>{col}</th>"

            html += """
                        </tr>
                    </thead>
                    <tbody>
            """

            # إضافة البيانات
            for row in data:
                html += "<tr>"
                for col in columns:
                    value = row[col] if row[col] is not None else ""
                    html += f"<td>{value}</td>"
                html += "</tr>"

            html += f"""
                    </tbody>
                </table>

                <div class="footer">
                    <p>تم إنشاء هذا التقرير بواسطة: {current_user} | نظام إدارة المخازن والمستودعات</p>
                </div>
            </body>
            </html>
            """

            return html

        except Exception as e:
            print(f"خطأ في إنشاء HTML: {e}")
            return f"<html><body><h1>خطأ في إنشاء التقرير: {e}</h1></body></html>"

    def export_report(self, title, data):
        """تصدير التقرير"""
        messagebox.showinfo("قريباً", "تصدير التقارير قيد التطوير")

    # ==================== التقارير الجديدة حسب الصور ====================

    def generate_inventory_status_report(self):
        """تقرير المخزون"""
        try:
            # إنشاء نافذة التقرير
            report_window = tk.Toplevel(self.parent)
            report_window.title("📊 تقرير المخزون")
            report_window.geometry("1200x700")
            report_window.resizable(False, False)

            # توسيط النافذة
            report_window.update_idletasks()
            x = (report_window.winfo_screenwidth() - 1200) // 2
            y = (report_window.winfo_screenheight() - 700) // 2
            report_window.geometry(f"1200x700+{x}+{y}")

            # الإطار الرئيسي
            main_frame = ttk_bs.Frame(report_window)
            main_frame.pack(fill=BOTH, expand=True, padx=20, pady=20)

            # شريط العلوي
            header_frame = ttk_bs.Frame(main_frame)
            header_frame.pack(fill=X, pady=(0, 20))

            # زر العودة للتقارير
            back_btn = ttk_bs.Button(
                header_frame,
                text="← العودة للتقارير",
                command=report_window.destroy,
                bootstyle="outline-secondary",
                width=22
            )
            back_btn.pack(side=LEFT)

            # زر طباعة
            print_btn = ttk_bs.Button(
                header_frame,
                text="🖨️ طباعة",
                command=lambda: self.print_inventory_report(report_window),
                bootstyle="primary",
                width=15
            )
            print_btn.pack(side=RIGHT)

            # العنوان
            title_label = ttk_bs.Label(
                header_frame,
                text="📊 تقرير المخزون",
                font=("Arial", 18, "bold"),
                bootstyle="primary"
            )
            title_label.pack(side=RIGHT, padx=20)

            # فلتر الأصناف
            filter_frame = ttk_bs.Frame(main_frame)
            filter_frame.pack(fill=X, pady=(0, 20))

            ttk_bs.Label(filter_frame, text="نوع العهدة:").pack(side=LEFT, padx=(0, 10))

            # إنشاء متغير الفلتر كخاصية للكلاس
            if not hasattr(self, 'custody_filter_var'):
                self.custody_filter_var = tk.StringVar()

            filter_combo = ttk_bs.Combobox(
                filter_frame,
                textvariable=self.custody_filter_var,
                values=["جميع أنواع العهدة", "مستهلكة", "مستديمة", "أخرى"],
                state="readonly",
                width=25
            )
            filter_combo.set("جميع أنواع العهدة")
            filter_combo.pack(side=LEFT, padx=10)

            # ربط تغيير القيمة بتحديث البيانات
            def on_filter_change(event=None):
                self.refresh_inventory_data(tree)

            filter_combo.bind('<<ComboboxSelected>>', on_filter_change)

            # زر تصفية
            filter_btn = ttk_bs.Button(
                filter_frame,
                text="🔍 تصفية",
                command=lambda: self.refresh_inventory_data(tree),
                bootstyle="info",
                width=18
            )
            filter_btn.pack(side=LEFT, padx=20)

            # جدول البيانات
            columns = ["#", "رقم الصنف", "اسم الصنف", "نوع العهدة", "التصنيف", "الكمية الحالية", "وحدة الصرف", "الحالة"]

            tree_frame = ttk_bs.Frame(main_frame)
            tree_frame.pack(fill=BOTH, expand=True)

            tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=15)
            tree.pack(side=LEFT, fill=BOTH, expand=True)

            # تعيين العناوين
            for col in columns:
                tree.heading(col, text=col)
                if col == "#":
                    tree.column(col, width=50, anchor="center")
                elif col == "اسم الصنف":
                    tree.column(col, width=200, anchor="center")
                elif col == "رقم الصنف":
                    tree.column(col, width=120, anchor="center")
                elif col == "الحالة":
                    tree.column(col, width=100, anchor="center")
                else:
                    tree.column(col, width=120, anchor="center")

            # حفظ مرجع للجدول لاستخدامه في التصفية
            self.inventory_tree = tree

            # تحميل البيانات الأولية
            self.refresh_inventory_data(tree)

            # شريط التمرير
            scrollbar = ttk.Scrollbar(tree_frame, orient=VERTICAL, command=tree.yview)
            tree.configure(yscrollcommand=scrollbar.set)
            scrollbar.pack(side=RIGHT, fill=Y)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء تقرير المخزون: {e}")

    def refresh_inventory_data(self, tree):
        """تحديث بيانات تقرير المخزون حسب الفلتر"""
        try:
            # مسح البيانات الحالية
            for item in tree.get_children():
                tree.delete(item)

            # بناء الاستعلام حسب الفلتر
            custody_filter = getattr(self, 'custody_filter_var', tk.StringVar()).get()

            if custody_filter == "جميع أنواع العهدة" or not custody_filter:
                query = """
                    SELECT item_number, item_name, custody_type, classification,
                           current_quantity, unit
                    FROM added_items
                    ORDER BY item_name ASC
                """
                params = ()
            else:
                query = """
                    SELECT item_number, item_name, custody_type, classification,
                           current_quantity, unit
                    FROM added_items
                    WHERE custody_type = ?
                    ORDER BY item_name ASC
                """
                params = (custody_filter,)

            data = db_manager.fetch_all(query, params)

            # إضافة البيانات
            for i, row in enumerate(data, 1):
                # تحديد الحالة بناءً على الكمية الحالية
                if row['current_quantity'] > 0:
                    status = "متوفر"
                    tag = 'available'
                else:
                    status = "غير متوفر"
                    tag = 'unavailable'

                tree.insert('', 'end', values=(
                    i,
                    row['item_number'],
                    row['item_name'],
                    row['custody_type'],
                    row['classification'],
                    int(row['current_quantity']),
                    row['unit'],
                    status
                ), tags=(tag,))

            # تنسيق الألوان
            tree.tag_configure('available', background='#e8f5e8')
            tree.tag_configure('unavailable', background='#ffebee')

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحديث بيانات المخزون: {e}")

    def print_inventory_report(self, report_window):
        """طباعة تقرير المخزون كـ HTML عربي"""
        try:
            import tempfile
            import os
            import webbrowser
            from datetime import datetime

            # جمع البيانات من الجدول
            tree = self.inventory_tree

            # إنشاء محتوى HTML عربي مع RTL
            html_content = f"""
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير المخزون</title>
    <style>
        @page {{
            size: A4 landscape;
            margin: 1cm;
        }}

        body {{
            font-family: 'Arial', 'Tahoma', 'Segoe UI', sans-serif;
            direction: rtl;
            text-align: center;
            margin: 0;
            padding: 20px;
            background-color: white;
        }}

        .header {{
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
        }}

        h1 {{
            color: #333;
            font-size: 28px;
            margin: 0 0 10px 0;
            font-weight: bold;
        }}

        .date {{
            color: #666;
            font-size: 16px;
            margin: 10px 0;
        }}

        .report-table {{
            width: 100%;
            border-collapse: collapse;
            margin: 20px auto;
            direction: rtl;
            font-size: 12px;
        }}

        .report-table th {{
            background-color: #4a5568;
            color: white;
            border: 1px solid #2d3748;
            padding: 12px 8px;
            text-align: center;
            font-weight: bold;
            font-size: 13px;
        }}

        .report-table td {{
            border: 1px solid #cbd5e0;
            padding: 10px 8px;
            text-align: center;
            background-color: #f7fafc;
        }}

        .report-table tr:nth-child(even) td {{
            background-color: #edf2f7;
        }}

        .report-table tr:hover td {{
            background-color: #e2e8f0;
        }}

        .available {{
            color: #38a169;
            font-weight: bold;
        }}

        .unavailable {{
            color: #e53e3e;
            font-weight: bold;
        }}

        .footer {{
            margin-top: 30px;
            text-align: center;
            font-size: 12px;
            color: #666;
            border-top: 1px solid #e2e8f0;
            padding-top: 15px;
        }}

        @media print {{
            body {{
                margin: 0;
                padding: 10px;
                font-size: 11px;
            }}
            .report-table {{
                font-size: 10px;
            }}
            .report-table th {{
                font-size: 11px;
                padding: 8px 6px;
            }}
            .report-table td {{
                padding: 6px 4px;
            }}
            h1 {{
                font-size: 24px;
            }}
            .date {{
                font-size: 14px;
            }}
        }}
    </style>
</head>
<body>
    <div class="header">
        <h1>تقرير المخزون</h1>
        <div class="date">تاريخ التقرير: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}</div>
    </div>

    <table class="report-table">
        <thead>
            <tr>
                <th style="width: 8%;">#</th>
                <th style="width: 12%;">رقم الصنف</th>
                <th style="width: 25%;">اسم الصنف</th>
                <th style="width: 12%;">نوع العهدة</th>
                <th style="width: 15%;">التصنيف</th>
                <th style="width: 10%;">الكمية الحالية</th>
                <th style="width: 10%;">وحدة الصرف</th>
                <th style="width: 8%;">الحالة</th>
            </tr>
        </thead>
        <tbody>
"""

            # إضافة البيانات للجدول
            for child in tree.get_children():
                values = tree.item(child)['values']

                # تحديد لون الحالة
                status_class = "available" if str(values[7]) == "متوفر" else "unavailable"

                html_content += f"""
            <tr>
                <td>{values[0]}</td>
                <td>{values[1]}</td>
                <td style="text-align: right; padding-right: 10px;">{values[2]}</td>
                <td>{values[3]}</td>
                <td>{values[4]}</td>
                <td>{values[5]}</td>
                <td>{values[6]}</td>
                <td class="{status_class}">{values[7]}</td>
            </tr>
"""

            html_content += f"""
        </tbody>
    </table>

    <div class="footer">
        <p>تم إنشاء هذا التقرير بواسطة نظام إدارة المخزون</p>
        <p>طُبع في: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}</p>
    </div>

    <script>
        // طباعة تلقائية عند فتح الصفحة
        window.onload = function() {{
            setTimeout(function() {{
                window.print();
            }}, 1000);
        }};
    </script>
</body>
</html>
"""

            # حفظ الملف HTML
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.html', mode='w', encoding='utf-8')
            temp_file.write(html_content)
            temp_file.close()

            # فتح الملف في المتصفح للطباعة
            webbrowser.open('file://' + temp_file.name)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في طباعة التقرير: {e}")
            print(f"خطأ في الطباعة: {e}")
            import traceback
            traceback.print_exc()

    def generate_transactions_operations_report(self):
        """تقرير عمليات الصرف - التقرير الثاني"""
        try:
            # إنشاء نافذة التقرير
            report_window = tk.Toplevel(self.parent)
            report_window.title("📋 تقرير عمليات الصرف")
            report_window.geometry("1200x800")
            report_window.resizable(False, False)

            # توسيط النافذة
            report_window.update_idletasks()
            x = (report_window.winfo_screenwidth() - 1200) // 2
            y = (report_window.winfo_screenheight() - 800) // 2
            report_window.geometry(f"1200x800+{x}+{y}")

            # الإطار الرئيسي
            main_frame = ttk_bs.Frame(report_window)
            main_frame.pack(fill=BOTH, expand=True, padx=20, pady=20)

            # شريط العلوي
            header_frame = ttk_bs.Frame(main_frame)
            header_frame.pack(fill=X, pady=(0, 20))

            # زر العودة للتقارير
            back_btn = ttk_bs.Button(
                header_frame,
                text="← العودة للتقارير",
                command=report_window.destroy,
                bootstyle="outline-secondary",
                width=22
            )
            back_btn.pack(side=LEFT)

            # زر طباعة
            def print_transactions_report():
                """طباعة تقرير عمليات الصرف كـ PDF"""
                self.print_transactions_operations_report(tree, beneficiary_combo.get(), from_date_entry.entry.get(), to_date_entry.entry.get())

            print_btn = ttk_bs.Button(
                header_frame,
                text="🖨️ طباعة",
                command=print_transactions_report,
                bootstyle="primary",
                width=15
            )
            print_btn.pack(side=RIGHT)

            # العنوان
            title_label = ttk_bs.Label(
                header_frame,
                text="📋 تقرير عمليات الصرف",
                font=("Arial", 18, "bold"),
                bootstyle="primary"
            )
            title_label.pack(side=RIGHT, padx=20)

            # فلاتر التقرير
            filters_frame = ttk_bs.Frame(main_frame)
            filters_frame.pack(fill=X, pady=(0, 20))

            # فلتر المستفيد
            beneficiary_frame = ttk_bs.Frame(filters_frame)
            beneficiary_frame.pack(side=LEFT, padx=10)

            ttk_bs.Label(beneficiary_frame, text="المستفيد").pack()

            # جلب قائمة المستفيدين من قاعدة البيانات مع تشخيص شامل
            print("🔍 جلب قائمة المستفيدين...")

            # أولاً: التحقق من وجود مستفيدين في النظام
            all_beneficiaries_query = "SELECT COUNT(*) as count FROM beneficiaries WHERE is_active = 1"
            all_beneficiaries_count = db_manager.fetch_one(all_beneficiaries_query)
            print(f"📊 إجمالي المستفيدين النشطين: {all_beneficiaries_count['count'] if all_beneficiaries_count else 0}")

            # ثانياً: التحقق من وجود عمليات في النظام
            all_transactions_query = "SELECT COUNT(*) as count FROM transactions"
            all_transactions_count = db_manager.fetch_one(all_transactions_query)
            print(f"📊 إجمالي العمليات: {all_transactions_count['count'] if all_transactions_count else 0}")

            # ثالثاً: جلب المستفيدين الذين لديهم عمليات
            beneficiaries_query = """
                SELECT DISTINCT b.name, b.id, COUNT(t.id) as transactions_count
                FROM beneficiaries b
                INNER JOIN transactions t ON b.id = t.beneficiary_id
                WHERE b.is_active = 1
                GROUP BY b.id, b.name
                ORDER BY b.name
            """
            beneficiaries_data = db_manager.fetch_all(beneficiaries_query)
            print(f"📊 المستفيدين الذين لديهم عمليات: {len(beneficiaries_data)}")

            # طباعة تفاصيل المستفيدين للتشخيص
            for i, beneficiary in enumerate(beneficiaries_data[:5]):  # أول 5 فقط
                print(f"  {i+1}. {beneficiary['name']} (عمليات: {beneficiary['transactions_count']})")

            beneficiaries_list = ["-- جميع المستفيدين --"] + [row['name'] for row in beneficiaries_data]
            print(f"📋 قائمة المستفيدين النهائية: {len(beneficiaries_list)} عنصر")

            beneficiary_combo = ttk_bs.Combobox(
                beneficiary_frame,
                values=beneficiaries_list,
                state="readonly",
                width=55
            )
            beneficiary_combo.set("-- جميع المستفيدين --")
            beneficiary_combo.pack()

            # فلتر التاريخ من
            from_date_frame = ttk_bs.Frame(filters_frame)
            from_date_frame.pack(side=LEFT, padx=10)

            ttk_bs.Label(from_date_frame, text="من تاريخ").pack()
            from_date_entry = ttk_bs.DateEntry(from_date_frame, width=30)
            from_date_entry.pack()

            # فلتر التاريخ إلى
            to_date_frame = ttk_bs.Frame(filters_frame)
            to_date_frame.pack(side=LEFT, padx=10)

            ttk_bs.Label(to_date_frame, text="إلى تاريخ").pack()
            to_date_entry = ttk_bs.DateEntry(to_date_frame, width=30)
            to_date_entry.pack()

            # بطاقات الإحصائيات
            stats_frame = ttk_bs.Frame(main_frame)
            stats_frame.pack(fill=X, pady=(0, 20))

            # إجمالي العمليات
            operations_card = ttk_bs.Frame(stats_frame, bootstyle="primary")
            operations_card.pack(side=RIGHT, padx=5, fill=X, expand=True)

            ttk_bs.Label(
                operations_card,
                text="إجمالي العمليات",
                font=("Arial", 12, "bold"),
                bootstyle="inverse-primary"
            ).pack(pady=5)

            operations_count = db_manager.fetch_one("SELECT COUNT(*) as count FROM transactions")
            operations_count_label = ttk_bs.Label(
                operations_card,
                text=str(operations_count['count'] if operations_count else 0),
                font=("Arial", 24, "bold"),
                bootstyle="inverse-primary"
            )
            operations_count_label.pack(pady=5)

            # إجمالي الأصناف
            items_card = ttk_bs.Frame(stats_frame, bootstyle="success")
            items_card.pack(side=RIGHT, padx=5, fill=X, expand=True)

            ttk_bs.Label(
                items_card,
                text="إجمالي الأصناف",
                font=("Arial", 12, "bold"),
                bootstyle="inverse-success"
            ).pack(pady=5)

            items_count = db_manager.fetch_one("SELECT COUNT(DISTINCT item_id) as count FROM transaction_items")
            items_count_label = ttk_bs.Label(
                items_card,
                text=str(items_count['count'] if items_count else 0),
                font=("Arial", 24, "bold"),
                bootstyle="inverse-success"
            )
            items_count_label.pack(pady=5)

            # إجمالي الكميات
            quantities_card = ttk_bs.Frame(stats_frame, bootstyle="info")
            quantities_card.pack(side=RIGHT, padx=5, fill=X, expand=True)

            ttk_bs.Label(
                quantities_card,
                text="إجمالي الكميات",
                font=("Arial", 12, "bold"),
                bootstyle="inverse-info"
            ).pack(pady=5)

            quantities_sum = db_manager.fetch_one("SELECT SUM(quantity) as sum FROM transaction_items")
            quantities_sum_label = ttk_bs.Label(
                quantities_card,
                text=str(int(quantities_sum['sum']) if quantities_sum and quantities_sum['sum'] else 0),
                font=("Arial", 24, "bold"),
                bootstyle="inverse-info"
            )
            quantities_sum_label.pack(pady=5)

            # زر تصفية
            def apply_filter():
                """تطبيق الفلتر على البيانات"""
                try:
                    # بناء الاستعلام حسب الفلاتر - محسن
                    base_query = """
                        SELECT t.id, t.transaction_number, b.name as beneficiary_name, b.number as beneficiary_number,
                               t.transaction_date, COUNT(ti.id) as items_count,
                               COALESCE(SUM(ti.quantity), 0) as total_quantity
                        FROM transactions t
                        INNER JOIN beneficiaries b ON t.beneficiary_id = b.id
                        LEFT JOIN transaction_items ti ON t.id = ti.transaction_id
                        WHERE b.is_active = 1
                    """

                    conditions = []
                    params = []

                    # فلتر المستفيد - إصلاح محسن
                    selected_beneficiary = beneficiary_combo.get()
                    if selected_beneficiary:
                        selected_beneficiary = selected_beneficiary.strip()

                    print(f"🔍 المستفيد المختار: '{selected_beneficiary}'")

                    # التحقق من أن المستفيد ليس القيمة الافتراضية
                    if (selected_beneficiary and
                        selected_beneficiary != "-- جميع المستفيدين --" and
                        selected_beneficiary != "" and
                        not selected_beneficiary.startswith("--")):

                        # استخدام البحث الدقيق أولاً ثم LIKE كبديل
                        conditions.append("(b.name = ? OR b.name LIKE ?)")
                        params.extend([selected_beneficiary, f"%{selected_beneficiary}%"])
                        print(f"✅ تم إضافة فلتر المستفيد: {selected_beneficiary}")
                    else:
                        print("ℹ️ لم يتم تحديد مستفيد محدد - عرض جميع المستفيدين")

                    # فلتر التاريخ من
                    from_date = from_date_entry.entry.get().strip()
                    print(f"من تاريخ: '{from_date}'")
                    if from_date:
                        conditions.append("DATE(t.transaction_date) >= ?")
                        params.append(from_date)
                        print(f"تم إضافة فلتر التاريخ من: {from_date}")

                    # فلتر التاريخ إلى
                    to_date = to_date_entry.entry.get().strip()
                    print(f"إلى تاريخ: '{to_date}'")
                    if to_date:
                        conditions.append("DATE(t.transaction_date) <= ?")
                        params.append(to_date)
                        print(f"تم إضافة فلتر التاريخ إلى: {to_date}")

                    # إضافة الشروط للاستعلام
                    if conditions:
                        base_query += " AND " + " AND ".join(conditions)

                    base_query += """
                        GROUP BY t.id, t.transaction_number, b.name, b.number, t.transaction_date
                        ORDER BY t.transaction_date DESC
                        LIMIT 100
                    """

                    print(f"🔍 استعلام الفلترة النهائي: {base_query}")
                    print(f"📊 معاملات الفلترة: {params}")

                    # تنفيذ الاستعلام مع معالجة أخطاء مفصلة
                    try:
                        filtered_data = db_manager.fetch_all(base_query, params)
                        print(f"✅ تم تنفيذ الاستعلام بنجاح")
                        print(f"📈 عدد النتائج المفلترة: {len(filtered_data)}")

                        # طباعة عينة من النتائج للتشخيص
                        if filtered_data:
                            print("📋 عينة من النتائج:")
                            for i, row in enumerate(filtered_data[:3]):  # أول 3 نتائج
                                print(f"  {i+1}. {row['beneficiary_name']} - {row['transaction_number']}")
                        else:
                            print("❌ لا توجد نتائج - سيتم التحقق من السبب...")

                            # تشخيص إضافي: اختبار الاستعلام بدون فلاتر
                            test_query = """
                                SELECT COUNT(*) as count
                                FROM transactions t
                                LEFT JOIN beneficiaries b ON t.beneficiary_id = b.id
                                LEFT JOIN transaction_items ti ON t.id = ti.transaction_id
                            """
                            test_result = db_manager.fetch_one(test_query)
                            print(f"🔍 إجمالي العمليات في قاعدة البيانات: {test_result['count'] if test_result else 0}")

                            # اختبار البحث عن المستفيد المحدد
                            if selected_beneficiary and selected_beneficiary != "-- جميع المستفيدين --":
                                beneficiary_test_query = """
                                    SELECT COUNT(*) as count, b.name
                                    FROM transactions t
                                    LEFT JOIN beneficiaries b ON t.beneficiary_id = b.id
                                    WHERE b.name = ? OR b.name LIKE ?
                                    GROUP BY b.name
                                """
                                beneficiary_test_result = db_manager.fetch_all(beneficiary_test_query, [selected_beneficiary, f"%{selected_beneficiary}%"])
                                print(f"🔍 نتائج البحث عن المستفيد '{selected_beneficiary}': {len(beneficiary_test_result)} نتيجة")
                                for result in beneficiary_test_result:
                                    print(f"  - {result['name']}: {result['count']} عملية")

                    except Exception as query_error:
                        print(f"❌ خطأ في تنفيذ الاستعلام: {query_error}")
                        import traceback
                        traceback.print_exc()
                        messagebox.showerror("خطأ في قاعدة البيانات", f"فشل في تنفيذ استعلام الفلترة: {query_error}")
                        return

                    # مسح البيانات الحالية
                    for item in tree.get_children():
                        tree.delete(item)

                    # إضافة البيانات المفلترة مع تشخيص مفصل
                    print(f"🔄 بدء إضافة {len(filtered_data)} عنصر للجدول...")

                    for i, row in enumerate(filtered_data, 1):
                        print(f"📝 إضافة العنصر {i}: {row['transaction_number']} - {row['beneficiary_name']}")

                        try:
                            item_id = tree.insert('', 'end', values=(
                                i,
                                row['transaction_number'],
                                row['beneficiary_name'] or '',
                                row['beneficiary_number'] or '',
                                row['transaction_date'],
                                int(row['items_count']),
                                int(row['total_quantity']),
                                "+"
                            ))
                            # حفظ معرف المعاملة كـ tag للوصول إليه لاحقاً
                            tree.item(item_id, tags=(str(row['id']),))
                            print(f"✅ تم إضافة العنصر {i} بنجاح")

                        except Exception as insert_error:
                            print(f"❌ خطأ في إضافة العنصر {i}: {insert_error}")
                            print(f"📊 بيانات العنصر: {row}")

                    print(f"✅ تم الانتهاء من إضافة البيانات للجدول")

                    # التحقق من عدد العناصر في الجدول
                    tree_items_count = len(tree.get_children())
                    print(f"📊 عدد العناصر في الجدول بعد الإضافة: {tree_items_count}")

                    if tree_items_count != len(filtered_data):
                        print(f"⚠️ تحذير: عدم تطابق العناصر! متوقع: {len(filtered_data)}, فعلي: {tree_items_count}")

                    # تحديث الإحصائيات مع تشخيص
                    operations_count_filtered = len(filtered_data)
                    items_count_filtered = sum(int(row['items_count']) for row in filtered_data)
                    quantities_sum_filtered = sum(int(row['total_quantity']) for row in filtered_data)

                    print(f"📊 حساب الإحصائيات:")
                    print(f"  - عدد العمليات: {operations_count_filtered}")
                    print(f"  - عدد الأصناف: {items_count_filtered}")
                    print(f"  - إجمالي الكميات: {quantities_sum_filtered}")

                    # تحديث بطاقات الإحصائيات
                    try:
                        operations_count_label.config(text=str(operations_count_filtered))
                        items_count_label.config(text=str(items_count_filtered))
                        quantities_sum_label.config(text=str(quantities_sum_filtered))
                        print("✅ تم تحديث بطاقات الإحصائيات بنجاح")
                    except Exception as stats_error:
                        print(f"❌ خطأ في تحديث الإحصائيات: {stats_error}")

                    print("✅ تم تحديث الفلترة والإحصائيات بنجاح")

                    # رسالة تأكيد للمستخدم مع تفاصيل أكثر
                    if len(filtered_data) == 0:
                        # إضافة معلومات تشخيصية
                        filter_details = []
                        if selected_beneficiary and selected_beneficiary != "-- جميع المستفيدين --":
                            filter_details.append(f"المستفيد: {selected_beneficiary}")
                        if from_date:
                            filter_details.append(f"من تاريخ: {from_date}")
                        if to_date:
                            filter_details.append(f"إلى تاريخ: {to_date}")

                        filter_text = "\n".join(filter_details) if filter_details else "بدون فلاتر محددة"

                        # التحقق من وجود بيانات أساسية
                        total_transactions = db_manager.fetch_one("SELECT COUNT(*) as count FROM transactions")
                        total_count = total_transactions['count'] if total_transactions else 0

                        message = f"""لا توجد عمليات تطابق معايير البحث:

{filter_text}

إجمالي العمليات في النظام: {total_count}

جرب:
• تغيير المستفيد المختار
• توسيع نطاق التاريخ
• إزالة جميع الفلاتر"""

                        messagebox.showinfo("نتيجة الفلترة", message)
                    else:
                        # عرض رسالة نجاح مؤقتة
                        success_label = ttk_bs.Label(
                            filters_frame,
                            text=f"✅ تم العثور على {len(filtered_data)} عملية",
                            bootstyle="success"
                        )
                        success_label.pack(side=LEFT, padx=10)

                        # إخفاء الرسالة بعد 3 ثوان
                        def hide_success_message():
                            try:
                                success_label.destroy()
                            except:
                                pass

                        report_window.after(3000, hide_success_message)

                except Exception as e:
                    messagebox.showerror("خطأ", f"فشل في تطبيق الفلتر: {e}")
                    print(f"❌ خطأ في الفلترة: {e}")
                    import traceback
                    traceback.print_exc()

            filter_btn = ttk_bs.Button(
                filters_frame,
                text="🔍 تصفية",
                command=apply_filter,
                bootstyle="info",
                width=18
            )
            filter_btn.pack(side=LEFT, padx=10)
            
            # زر إعادة تعيين الفلاتر
            def reset_filters():
                """إعادة تعيين جميع الفلاتر"""
                beneficiary_combo.set("-- جميع المستفيدين --")
                from_date_entry.entry.delete(0, 'end')
                to_date_entry.entry.delete(0, 'end')
                apply_filter()  # تطبيق الفلتر الفارغ
            
            reset_btn = ttk_bs.Button(
                filters_frame,
                text="🔄 إعادة تعيين",
                command=reset_filters,
                bootstyle="secondary",
                width=18
            )
            reset_btn.pack(side=LEFT, padx=10)

            # جدول البيانات
            columns = ["#", "رقم العملية", "المستفيد", "الرقم", "التاريخ", "عدد الأصناف", "إجمالي الكميات", "الإجراءات"]

            tree_frame = ttk_bs.Frame(main_frame)
            tree_frame.pack(fill=BOTH, expand=True)

            tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=12)
            tree.pack(side=LEFT, fill=BOTH, expand=True)

            # تعيين العناوين
            for col in columns:
                tree.heading(col, text=col)
                if col == "#":
                    tree.column(col, width=50, anchor="center")
                elif col == "الإجراءات":
                    tree.column(col, width=100, anchor="center")
                else:
                    tree.column(col, width=120, anchor="center")

            # جلب البيانات الأولية
            query = """
                SELECT t.id, t.transaction_number, b.name as beneficiary_name, b.number as beneficiary_number,
                       t.transaction_date, COUNT(ti.id) as items_count,
                       COALESCE(SUM(ti.quantity), 0) as total_quantity
                FROM transactions t
                INNER JOIN beneficiaries b ON t.beneficiary_id = b.id
                LEFT JOIN transaction_items ti ON t.id = ti.transaction_id
                WHERE b.is_active = 1
                GROUP BY t.id, t.transaction_number, b.name, b.number, t.transaction_date
                ORDER BY t.transaction_date DESC
                LIMIT 100
            """
            data = db_manager.fetch_all(query)
            print(f"✅ تم تحميل {len(data)} عملية في التقرير")

            # إضافة البيانات
            for i, row in enumerate(data, 1):
                item_id = tree.insert('', 'end', values=(
                    i,
                    row['transaction_number'],
                    row['beneficiary_name'] or '',
                    row['beneficiary_number'] or '',
                    row['transaction_date'],
                    int(row['items_count']),
                    int(row['total_quantity']),
                    "+"
                ))
                # حفظ معرف المعاملة كـ tag للوصول إليه لاحقاً
                tree.item(item_id, tags=(str(row['id']),))

            # شريط التمرير
            scrollbar = ttk.Scrollbar(tree_frame, orient=VERTICAL, command=tree.yview)
            tree.configure(yscrollcommand=scrollbar.set)
            scrollbar.pack(side=RIGHT, fill=Y)

            # وظيفة عرض تفاصيل المعاملة
            def show_transaction_details(event):
                """عرض تفاصيل المعاملة عند النقر على زر +"""
                try:
                    # تحديد العنصر المنقور عليه
                    item = tree.identify('item', event.x, event.y)
                    if not item:
                        print("❌ لم يتم تحديد عنصر")
                        return

                    # التحقق من النقر على عمود الإجراءات - إصلاح الخطأ
                    try:
                        column = tree.identify_column(event.x)  # إصلاح: معامل واحد فقط
                        print(f"🖱️ تم النقر على العمود: {column}")
                    except Exception as col_error:
                        print(f"❌ خطأ في تحديد العمود: {col_error}")
                        # استخدام طريقة بديلة
                        region = tree.identify_region(event.x, event.y)
                        if region == "cell":
                            column = "#8"  # افتراض أنه عمود الإجراءات
                        else:
                            return

                    # التحقق من عمود الإجراءات (العمود الأخير)
                    if column == "#8":  # عمود الإجراءات
                        values = tree.item(item)['values']
                        if len(values) >= 2:
                            transaction_number = values[1]  # رقم المعاملة
                            print(f"🔍 محاولة فتح تفاصيل المعاملة: {transaction_number}")

                            # جلب معرف المعاملة من قاعدة البيانات
                            transaction_query = "SELECT id FROM transactions WHERE transaction_number = ?"
                            transaction_result = db_manager.fetch_one(transaction_query, (transaction_number,))

                            if transaction_result:
                                transaction_id = transaction_result['id']
                                print(f"✅ تم العثور على المعاملة بالمعرف: {transaction_id}")

                                # فتح نافذة تفاصيل المعاملة
                                from ui.transaction_details_window import TransactionDetailsWindow
                                TransactionDetailsWindow(report_window, transaction_id, None)
                                print("✅ تم فتح نافذة تفاصيل المعاملة")
                            else:
                                print(f"❌ لم يتم العثور على المعاملة برقم: {transaction_number}")
                                messagebox.showerror("خطأ", f"لم يتم العثور على المعاملة برقم: {transaction_number}")
                        else:
                            print("❌ بيانات العنصر غير مكتملة")
                    else:
                        print(f"ℹ️ تم النقر على عمود آخر: {column}")

                except Exception as e:
                    print(f"❌ خطأ في عرض التفاصيل: {e}")
                    import traceback
                    traceback.print_exc()
                    messagebox.showerror("خطأ", f"فشل في فتح تفاصيل المعاملة: {e}")

            # إضافة وظيفة للنقر المزدوج كبديل
            def show_transaction_details_double_click(event):
                """عرض تفاصيل المعاملة عند النقر المزدوج على أي مكان في الصف"""
                try:
                    item = tree.identify('item', event.x, event.y)
                    if not item:
                        return

                    values = tree.item(item)['values']
                    if len(values) >= 2:
                        transaction_number = values[1]
                        print(f"🔍 النقر المزدوج - فتح تفاصيل المعاملة: {transaction_number}")

                        # جلب معرف المعاملة من قاعدة البيانات
                        transaction_query = "SELECT id FROM transactions WHERE transaction_number = ?"
                        transaction_result = db_manager.fetch_one(transaction_query, (transaction_number,))

                        if transaction_result:
                            transaction_id = transaction_result['id']
                            from ui.transaction_details_window import TransactionDetailsWindow
                            TransactionDetailsWindow(report_window, transaction_id, None)
                        else:
                            messagebox.showerror("خطأ", f"لم يتم العثور على المعاملة برقم: {transaction_number}")

                except Exception as e:
                    print(f"❌ خطأ في النقر المزدوج: {e}")
                    import traceback
                    traceback.print_exc()

            # إضافة طريقة بديلة أبسط - قائمة منبثقة
            def show_context_menu(event):
                """عرض قائمة منبثقة للإجراءات"""
                try:
                    item = tree.identify('item', event.x, event.y)
                    if not item:
                        return

                    # إنشاء قائمة منبثقة
                    context_menu = tk.Menu(report_window, tearoff=0)
                    context_menu.add_command(
                        label="📋 عرض تفاصيل العملية",
                        command=lambda: open_transaction_details_from_item(item)
                    )

                    try:
                        context_menu.tk_popup(event.x_root, event.y_root)
                    finally:
                        context_menu.grab_release()

                except Exception as e:
                    print(f"❌ خطأ في القائمة المنبثقة: {e}")

            def open_transaction_details_from_item(item):
                """فتح تفاصيل المعاملة من عنصر الجدول"""
                try:
                    values = tree.item(item)['values']
                    if len(values) >= 2:
                        transaction_number = values[1]
                        print(f"🔍 فتح تفاصيل المعاملة: {transaction_number}")

                        # جلب معرف المعاملة من قاعدة البيانات
                        transaction_query = "SELECT id FROM transactions WHERE transaction_number = ?"
                        transaction_result = db_manager.fetch_one(transaction_query, (transaction_number,))

                        if transaction_result:
                            transaction_id = transaction_result['id']
                            print(f"✅ تم العثور على المعاملة بالمعرف: {transaction_id}")

                            # فتح نافذة تفاصيل المعاملة
                            from ui.transaction_details_window import TransactionDetailsWindow
                            TransactionDetailsWindow(report_window, transaction_id, None)
                            print("✅ تم فتح نافذة تفاصيل المعاملة")
                        else:
                            print(f"❌ لم يتم العثور على المعاملة برقم: {transaction_number}")
                            messagebox.showerror("خطأ", f"لم يتم العثور على المعاملة برقم: {transaction_number}")
                    else:
                        print("❌ بيانات العنصر غير مكتملة")

                except Exception as e:
                    print(f"❌ خطأ في فتح التفاصيل: {e}")
                    import traceback
                    traceback.print_exc()
                    messagebox.showerror("خطأ", f"فشل في فتح تفاصيل المعاملة: {e}")

            # ربط الأحداث بالجدول
            tree.bind("<Button-1>", show_transaction_details)  # النقر العادي على زر +
            tree.bind("<Double-1>", show_transaction_details_double_click)  # النقر المزدوج
            tree.bind("<Button-3>", show_context_menu)  # النقر بالزر الأيمن - قائمة منبثقة

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء تقرير عمليات الصرف: {e}")

    def print_transactions_operations_report(self, tree, selected_beneficiary, from_date, to_date):
        """طباعة تقرير عمليات الصرف كـ HTML احترافي مع دعم كامل للعربية"""
        try:
            import tempfile
            import os
            import webbrowser
            from datetime import datetime

            # إنشاء ملف HTML مؤقت
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.html', mode='w', encoding='utf-8')

            # جمع البيانات من الجدول
            table_data = []
            for child in tree.get_children():
                values = tree.item(child)['values']
                table_data.append({
                    'sequence': str(values[0]),
                    'transaction_number': str(values[1]),
                    'beneficiary_name': str(values[2]),
                    'beneficiary_number': str(values[3]),
                    'transaction_date': str(values[4]),
                    'items_count': str(values[5]),
                    'total_quantity': str(values[6])
                })

            # إنشاء محتوى HTML
            html_content = f"""
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير عمليات الصرف</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap');

        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}

        body {{
            font-family: 'Cairo', 'Arial', 'Tahoma', 'Segoe UI', sans-serif;
            direction: rtl;
            text-align: right;
            background-color: #f8f9fa;
            color: #333;
            line-height: 1.6;
        }}

        .container {{
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }}

        .header {{
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
        }}

        .header h1 {{
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
        }}

        .header .subtitle {{
            font-size: 16px;
            opacity: 0.9;
        }}

        .filter-info {{
            background-color: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-right: 4px solid #2196f3;
        }}

        .filter-info h3 {{
            color: #1976d2;
            margin-bottom: 10px;
            font-size: 18px;
        }}

        .filter-info p {{
            margin: 5px 0;
            color: #555;
        }}

        .table-container {{
            overflow-x: auto;
            margin: 20px 0;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }}

        table {{
            width: 100%;
            border-collapse: collapse;
            background-color: white;
            font-size: 14px;
        }}

        thead {{
            background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
            color: white;
        }}

        thead th {{
            padding: 15px 10px;
            text-align: center;
            font-weight: 600;
            font-size: 16px;
            border-bottom: 2px solid #e2e8f0;
        }}

        tbody tr {{
            transition: background-color 0.3s ease;
        }}

        tbody tr:nth-child(even) {{
            background-color: #f8f9fa;
        }}

        tbody tr:hover {{
            background-color: #e8f4fd;
        }}

        tbody td {{
            padding: 12px 10px;
            text-align: center;
            border-bottom: 1px solid #e2e8f0;
            vertical-align: middle;
        }}

        .sequence-col {{
            background-color: #667eea;
            color: white;
            font-weight: 600;
            width: 60px;
        }}

        .transaction-number {{
            font-weight: 600;
            color: #2d3748;
        }}

        .beneficiary-name {{
            font-weight: 500;
            color: #4a5568;
        }}

        .quantity-high {{
            background-color: #c6f6d5;
            color: #22543d;
            font-weight: 600;
            border-radius: 4px;
            padding: 4px 8px;
        }}

        .quantity-medium {{
            background-color: #fef5e7;
            color: #744210;
            font-weight: 600;
            border-radius: 4px;
            padding: 4px 8px;
        }}

        .quantity-low {{
            background-color: #fed7d7;
            color: #742a2a;
            font-weight: 600;
            border-radius: 4px;
            padding: 4px 8px;
        }}

        .footer {{
            margin-top: 30px;
            text-align: center;
            padding: 20px;
            background-color: #f7fafc;
            border-radius: 8px;
            color: #666;
        }}

        .stats-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }}

        .stat-card {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }}

        .stat-card h3 {{
            font-size: 24px;
            margin-bottom: 5px;
        }}

        .stat-card p {{
            opacity: 0.9;
            font-size: 14px;
        }}

        @media print {{
            body {{
                background-color: white;
            }}

            .container {{
                box-shadow: none;
                max-width: none;
                margin: 0;
                padding: 10px;
            }}

            .header {{
                background: #667eea !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }}

            .table-container {{
                box-shadow: none;
            }}

            thead {{
                background: #4a5568 !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }}

            .sequence-col {{
                background-color: #667eea !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }}

            .quantity-high, .quantity-medium, .quantity-low {{
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }}

            .stat-card {{
                background: #667eea !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }}

            tbody tr:nth-child(even) {{
                background-color: #f8f9fa !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }}
        }}

        @page {{
            size: A4 landscape;
            margin: 1cm;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📋 تقرير عمليات الصرف</h1>
            <div class="subtitle">تقرير شامل لجميع عمليات الصرف في النظام</div>
        </div>"""


            # إضافة معلومات الفلتر
            filter_info_html = f"""
        <div class="filter-info">
            <h3>📊 معلومات التقرير</h3>
            <p><strong>تاريخ التقرير:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>"""

            if selected_beneficiary != "-- جميع المستفيدين --":
                filter_info_html += f"<p><strong>المستفيد:</strong> {selected_beneficiary}</p>"
            if from_date:
                filter_info_html += f"<p><strong>من تاريخ:</strong> {from_date}</p>"
            if to_date:
                filter_info_html += f"<p><strong>إلى تاريخ:</strong> {to_date}</p>"

            filter_info_html += f"<p><strong>عدد العمليات:</strong> {len(table_data)}</p></div>"

            # لا نضيف معلومات التقرير في الطباعة
            # html_content += filter_info_html

            # حساب الإحصائيات
            total_operations = len(table_data)
            total_items = sum(int(row['items_count']) for row in table_data)
            total_quantities = sum(int(row['total_quantity']) for row in table_data)

            # لا نضيف بطاقات الإحصائيات في الطباعة
            # stats_html = f"""
            # <div class="stats-grid">
            #     <div class="stat-card">
            #         <h3>{total_operations}</h3>
            #         <p>إجمالي العمليات</p>
            #     </div>
            #     <div class="stat-card">
            #         <h3>{total_items}</h3>
            #         <p>إجمالي الأصناف</p>
            #     </div>
            #     <div class="stat-card">
            #         <h3>{total_quantities}</h3>
            #         <p>إجمالي الكميات</p>
            #     </div>
            # </div>"""
            #
            # html_content += stats_html

            # إضافة الجدول
            table_html = """
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>#</th>
                        <th>رقم العملية</th>
                        <th>المستفيد</th>
                        <th>الرقم</th>
                        <th>التاريخ</th>
                        <th>عدد الأصناف</th>
                        <th>إجمالي الكميات</th>
                    </tr>
                </thead>
                <tbody>"""

            # إضافة صفوف البيانات
            for row in table_data:
                quantity = int(row['total_quantity'])
                quantity_class = ""
                if quantity > 50:
                    quantity_class = "quantity-high"
                elif quantity > 20:
                    quantity_class = "quantity-medium"
                else:
                    quantity_class = "quantity-low"

                table_html += f"""
                    <tr>
                        <td class="sequence-col">{row['sequence']}</td>
                        <td class="transaction-number">{row['transaction_number']}</td>
                        <td class="beneficiary-name">{row['beneficiary_name']}</td>
                        <td>{row['beneficiary_number']}</td>
                        <td>{row['transaction_date']}</td>
                        <td>{row['items_count']}</td>
                        <td class="{quantity_class}">{row['total_quantity']}</td>
                    </tr>"""

            table_html += """
                </tbody>
            </table>
        </div>"""

            html_content += table_html

            # إضافة التذييل
            footer_html = f"""
        <div class="footer">
            <p>تم إنشاء هذا التقرير بواسطة نظام إدارة المخزون</p>
            <p>تاريخ الطباعة: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        </div>
    </div>

    <script>
        // طباعة تلقائية عند فتح الصفحة
        window.onload = function() {{
            setTimeout(function() {{
                window.print();
            }}, 1000);
        }};
    </script>
</body>
</html>"""

            html_content += footer_html

            # كتابة المحتوى إلى الملف
            temp_file.write(html_content)
            temp_file.close()

            # فتح الملف في المتصفح للطباعة
            webbrowser.open('file://' + os.path.abspath(temp_file.name))

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في طباعة التقرير: {e}")
            print(f"خطأ في الطباعة: {e}")
            import traceback
            traceback.print_exc()

    def show_transaction_details_window(self, transaction_number, parent_window):
        """عرض نافذة تفاصيل عملية الصرف"""
        try:
            # إنشاء نافذة التفاصيل
            details_window = tk.Toplevel(parent_window)
            details_window.title(f"تفاصيل عملية الصرف - {transaction_number}")
            details_window.geometry("1000x600")
            details_window.resizable(False, False)

            # توسيط النافذة
            details_window.update_idletasks()
            x = (details_window.winfo_screenwidth() - 1000) // 2
            y = (details_window.winfo_screenheight() - 600) // 2
            details_window.geometry(f"1000x600+{x}+{y}")

            # الإطار الرئيسي
            main_frame = ttk_bs.Frame(details_window)
            main_frame.pack(fill=BOTH, expand=True, padx=20, pady=20)

            # العنوان
            title_label = ttk_bs.Label(
                main_frame,
                text=f"📋 تفاصيل عملية الصرف - {transaction_number}",
                font=("Arial", 16, "bold"),
                bootstyle="primary"
            )
            title_label.pack(pady=(0, 20))

            # جلب تفاصيل المعاملة
            transaction_query = """
                SELECT t.*, b.name as beneficiary_name, b.number as beneficiary_number,
                       b.rank, b.department, u.full_name as user_name
                FROM transactions t
                LEFT JOIN beneficiaries b ON t.beneficiary_id = b.id
                LEFT JOIN users u ON t.user_id = u.id
                WHERE t.transaction_number = ?
            """
            transaction_data = db_manager.fetch_one(transaction_query, (transaction_number,))

            if not transaction_data:
                messagebox.showerror("خطأ", "لم يتم العثور على المعاملة")
                details_window.destroy()
                return

            # معلومات المعاملة
            info_frame = ttk_bs.LabelFrame(main_frame, text="معلومات المعاملة", padding=15)
            info_frame.pack(fill=X, pady=(0, 20))

            # الصف الأول
            row1_frame = ttk_bs.Frame(info_frame)
            row1_frame.pack(fill=X, pady=5)

            ttk_bs.Label(row1_frame, text=f"رقم العملية: {transaction_data['transaction_number']}", font=("Arial", 10, "bold")).pack(side=LEFT, padx=20)
            ttk_bs.Label(row1_frame, text=f"التاريخ: {transaction_data['transaction_date']}", font=("Arial", 10)).pack(side=LEFT, padx=20)

            # الصف الثاني
            row2_frame = ttk_bs.Frame(info_frame)
            row2_frame.pack(fill=X, pady=5)

            ttk_bs.Label(row2_frame, text=f"المستفيد: {transaction_data['beneficiary_name'] or 'غير محدد'}", font=("Arial", 10)).pack(side=LEFT, padx=20)
            ttk_bs.Label(row2_frame, text=f"الرقم: {transaction_data['beneficiary_number'] or 'غير محدد'}", font=("Arial", 10)).pack(side=LEFT, padx=20)

            # الصف الثالث
            row3_frame = ttk_bs.Frame(info_frame)
            row3_frame.pack(fill=X, pady=5)

            ttk_bs.Label(row3_frame, text=f"الرتبة: {transaction_data['rank'] or 'غير محدد'}", font=("Arial", 10)).pack(side=LEFT, padx=20)
            ttk_bs.Label(row3_frame, text=f"القسم: {transaction_data['department'] or 'غير محدد'}", font=("Arial", 10)).pack(side=LEFT, padx=20)

            # جدول الأصناف
            items_frame = ttk_bs.LabelFrame(main_frame, text="الأصناف المصروفة", padding=15)
            items_frame.pack(fill=BOTH, expand=True)

            # أعمدة الجدول
            columns = ["#", "رقم الصنف", "اسم الصنف", "الكمية المصروفة", "وحدة الصرف", "ملاحظات"]

            tree_frame = ttk_bs.Frame(items_frame)
            tree_frame.pack(fill=BOTH, expand=True)

            items_tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=12)
            items_tree.pack(side=LEFT, fill=BOTH, expand=True)

            # تعيين العناوين
            for col in columns:
                items_tree.heading(col, text=col)
                if col == "#":
                    items_tree.column(col, width=50, anchor="center")
                elif col == "اسم الصنف":
                    items_tree.column(col, width=250, anchor="center")
                elif col == "ملاحظات":
                    items_tree.column(col, width=200, anchor="center")
                else:
                    items_tree.column(col, width=120, anchor="center")

            # جلب أصناف المعاملة
            items_query = """
                SELECT ti.*, i.number as item_number, i.name as item_name, i.unit
                FROM transaction_items ti
                LEFT JOIN items i ON ti.item_id = i.id
                WHERE ti.transaction_id = ?
                ORDER BY ti.id
            """
            items_data = db_manager.fetch_all(items_query, (transaction_data['id'],))

            # إضافة البيانات
            total_quantity = 0
            for i, item in enumerate(items_data, 1):
                items_tree.insert('', 'end', values=(
                    i,
                    item['item_number'] or '',
                    item['item_name'] or '',
                    int(item['quantity']),
                    item['unit'] or '',
                    item['notes'] or ''
                ))
                total_quantity += int(item['quantity'])

            # شريط التمرير للجدول
            items_scrollbar = ttk.Scrollbar(tree_frame, orient=VERTICAL, command=items_tree.yview)
            items_tree.configure(yscrollcommand=items_scrollbar.set)
            items_scrollbar.pack(side=RIGHT, fill=Y)

            # إجمالي الكميات
            total_frame = ttk_bs.Frame(items_frame)
            total_frame.pack(fill=X, pady=(10, 0))

            ttk_bs.Label(
                total_frame,
                text=f"إجمالي الكميات: {total_quantity}",
                font=("Arial", 12, "bold"),
                bootstyle="success"
            ).pack(side=RIGHT)

            # أزرار الإجراءات
            buttons_frame = ttk_bs.Frame(main_frame)
            buttons_frame.pack(fill=X, pady=(20, 0))

            ttk_bs.Button(
                buttons_frame,
                text="❌ إغلاق",
                command=details_window.destroy,
                bootstyle="secondary",
                width=18
            ).pack(side=RIGHT, padx=10)

            ttk_bs.Button(
                buttons_frame,
                text="🖨️ طباعة التفاصيل",
                command=lambda: self.print_transaction_details(transaction_data, items_data),
                bootstyle="primary",
                width=22
            ).pack(side=RIGHT, padx=10)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في عرض تفاصيل المعاملة: {e}")
            print(f"خطأ في عرض التفاصيل: {e}")
            import traceback
            traceback.print_exc()

    def print_transaction_details(self, transaction_data, items_data):
        """طباعة تفاصيل المعاملة"""
        try:
            from reportlab.lib.pagesizes import A4
            from reportlab.lib import colors
            from reportlab.lib.units import inch
            from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Spacer
            from reportlab.pdfbase import pdfmetrics
            from reportlab.pdfbase.ttfonts import TTFont
            import tempfile
            import os
            from datetime import datetime

            # محاولة تسجيل خط عربي
            try:
                arabic_font_path = None
                possible_fonts = [
                    "C:/Windows/Fonts/arial.ttf",
                    "C:/Windows/Fonts/tahoma.ttf",
                    "C:/Windows/Fonts/calibri.ttf"
                ]

                for font_path in possible_fonts:
                    if os.path.exists(font_path):
                        arabic_font_path = font_path
                        break

                if arabic_font_path:
                    pdfmetrics.registerFont(TTFont('Arabic', arabic_font_path))
                    font_name = 'Arabic'
                else:
                    font_name = 'Helvetica'

            except Exception:
                font_name = 'Helvetica'

            # إنشاء ملف PDF مؤقت
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
            temp_file.close()

            # إعداد الصفحة
            doc = SimpleDocTemplate(
                temp_file.name,
                pagesize=A4,
                rightMargin=0.5*inch,
                leftMargin=0.5*inch,
                topMargin=0.5*inch,
                bottomMargin=0.5*inch
            )

            story = []

            # العنوان
            title_data = [[f"تفاصيل عملية الصرف - {transaction_data['transaction_number']}"]]
            title_table = Table(title_data, colWidths=[7*inch])
            title_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, -1), font_name),
                ('FONTSIZE', (0, 0), (-1, -1), 16),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 20),
            ]))
            story.append(title_table)

            # معلومات المعاملة
            info_data = [
                [f"رقم العملية: {transaction_data['transaction_number']}", f"التاريخ: {transaction_data['transaction_date']}"],
                [f"المستفيد: {transaction_data['beneficiary_name'] or 'غير محدد'}", f"الرقم: {transaction_data['beneficiary_number'] or 'غير محدد'}"],
                [f"الرتبة: {transaction_data['rank'] or 'غير محدد'}", f"القسم: {transaction_data['department'] or 'غير محدد'}"]
            ]

            info_table = Table(info_data, colWidths=[3.5*inch, 3.5*inch])
            info_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
                ('FONTNAME', (0, 0), (-1, -1), font_name),
                ('FONTSIZE', (0, 0), (-1, -1), 12),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
            ]))
            story.append(info_table)
            story.append(Spacer(1, 20))

            # جدول الأصناف
            headers = ["#", "رقم الصنف", "اسم الصنف", "الكمية", "وحدة الصرف", "ملاحظات"]
            items_table_data = [headers]

            total_quantity = 0
            for i, item in enumerate(items_data, 1):
                items_table_data.append([
                    str(i),
                    item['item_number'] or '',
                    item['item_name'] or '',
                    str(int(item['quantity'])),
                    item['unit'] or '',
                    item['notes'] or ''
                ])
                total_quantity += int(item['quantity'])

            # إضافة صف الإجمالي
            items_table_data.append(['', '', 'الإجمالي', str(total_quantity), '', ''])

            items_table = Table(items_table_data, colWidths=[0.5*inch, 1*inch, 2.5*inch, 1*inch, 1*inch, 1*inch])
            items_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), font_name),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -2), colors.beige),
                ('BACKGROUND', (0, -1), (-1, -1), colors.lightgrey),
                ('FONTNAME', (0, 1), (-1, -1), font_name),
                ('FONTSIZE', (0, 1), (-1, -1), 9),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ]))
            story.append(items_table)

            # بناء PDF
            doc.build(story)

            # فتح PDF للطباعة
            os.startfile(temp_file.name)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في طباعة تفاصيل المعاملة: {e}")
            print(f"خطأ في طباعة التفاصيل: {e}")
            import traceback
            traceback.print_exc()

    def generate_beneficiaries_operations_report(self):
        """تقرير المستفيدين - التقرير الثالث"""
        try:
            # إنشاء نافذة التقرير
            report_window = tk.Toplevel(self.parent)
            report_window.title("👥 تقرير المستفيدين")
            report_window.geometry("1200x800")
            report_window.resizable(False, False)

            # توسيط النافذة
            report_window.update_idletasks()
            x = (report_window.winfo_screenwidth() - 1200) // 2
            y = (report_window.winfo_screenheight() - 800) // 2
            report_window.geometry(f"1200x800+{x}+{y}")

            # الإطار الرئيسي
            main_frame = ttk_bs.Frame(report_window)
            main_frame.pack(fill=BOTH, expand=True, padx=20, pady=20)

            # شريط العلوي
            header_frame = ttk_bs.Frame(main_frame)
            header_frame.pack(fill=X, pady=(0, 20))

            # زر العودة للتقارير
            back_btn = ttk_bs.Button(
                header_frame,
                text="← العودة للتقارير",
                command=report_window.destroy,
                bootstyle="outline-secondary",
                width=22
            )
            back_btn.pack(side=LEFT)

            # زر طباعة
            print_btn = ttk_bs.Button(
                header_frame,
                text="🖨️ طباعة",
                command=lambda: self.print_beneficiaries_report(data),
                bootstyle="primary",
                width=15
            )
            print_btn.pack(side=RIGHT)

            # العنوان
            title_label = ttk_bs.Label(
                header_frame,
                text="👥 تقرير المستفيدين",
                font=("Arial", 18, "bold"),
                bootstyle="primary"
            )
            title_label.pack(side=RIGHT, padx=20)

            # فلاتر التقرير
            filters_frame = ttk_bs.Frame(main_frame)
            filters_frame.pack(fill=X, pady=(0, 20))

            # فلتر التاريخ من
            from_date_frame = ttk_bs.Frame(filters_frame)
            from_date_frame.pack(side=LEFT, padx=10)

            ttk_bs.Label(from_date_frame, text="من تاريخ").pack()
            from_date_entry = ttk_bs.DateEntry(from_date_frame, width=30)
            from_date_entry.pack()

            # فلتر التاريخ إلى
            to_date_frame = ttk_bs.Frame(filters_frame)
            to_date_frame.pack(side=LEFT, padx=10)

            ttk_bs.Label(to_date_frame, text="إلى تاريخ").pack()
            to_date_entry = ttk_bs.DateEntry(to_date_frame, width=30)
            to_date_entry.pack()

            # دالة التصفية
            def apply_beneficiaries_filter():
                """تطبيق فلاتر تقرير المستفيدين"""
                try:
                    # مسح البيانات الحالية
                    for item in tree.get_children():
                        tree.delete(item)

                    # الحصول على قيم الفلاتر
                    from_date = from_date_entry.entry.get()
                    to_date = to_date_entry.entry.get()

                    # بناء الاستعلام مع الفلاتر
                    base_query = """
                        SELECT b.name, b.number, b.rank,
                               COALESCE(u.name, '') ||
                               CASE WHEN d.name IS NOT NULL THEN ' - ' || d.name ELSE '' END ||
                               CASE WHEN s.name IS NOT NULL THEN ' - ' || s.name ELSE '' END as department,
                               COUNT(t.id) as operations_count,
                               COALESCE(SUM(ti.quantity), 0) as total_quantity
                        FROM beneficiaries b
                        LEFT JOIN units u ON b.unit_id = u.id
                        LEFT JOIN departments d ON b.department_id = d.id
                        LEFT JOIN sections s ON b.section_id = s.id
                        LEFT JOIN transactions t ON b.id = t.beneficiary_id
                        LEFT JOIN transaction_items ti ON t.id = ti.transaction_id
                        WHERE b.is_active = 1
                    """

                    # إضافة فلتر التاريخ إذا تم تحديده
                    if from_date and to_date:
                        base_query += f" AND DATE(t.transaction_date) BETWEEN '{from_date}' AND '{to_date}'"
                    elif from_date:
                        base_query += f" AND DATE(t.transaction_date) >= '{from_date}'"
                    elif to_date:
                        base_query += f" AND DATE(t.transaction_date) <= '{to_date}'"

                    base_query += """
                        GROUP BY b.id, b.name, b.number, b.rank, u.name, d.name, s.name
                        ORDER BY b.name
                    """

                    # تنفيذ الاستعلام
                    filtered_data = db_manager.fetch_all(base_query)

                    if not filtered_data:
                        # عرض رسالة إذا لم توجد نتائج
                        tree.insert('', 'end', values=(
                            "",
                            "لا توجد نتائج تطابق الفلاتر المحددة",
                            "",
                            "",
                            "",
                            "",
                            "",
                            ""
                        ))
                        return

                    # إضافة البيانات المفلترة
                    for i, row in enumerate(filtered_data, 1):
                        name = row['name'] if row['name'] else 'غير محدد'
                        number = row['number'] if row['number'] else 'غير محدد'
                        rank = row['rank'] if row['rank'] else '-'
                        department = row['department'].strip() if row['department'] and row['department'].strip() else '-'
                        operations_count = int(row['operations_count']) if row['operations_count'] else 0
                        total_quantity = int(row['total_quantity']) if row['total_quantity'] else 0

                        tree.insert('', 'end', values=(
                            i,
                            name,
                            number,
                            rank,
                            department,
                            operations_count,
                            total_quantity,
                            "تفاصيل"
                        ))

                    # إضافة صف الإجمالي
                    total_operations = sum(int(row['operations_count']) if row['operations_count'] else 0 for row in filtered_data)
                    total_quantities = sum(int(row['total_quantity']) if row['total_quantity'] else 0 for row in filtered_data)

                    tree.insert('', 'end', values=(
                        "",
                        "الإجمالي",
                        "",
                        "",
                        "",
                        total_operations,
                        total_quantities,
                        ""
                    ), tags=('total',))

                    # عرض رسالة نجاح
                    date_info = ""
                    if from_date or to_date:
                        if from_date and to_date:
                            date_info = f"من {from_date} إلى {to_date}"
                        elif from_date:
                            date_info = f"من {from_date}"
                        elif to_date:
                            date_info = f"حتى {to_date}"

                    filter_info = f"✅ تم تطبيق الفلاتر - {len(filtered_data)} نتيجة"
                    if date_info:
                        filter_info += f" | التاريخ: {date_info}"

                    # عرض رسالة مؤقتة
                    temp_label = ttk_bs.Label(
                        filters_frame,
                        text=filter_info,
                        bootstyle="success",
                        font=("Arial", 9)
                    )
                    temp_label.pack(side=LEFT, padx=10)

                    # إزالة الرسالة بعد 3 ثوان
                    def remove_message():
                        try:
                            temp_label.destroy()
                        except:
                            pass

                    report_window.after(3000, remove_message)

                except Exception as e:
                    messagebox.showerror("خطأ", f"فشل في تطبيق الفلاتر: {e}")

            # زر تصفية
            filter_btn = ttk_bs.Button(
                filters_frame,
                text="🔍 تصفية",
                command=apply_beneficiaries_filter,
                bootstyle="info",
                width=18
            )
            filter_btn.pack(side=LEFT, padx=10)

            # زر إعادة تعيين
            def reset_beneficiaries_filters():
                """إعادة تعيين فلاتر المستفيدين"""
                try:
                    from_date_entry.entry.delete(0, 'end')
                    to_date_entry.entry.delete(0, 'end')

                    # مسح البيانات الحالية
                    for item in tree.get_children():
                        tree.delete(item)

                    # إعادة تحميل البيانات الأصلية
                    original_data = db_manager.fetch_all(query)

                    if not original_data:
                        tree.insert('', 'end', values=(
                            "",
                            "لا توجد بيانات مستفيدين",
                            "",
                            "",
                            "",
                            "",
                            "",
                            ""
                        ))
                    else:
                        for i, row in enumerate(original_data, 1):
                            name = row['name'] if row['name'] else 'غير محدد'
                            number = row['number'] if row['number'] else 'غير محدد'
                            rank = row['rank'] if row['rank'] else '-'
                            department = row['department'].strip() if row['department'] and row['department'].strip() else '-'
                            operations_count = int(row['operations_count']) if row['operations_count'] else 0
                            total_quantity = int(row['total_quantity']) if row['total_quantity'] else 0

                            tree.insert('', 'end', values=(
                                i,
                                name,
                                number,
                                rank,
                                department,
                                operations_count,
                                total_quantity,
                                "تفاصيل"
                            ))

                        # إضافة صف الإجمالي
                        total_operations = sum(int(row['operations_count']) if row['operations_count'] else 0 for row in original_data)
                        total_quantities = sum(int(row['total_quantity']) if row['total_quantity'] else 0 for row in original_data)

                        tree.insert('', 'end', values=(
                            "",
                            "الإجمالي",
                            "",
                            "",
                            "",
                            total_operations,
                            total_quantities,
                            ""
                        ), tags=('total',))

                except Exception as e:
                    messagebox.showerror("خطأ", f"فشل في إعادة تعيين الفلاتر: {e}")

            reset_btn = ttk_bs.Button(
                filters_frame,
                text="🔄 إعادة تعيين",
                command=reset_beneficiaries_filters,
                bootstyle="secondary",
                width=20
            )
            reset_btn.pack(side=LEFT, padx=10)

            # جدول البيانات
            columns = ["#", "الاسم", "الرقم", "الرتبة", "القسم/الإدارة", "عدد العمليات", "إجمالي الكميات", "الإجراءات"]

            tree_frame = ttk_bs.Frame(main_frame)
            tree_frame.pack(fill=BOTH, expand=True)

            tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=15)
            tree.pack(side=LEFT, fill=BOTH, expand=True)

            # تعيين العناوين
            for col in columns:
                tree.heading(col, text=col)
                if col == "#":
                    tree.column(col, width=50, anchor="center")
                elif col == "الاسم":
                    tree.column(col, width=200, anchor="center")
                elif col == "الإجراءات":
                    tree.column(col, width=100, anchor="center")
                else:
                    tree.column(col, width=120, anchor="center")

            # جلب البيانات مع ربط الجداول الصحيحة
            query = """
                SELECT b.name, b.number, b.rank,
                       COALESCE(u.name, '') ||
                       CASE WHEN d.name IS NOT NULL THEN ' - ' || d.name ELSE '' END ||
                       CASE WHEN s.name IS NOT NULL THEN ' - ' || s.name ELSE '' END as department,
                       COUNT(t.id) as operations_count,
                       COALESCE(SUM(ti.quantity), 0) as total_quantity
                FROM beneficiaries b
                LEFT JOIN units u ON b.unit_id = u.id
                LEFT JOIN departments d ON b.department_id = d.id
                LEFT JOIN sections s ON b.section_id = s.id
                LEFT JOIN transactions t ON b.id = t.beneficiary_id
                LEFT JOIN transaction_items ti ON t.id = ti.transaction_id
                WHERE b.is_active = 1
                GROUP BY b.id, b.name, b.number, b.rank, u.name, d.name, s.name
                ORDER BY b.name
            """
            data = db_manager.fetch_all(query)

            # التحقق من وجود البيانات
            if not data:
                # إضافة رسالة عدم وجود بيانات
                tree.insert('', 'end', values=(
                    "",
                    "لا توجد بيانات مستفيدين",
                    "",
                    "",
                    "",
                    "",
                    "",
                    ""
                ))
            else:
                # إضافة البيانات
                for i, row in enumerate(data, 1):
                    # تنظيف البيانات وإضافة قيم افتراضية
                    name = row['name'] if row['name'] else 'غير محدد'
                    number = row['number'] if row['number'] else 'غير محدد'
                    rank = row['rank'] if row['rank'] else '-'
                    department = row['department'].strip() if row['department'] and row['department'].strip() else '-'
                    operations_count = int(row['operations_count']) if row['operations_count'] else 0
                    total_quantity = int(row['total_quantity']) if row['total_quantity'] else 0

                    tree.insert('', 'end', values=(
                        i,
                        name,
                        number,
                        rank,
                        department,
                        operations_count,
                        total_quantity,
                        "تفاصيل"
                    ))

            # شريط التمرير
            scrollbar = ttk.Scrollbar(tree_frame, orient=VERTICAL, command=tree.yview)
            tree.configure(yscrollcommand=scrollbar.set)
            scrollbar.pack(side=RIGHT, fill=Y)

            # دالة عرض تفاصيل المستفيد
            def show_beneficiary_details(event):
                """عرض تفاصيل المستفيد المحدد"""
                try:
                    selection = tree.selection()
                    if not selection:
                        return

                    item = tree.item(selection[0])
                    values = item['values']

                    # التحقق من أن هذا ليس صف الإجمالي أو رسالة فارغة
                    if not values or len(values) < 8 or values[1] in ["الإجمالي", "لا توجد بيانات مستفيدين", "لا توجد نتائج تطابق الفلاتر المحددة"]:
                        return

                    beneficiary_name = values[1]
                    beneficiary_number = values[2]

                    # البحث عن تفاصيل المستفيد
                    details_query = """
                        SELECT t.transaction_number, t.transaction_date,
                               ti.quantity, ai.item_name, ai.unit,
                               t.notes
                        FROM transactions t
                        JOIN transaction_items ti ON t.id = ti.transaction_id
                        JOIN added_items ai ON ti.item_id = ai.id
                        JOIN beneficiaries b ON t.beneficiary_id = b.id
                        WHERE b.name = ? AND b.number = ?
                        ORDER BY t.transaction_date DESC
                    """

                    details_data = db_manager.fetch_all(details_query, (beneficiary_name, beneficiary_number))

                    # إنشاء نافذة التفاصيل
                    details_window = tk.Toplevel(report_window)
                    details_window.title(f"تفاصيل المستفيد: {beneficiary_name}")
                    details_window.geometry("800x600")
                    details_window.resizable(True, True)

                    # توسيط النافذة
                    details_window.update_idletasks()
                    x = (details_window.winfo_screenwidth() - 800) // 2
                    y = (details_window.winfo_screenheight() - 600) // 2
                    details_window.geometry(f"800x600+{x}+{y}")

                    # الإطار الرئيسي
                    details_main_frame = ttk_bs.Frame(details_window)
                    details_main_frame.pack(fill=BOTH, expand=True, padx=20, pady=20)

                    # العنوان
                    title_label = ttk_bs.Label(
                        details_main_frame,
                        text=f"📋 تفاصيل عمليات المستفيد: {beneficiary_name}",
                        font=("Arial", 16, "bold"),
                        bootstyle="primary"
                    )
                    title_label.pack(pady=(0, 20))

                    # معلومات المستفيد
                    info_frame = ttk_bs.LabelFrame(details_main_frame, text="معلومات المستفيد", bootstyle="info")
                    info_frame.pack(fill=X, pady=(0, 20))

                    info_text = f"الاسم: {beneficiary_name} | الرقم: {beneficiary_number} | الرتبة: {values[3]} | القسم: {values[4]}"
                    ttk_bs.Label(info_frame, text=info_text, font=("Arial", 10)).pack(pady=10)

                    # جدول التفاصيل
                    details_columns = ["#", "رقم المعاملة", "التاريخ", "الصنف", "الكمية", "الوحدة", "ملاحظات"]

                    details_tree_frame = ttk_bs.Frame(details_main_frame)
                    details_tree_frame.pack(fill=BOTH, expand=True)

                    details_tree = ttk.Treeview(details_tree_frame, columns=details_columns, show='headings', height=15)
                    details_tree.pack(side=LEFT, fill=BOTH, expand=True)

                    # تعيين العناوين
                    for col in details_columns:
                        details_tree.heading(col, text=col)
                        if col == "#":
                            details_tree.column(col, width=50, anchor="center")
                        elif col == "رقم المعاملة":
                            details_tree.column(col, width=120, anchor="center")
                        elif col == "التاريخ":
                            details_tree.column(col, width=100, anchor="center")
                        elif col == "الصنف":
                            details_tree.column(col, width=200, anchor="center")
                        else:
                            details_tree.column(col, width=100, anchor="center")

                    # إضافة البيانات
                    if details_data:
                        for i, row in enumerate(details_data, 1):
                            details_tree.insert('', 'end', values=(
                                i,
                                row['transaction_number'],
                                row['transaction_date'],
                                row['item_name'],
                                int(row['quantity']),
                                row['unit'],
                                row['notes'] or '-'
                            ))
                    else:
                        details_tree.insert('', 'end', values=(
                            "",
                            "لا توجد عمليات لهذا المستفيد",
                            "",
                            "",
                            "",
                            "",
                            ""
                        ))

                    # شريط التمرير للتفاصيل
                    details_scrollbar = ttk.Scrollbar(details_tree_frame, orient=VERTICAL, command=details_tree.yview)
                    details_tree.configure(yscrollcommand=details_scrollbar.set)
                    details_scrollbar.pack(side=RIGHT, fill=Y)

                    # زر إغلاق
                    close_btn = ttk_bs.Button(
                        details_main_frame,
                        text="إغلاق",
                        command=details_window.destroy,
                        bootstyle="secondary",
                        width=15
                    )
                    close_btn.pack(pady=(20, 0))

                except Exception as e:
                    messagebox.showerror("خطأ", f"فشل في عرض تفاصيل المستفيد: {e}")

            # ربط النقر المزدوج بعرض التفاصيل
            tree.bind("<Double-1>", show_beneficiary_details)

            # إضافة صف الإجمالي
            total_operations = sum(int(row['operations_count']) for row in data)
            total_quantities = sum(int(row['total_quantity']) for row in data)

            tree.insert('', 'end', values=(
                "",
                "الإجمالي",
                "",
                "",
                "",
                total_operations,
                total_quantities,
                ""
            ), tags=('total',))

            # تنسيق صف الإجمالي
            tree.tag_configure('total', background='#e3f2fd', font=('Arial', 10, 'bold'))

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء تقرير المستفيدين: {e}")

    def print_beneficiaries_report(self, data):
        """طباعة تقرير المستفيدين"""
        try:
            import tempfile
            import webbrowser
            from datetime import datetime

            # حساب الإجماليات
            total_operations = sum(int(row['operations_count']) if row['operations_count'] else 0 for row in data)
            total_quantities = sum(int(row['total_quantity']) if row['total_quantity'] else 0 for row in data)

            current_date = datetime.now().strftime("%Y-%m-%d")
            current_time = datetime.now().strftime("%H:%M")

            html = f"""<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head><meta charset="UTF-8"><title>تقرير المستفيدين</title>
<style>
body {{ font-family: Arial, sans-serif; direction: rtl; margin: 20px; }}
.header {{ text-align: center; margin-bottom: 30px; }}
.header h1 {{ color: #2c3e50; font-size: 24px; margin-bottom: 10px; }}
table {{ width: 100%; border-collapse: collapse; margin-top: 20px; }}
th, td {{ border: 1px solid #ddd; padding: 8px; text-align: center; }}
th {{ background-color: #495057; color: white; }}
tr:nth-child(even) {{ background-color: #f2f2f2; }}
.total-row {{ background-color: #e3f2fd; font-weight: bold; }}
@media print {{ @page {{ size: A4 landscape; margin: 0.5in; }} }}
</style>
<script>window.onload = function() {{ setTimeout(function() {{ window.print(); }}, 500); }};</script>
</head>
<body>
<div class="header">
<h1>👥 تقرير المستفيدين</h1>
<p>تاريخ التقرير: {current_date} | وقت الطباعة: {current_time}</p>
</div>
<table>
<thead><tr><th>#</th><th>الاسم</th><th>الرقم</th><th>الرتبة</th><th>القسم/الإدارة</th><th>عدد العمليات</th><th>إجمالي الكميات</th></tr></thead>
<tbody>"""

            # إضافة صفوف البيانات
            for i, row in enumerate(data, 1):
                name = row['name'] if row['name'] else 'غير محدد'
                number = row['number'] if row['number'] else 'غير محدد'
                rank = row['rank'] if row['rank'] else '-'
                department = row['department'].strip() if row['department'] and row['department'].strip() else '-'
                operations_count = int(row['operations_count']) if row['operations_count'] else 0
                total_quantity = int(row['total_quantity']) if row['total_quantity'] else 0

                html += f"<tr><td>{i}</td><td>{name}</td><td>{number}</td><td>{rank}</td><td>{department}</td><td>{operations_count:,}</td><td>{total_quantity:,}</td></tr>"

            # إضافة صف الإجمالي
            html += f"""<tr class="total-row"><td></td><td>الإجمالي</td><td></td><td></td><td></td><td>{total_operations:,}</td><td>{total_quantities:,}</td></tr>
</tbody></table></body></html>"""

            # إنشاء ملف HTML مؤقت
            with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as f:
                f.write(html)
                temp_file = f.name

            # فتح الملف في المتصفح للطباعة التلقائية
            webbrowser.open(f'file://{temp_file}')

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في طباعة تقرير المستفيدين: {e}")

    def generate_inventory_movement_report(self):
        """تقرير حركة المخزون - التقرير الرابع"""
        try:
            # إنشاء نافذة التقرير
            report_window = tk.Toplevel(self.parent)
            report_window.title("🔄 تقرير حركة المخزون")
            report_window.geometry("1200x800")
            report_window.resizable(False, False)

            # توسيط النافذة
            report_window.update_idletasks()
            x = (report_window.winfo_screenwidth() - 1200) // 2
            y = (report_window.winfo_screenheight() - 800) // 2
            report_window.geometry(f"1200x800+{x}+{y}")

            # الإطار الرئيسي
            main_frame = ttk_bs.Frame(report_window)
            main_frame.pack(fill=BOTH, expand=True, padx=20, pady=20)

            # شريط العلوي
            header_frame = ttk_bs.Frame(main_frame)
            header_frame.pack(fill=X, pady=(0, 20))

            # زر العودة للتقارير
            back_btn = ttk_bs.Button(
                header_frame,
                text="← العودة للتقارير",
                command=report_window.destroy,
                bootstyle="outline-secondary",
                width=22
            )
            back_btn.pack(side=LEFT)

            # زر طباعة (سيتم تعيين الأمر لاحقاً)
            print_btn = ttk_bs.Button(
                header_frame,
                text="🖨️ طباعة",
                bootstyle="primary",
                width=15
            )
            print_btn.pack(side=RIGHT)

            # العنوان
            title_label = ttk_bs.Label(
                header_frame,
                text="🔄 تقرير حركة المخزون",
                font=("Arial", 18, "bold"),
                bootstyle="primary"
            )
            title_label.pack(side=RIGHT, padx=20)

            # فلاتر التقرير
            filters_frame = ttk_bs.Frame(main_frame)
            filters_frame.pack(fill=X, pady=(0, 20))

            # فلتر نوع الحركة
            movement_frame = ttk_bs.Frame(filters_frame)
            movement_frame.pack(side=LEFT, padx=10)

            ttk_bs.Label(movement_frame, text="نوع الحركة").pack()
            movement_combo = ttk_bs.Combobox(
                movement_frame,
                values=["-- جميع الحركات --", "إضافة", "صرف"],
                state="readonly",
                width=50
            )
            movement_combo.set("-- جميع الحركات --")
            movement_combo.pack()

            # فلتر التاريخ من
            from_date_frame = ttk_bs.Frame(filters_frame)
            from_date_frame.pack(side=LEFT, padx=10)

            ttk_bs.Label(from_date_frame, text="من تاريخ").pack()
            from_date_entry = ttk_bs.DateEntry(from_date_frame, width=30)
            from_date_entry.pack()

            # فلتر التاريخ إلى
            to_date_frame = ttk_bs.Frame(filters_frame)
            to_date_frame.pack(side=LEFT, padx=10)

            ttk_bs.Label(to_date_frame, text="إلى تاريخ").pack()
            to_date_entry = ttk_bs.DateEntry(to_date_frame, width=30)
            to_date_entry.pack()

            # دالة التصفية
            def apply_inventory_movement_filter():
                """تطبيق فلاتر تقرير حركة المخزون"""
                try:
                    # مسح البيانات الحالية
                    for item in tree.get_children():
                        tree.delete(item)

                    # الحصول على قيم الفلاتر
                    movement_type = movement_combo.get()
                    from_date = from_date_entry.entry.get()
                    to_date = to_date_entry.entry.get()

                    # بناء الاستعلام مع الفلاتر
                    if movement_type == "-- جميع الحركات --":
                        # عرض جميع الأصناف مع فلتر التاريخ فقط
                        base_query = """
                            SELECT ai.item_name, ai.item_number, ai.unit,
                                   ai.current_quantity as total_items,
                                   COALESCE(dispensed.total_dispensed, 0) as total_dispensed
                            FROM added_items ai
                            LEFT JOIN (
                                SELECT ai2.id as item_id, SUM(ti.quantity) as total_dispensed
                                FROM transaction_items ti
                                JOIN added_items ai2 ON ti.item_id = ai2.id
                                JOIN transactions t ON ti.transaction_id = t.id
                                WHERE 1=1
                        """

                        # إضافة فلتر التاريخ إذا تم تحديده
                        if from_date and to_date:
                            base_query += f" AND DATE(t.transaction_date) BETWEEN '{from_date}' AND '{to_date}'"
                        elif from_date:
                            base_query += f" AND DATE(t.transaction_date) >= '{from_date}'"
                        elif to_date:
                            base_query += f" AND DATE(t.transaction_date) <= '{to_date}'"

                        base_query += """
                                GROUP BY ai2.id
                            ) dispensed ON ai.id = dispensed.item_id
                            WHERE ai.is_active = 1
                            ORDER BY ai.item_name
                        """

                    elif movement_type == "إضافة":
                        # عرض الأصناف التي تم إضافتها (من جدول حركات المخزون)
                        base_query = """
                            SELECT ai.item_name, ai.item_number, ai.unit,
                                   COALESCE(additions.total_added, 0) as total_items,
                                   0 as total_dispensed
                            FROM added_items ai
                            INNER JOIN (
                                SELECT im.item_number, SUM(im.quantity) as total_added
                                FROM inventory_movements_new im
                                WHERE im.movement_type = 'إضافة' AND im.is_active = 1
                        """

                        # إضافة فلتر التاريخ
                        if from_date and to_date:
                            base_query += f" AND DATE(im.movement_date) BETWEEN '{from_date}' AND '{to_date}'"
                        elif from_date:
                            base_query += f" AND DATE(im.movement_date) >= '{from_date}'"
                        elif to_date:
                            base_query += f" AND DATE(im.movement_date) <= '{to_date}'"

                        base_query += """
                                GROUP BY im.item_number
                            ) additions ON ai.item_number = additions.item_number
                            WHERE ai.is_active = 1
                            ORDER BY ai.item_name
                        """

                    elif movement_type == "صرف":
                        # عرض الأصناف التي تم صرفها
                        base_query = """
                            SELECT ai.item_name, ai.item_number, ai.unit,
                                   0 as total_items,
                                   COALESCE(dispensed.total_dispensed, 0) as total_dispensed
                            FROM added_items ai
                            INNER JOIN (
                                SELECT ai2.id as item_id, SUM(ti.quantity) as total_dispensed
                                FROM transaction_items ti
                                JOIN added_items ai2 ON ti.item_id = ai2.id
                                JOIN transactions t ON ti.transaction_id = t.id
                                WHERE 1=1
                        """

                        # إضافة فلتر التاريخ
                        if from_date and to_date:
                            base_query += f" AND DATE(t.transaction_date) BETWEEN '{from_date}' AND '{to_date}'"
                        elif from_date:
                            base_query += f" AND DATE(t.transaction_date) >= '{from_date}'"
                        elif to_date:
                            base_query += f" AND DATE(t.transaction_date) <= '{to_date}'"

                        base_query += """
                                GROUP BY ai2.id
                            ) dispensed ON ai.id = dispensed.item_id
                            WHERE ai.is_active = 1
                            ORDER BY ai.item_name
                        """

                    # تنفيذ الاستعلام
                    filtered_data = db_manager.fetch_all(base_query)

                    if not filtered_data:
                        # عرض رسالة إذا لم توجد نتائج
                        tree.insert('', 'end', values=(
                            "",
                            "",
                            "لا توجد نتائج تطابق الفلاتر المحددة",
                            "",
                            "",
                            "",
                            ""
                        ))
                        return

                    # إضافة البيانات المفلترة
                    for i, row in enumerate(filtered_data, 1):
                        total_items = int(row['total_items']) if row['total_items'] else 0
                        total_dispensed = int(row['total_dispensed']) if row['total_dispensed'] else 0
                        net_changes = total_items - total_dispensed

                        tree.insert('', 'end', values=(
                            i,
                            row['item_number'],
                            row['item_name'],
                            row['unit'],
                            total_items,
                            total_dispensed,
                            net_changes
                        ))

                    # إضافة صف الإجمالي
                    total_items_sum = sum(int(row['total_items']) if row['total_items'] else 0 for row in filtered_data)
                    total_dispensed_sum = sum(int(row['total_dispensed']) if row['total_dispensed'] else 0 for row in filtered_data)
                    total_net_changes = total_items_sum - total_dispensed_sum

                    tree.insert('', 'end', values=(
                        "",
                        "",
                        "الإجمالي",
                        "",
                        total_items_sum,
                        total_dispensed_sum,
                        total_net_changes
                    ), tags=('total',))

                    # تحديث أمر الطباعة مع البيانات المفلترة
                    print_btn.configure(command=lambda: self.print_inventory_movement_report(filtered_data))

                    # عرض رسالة نجاح
                    filter_info = f"نوع الحركة: {movement_type}"
                    if from_date or to_date:
                        date_range = ""
                        if from_date and to_date:
                            date_range = f"من {from_date} إلى {to_date}"
                        elif from_date:
                            date_range = f"من {from_date}"
                        elif to_date:
                            date_range = f"حتى {to_date}"
                        filter_info += f" | التاريخ: {date_range}"

                    # عرض رسالة مؤقتة (تختفي بعد 3 ثوان)
                    temp_label = ttk_bs.Label(
                        filters_frame,
                        text=f"✅ تم تطبيق الفلاتر - {len(filtered_data)} نتيجة | {filter_info}",
                        bootstyle="success",
                        font=("Arial", 9)
                    )
                    temp_label.pack(side=LEFT, padx=10)

                    # إزالة الرسالة بعد 3 ثوان
                    def remove_message():
                        try:
                            temp_label.destroy()
                        except:
                            pass

                    report_window.after(3000, remove_message)

                except Exception as e:
                    messagebox.showerror("خطأ", f"فشل في تطبيق الفلاتر: {e}")

            # دالة إعادة تعيين الفلاتر
            def reset_filters():
                """إعادة تعيين الفلاتر والعودة للعرض الأصلي"""
                try:
                    # إعادة تعيين قيم الفلاتر
                    movement_combo.set("-- جميع الحركات --")
                    from_date_entry.entry.delete(0, 'end')
                    to_date_entry.entry.delete(0, 'end')

                    # مسح البيانات الحالية
                    for item in tree.get_children():
                        tree.delete(item)

                    # إعادة تحميل البيانات الأصلية
                    original_query = """
                        SELECT ai.item_name, ai.item_number, ai.unit,
                               ai.current_quantity as total_items,
                               COALESCE(dispensed.total_dispensed, 0) as total_dispensed
                        FROM added_items ai
                        LEFT JOIN (
                            SELECT ai2.id as item_id, SUM(ti.quantity) as total_dispensed
                            FROM transaction_items ti
                            JOIN added_items ai2 ON ti.item_id = ai2.id
                            GROUP BY ai2.id
                        ) dispensed ON ai.id = dispensed.item_id
                        WHERE ai.is_active = 1
                        ORDER BY ai.item_name
                    """
                    original_data = db_manager.fetch_all(original_query)

                    # إضافة البيانات الأصلية
                    for i, row in enumerate(original_data, 1):
                        total_items = int(row['total_items']) if row['total_items'] else 0
                        total_dispensed = int(row['total_dispensed']) if row['total_dispensed'] else 0
                        net_changes = total_items - total_dispensed

                        tree.insert('', 'end', values=(
                            i,
                            row['item_number'],
                            row['item_name'],
                            row['unit'],
                            total_items,
                            total_dispensed,
                            net_changes
                        ))

                    # إضافة صف الإجمالي
                    total_items_sum = sum(int(row['total_items']) if row['total_items'] else 0 for row in original_data)
                    total_dispensed_sum = sum(int(row['total_dispensed']) if row['total_dispensed'] else 0 for row in original_data)
                    total_net_changes = total_items_sum - total_dispensed_sum

                    tree.insert('', 'end', values=(
                        "",
                        "",
                        "الإجمالي",
                        "",
                        total_items_sum,
                        total_dispensed_sum,
                        total_net_changes
                    ), tags=('total',))

                    # تحديث أمر الطباعة
                    print_btn.configure(command=lambda: self.print_inventory_movement_report(original_data))

                except Exception as e:
                    messagebox.showerror("خطأ", f"فشل في إعادة تعيين الفلاتر: {e}")

            # زر تصفية
            filter_btn = ttk_bs.Button(
                filters_frame,
                text="🔍 تصفية",
                command=apply_inventory_movement_filter,
                bootstyle="info",
                width=18
            )
            filter_btn.pack(side=LEFT, padx=10)

            # زر إعادة تعيين
            reset_btn = ttk_bs.Button(
                filters_frame,
                text="🔄 إعادة تعيين",
                command=reset_filters,
                bootstyle="secondary",
                width=20
            )
            reset_btn.pack(side=LEFT, padx=10)

            # بطاقات الإحصائيات
            stats_frame = ttk_bs.Frame(main_frame)
            stats_frame.pack(fill=X, pady=(0, 20))

            # إجمالي الأصناف (تم تغيير من إجمالي الإضافات)
            items_card = ttk_bs.Frame(stats_frame, bootstyle="success")
            items_card.pack(side=RIGHT, padx=5, fill=X, expand=True)

            ttk_bs.Label(
                items_card,
                text="إجمالي الأصناف",
                font=("Arial", 12, "bold"),
                bootstyle="inverse-success"
            ).pack(pady=5)

            # سحب عدد الأصناف من شاشة الأصناف (added_items)
            items_count = db_manager.fetch_one(
                "SELECT COUNT(*) as count FROM added_items WHERE is_active = 1"
            )
            ttk_bs.Label(
                items_card,
                text=str(items_count['count'] if items_count else 0),
                font=("Arial", 24, "bold"),
                bootstyle="inverse-success"
            ).pack(pady=5)

            # إجمالي الصرف
            dispensing_card = ttk_bs.Frame(stats_frame, bootstyle="danger")
            dispensing_card.pack(side=RIGHT, padx=5, fill=X, expand=True)

            ttk_bs.Label(
                dispensing_card,
                text="إجمالي الصرف",
                font=("Arial", 12, "bold"),
                bootstyle="inverse-danger"
            ).pack(pady=5)

            dispensing_count = db_manager.fetch_one(
                "SELECT COUNT(*) as count FROM transaction_items"
            )
            ttk_bs.Label(
                dispensing_card,
                text=str(dispensing_count['count'] if dispensing_count else 0),
                font=("Arial", 24, "bold"),
                bootstyle="inverse-danger"
            ).pack(pady=5)

            # صافي التغيرات (إجمالي الأصناف - إجمالي الصرف)
            net_change_card = ttk_bs.Frame(stats_frame, bootstyle="primary")
            net_change_card.pack(side=RIGHT, padx=5, fill=X, expand=True)

            ttk_bs.Label(
                net_change_card,
                text="صافي التغيرات",
                font=("Arial", 12, "bold"),
                bootstyle="inverse-primary"
            ).pack(pady=5)

            # حساب صافي التغيرات
            total_items = items_count['count'] if items_count else 0
            total_dispensed = dispensing_count['count'] if dispensing_count else 0
            net_changes = total_items - total_dispensed

            ttk_bs.Label(
                net_change_card,
                text=str(net_changes),
                font=("Arial", 24, "bold"),
                bootstyle="inverse-primary"
            ).pack(pady=5)

            # جدول البيانات - ترتيب الخانات الجديد
            columns = ["#", "رقم الصنف", "الصنف", "وحدة الصرف", "إجمالي الأصناف", "إجمالي الصرف", "صافي التغيرات"]

            tree_frame = ttk_bs.Frame(main_frame)
            tree_frame.pack(fill=BOTH, expand=True)

            tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=12)
            tree.pack(side=LEFT, fill=BOTH, expand=True)

            # تعيين العناوين وعرض الأعمدة
            for col in columns:
                tree.heading(col, text=col)
                if col == "#":
                    tree.column(col, width=50, anchor="center")
                elif col == "رقم الصنف":
                    tree.column(col, width=100, anchor="center")
                elif col == "الصنف":
                    tree.column(col, width=200, anchor="center")
                elif col == "وحدة الصرف":
                    tree.column(col, width=100, anchor="center")
                else:
                    tree.column(col, width=120, anchor="center")

            # جلب البيانات مع التعديل الجديد
            query = """
                SELECT ai.item_name, ai.item_number, ai.unit,
                       ai.current_quantity as total_items,
                       COALESCE(dispensed.total_dispensed, 0) as total_dispensed
                FROM added_items ai
                LEFT JOIN (
                    SELECT ai2.id as item_id, SUM(ti.quantity) as total_dispensed
                    FROM transaction_items ti
                    JOIN added_items ai2 ON ti.item_id = ai2.id
                    GROUP BY ai2.id
                ) dispensed ON ai.id = dispensed.item_id
                WHERE ai.is_active = 1
                ORDER BY ai.item_name
            """
            data = db_manager.fetch_all(query)

            # تعيين أمر الطباعة بعد جلب البيانات
            print_btn.configure(command=lambda: self.print_inventory_movement_report(data))

            # إضافة البيانات
            for i, row in enumerate(data, 1):
                total_items = int(row['total_items']) if row['total_items'] else 0
                total_dispensed = int(row['total_dispensed']) if row['total_dispensed'] else 0
                net_changes = total_items - total_dispensed

                tree.insert('', 'end', values=(
                    i,
                    row['item_number'],
                    row['item_name'],
                    row['unit'],
                    total_items,
                    total_dispensed,
                    net_changes
                ))

            # شريط التمرير
            scrollbar = ttk.Scrollbar(tree_frame, orient=VERTICAL, command=tree.yview)
            tree.configure(yscrollcommand=scrollbar.set)
            scrollbar.pack(side=RIGHT, fill=Y)

            # إضافة صف الإجمالي
            total_items_sum = sum(int(row['total_items']) if row['total_items'] else 0 for row in data)
            total_dispensed_sum = sum(int(row['total_dispensed']) if row['total_dispensed'] else 0 for row in data)
            total_net_changes = total_items_sum - total_dispensed_sum

            tree.insert('', 'end', values=(
                "",
                "",
                "الإجمالي",
                "",
                total_items_sum,
                total_dispensed_sum,
                total_net_changes
            ), tags=('total',))

            # تنسيق صف الإجمالي
            tree.tag_configure('total', background='#e3f2fd', font=('Arial', 10, 'bold'))

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء تقرير حركة المخزون: {e}")

    def print_inventory_movement_report(self, data):
        """طباعة تقرير حركة المخزون بتنسيق PDF أفقي"""
        try:
            import tempfile
            import webbrowser
            import os
            from datetime import datetime

            # إنشاء ملف HTML مؤقت
            with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as f:
                html_content = self.generate_inventory_movement_html(data)
                f.write(html_content)
                temp_file = f.name

            # فتح الملف في المتصفح للطباعة التلقائية
            webbrowser.open(f'file://{temp_file}')

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في طباعة التقرير: {e}")

    def generate_inventory_movement_html(self, data):
        """إنشاء محتوى HTML لتقرير حركة المخزون"""
        from datetime import datetime

        # حساب الإجماليات
        total_items_sum = sum(int(row['total_items']) if row['total_items'] else 0 for row in data)
        total_dispensed_sum = sum(int(row['total_dispensed']) if row['total_dispensed'] else 0 for row in data)
        total_net_changes = total_items_sum - total_dispensed_sum

        current_date = datetime.now().strftime("%Y-%m-%d")
        current_time = datetime.now().strftime("%H:%M")

        html = f"""
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير حركة المخزون</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;700&display=swap');

        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}

        body {{
            font-family: 'Noto Sans Arabic', 'Arial', 'Tahoma', 'Segoe UI', sans-serif;
            direction: rtl;
            background-color: #ffffff;
            color: #333;
            font-size: 12px;
            line-height: 1.4;
        }}

        .container {{
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 15px;
        }}

        .header {{
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 3px solid #2c3e50;
            padding-bottom: 15px;
        }}

        .header h1 {{
            color: #2c3e50;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
        }}

        .header-info {{
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 10px;
            font-size: 11px;
            color: #666;
        }}

        .stats-cards {{
            display: flex;
            justify-content: space-around;
            margin-bottom: 20px;
            gap: 15px;
        }}

        .stat-card {{
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 12px;
            text-align: center;
            flex: 1;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }}

        .stat-card.success {{
            border-color: #28a745;
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        }}

        .stat-card.danger {{
            border-color: #dc3545;
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
        }}

        .stat-card.primary {{
            border-color: #007bff;
            background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
        }}

        .stat-title {{
            font-size: 11px;
            font-weight: bold;
            margin-bottom: 5px;
            color: #495057;
        }}

        .stat-value {{
            font-size: 18px;
            font-weight: bold;
            color: #212529;
        }}

        .table-container {{
            overflow-x: auto;
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }}

        table {{
            width: 100%;
            border-collapse: collapse;
            background-color: white;
            font-size: 11px;
        }}

        th {{
            background: linear-gradient(135deg, #495057 0%, #343a40 100%);
            color: white;
            padding: 12px 8px;
            text-align: center;
            font-weight: bold;
            border: 1px solid #dee2e6;
            font-size: 12px;
        }}

        td {{
            padding: 10px 8px;
            text-align: center;
            border: 1px solid #dee2e6;
            vertical-align: middle;
        }}

        tr:nth-child(even) {{
            background-color: #f8f9fa;
        }}

        tr:nth-child(odd) {{
            background-color: #ffffff;
        }}

        tr:hover {{
            background-color: #e3f2fd;
        }}

        .total-row {{
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%) !important;
            font-weight: bold;
            border-top: 2px solid #2196f3;
        }}

        .total-row td {{
            font-weight: bold;
            color: #1976d2;
        }}

        .positive {{
            color: #28a745;
            font-weight: bold;
        }}

        .negative {{
            color: #dc3545;
            font-weight: bold;
        }}

        .zero {{
            color: #6c757d;
        }}

        .footer {{
            margin-top: 20px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #dee2e6;
            padding-top: 10px;
        }}

        @media print {{
            @page {{
                size: A4 landscape;
                margin: 0.5in;
            }}

            body {{
                font-size: 10px;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }}

            .container {{
                padding: 0;
                max-width: none;
            }}

            .header h1 {{
                font-size: 20px;
            }}

            .stat-card {{
                padding: 8px;
            }}

            .stat-value {{
                font-size: 14px;
            }}

            th {{
                font-size: 10px;
                padding: 8px 6px;
            }}

            td {{
                font-size: 9px;
                padding: 6px 4px;
            }}

            tr {{
                page-break-inside: avoid;
            }}
        }}
    </style>
    <script>
        window.onload = function() {{
            setTimeout(function() {{
                window.print();
            }}, 500);
        }};
    </script>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔄 تقرير حركة المخزون</h1>
            <div class="header-info">
                <div>تاريخ التقرير: {current_date}</div>
                <div>وقت الطباعة: {current_time}</div>
                <div>نظام إدارة المخازن</div>
            </div>
        </div>

        <div class="stats-cards">
            <div class="stat-card success">
                <div class="stat-title">إجمالي الأصناف</div>
                <div class="stat-value">{total_items_sum:,}</div>
            </div>
            <div class="stat-card danger">
                <div class="stat-title">إجمالي الصرف</div>
                <div class="stat-value">{total_dispensed_sum:,}</div>
            </div>
            <div class="stat-card primary">
                <div class="stat-title">صافي التغيرات</div>
                <div class="stat-value">{total_net_changes:,}</div>
            </div>
        </div>

        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th style="width: 5%;">#</th>
                        <th style="width: 12%;">رقم الصنف</th>
                        <th style="width: 25%;">الصنف</th>
                        <th style="width: 12%;">وحدة الصرف</th>
                        <th style="width: 15%;">إجمالي الأصناف</th>
                        <th style="width: 15%;">إجمالي الصرف</th>
                        <th style="width: 16%;">صافي التغيرات</th>
                    </tr>
                </thead>
                <tbody>"""

        # إضافة صفوف البيانات
        for i, row in enumerate(data, 1):
            total_items = int(row['total_items']) if row['total_items'] else 0
            total_dispensed = int(row['total_dispensed']) if row['total_dispensed'] else 0
            net_changes = total_items - total_dispensed

            # تحديد لون صافي التغيرات
            net_class = "positive" if net_changes > 0 else "negative" if net_changes < 0 else "zero"

            html += f"""
                    <tr>
                        <td>{i}</td>
                        <td>{row['item_number']}</td>
                        <td style="text-align: right; padding-right: 10px;">{row['item_name']}</td>
                        <td>{row['unit']}</td>
                        <td>{total_items:,}</td>
                        <td>{total_dispensed:,}</td>
                        <td class="{net_class}">{net_changes:,}</td>
                    </tr>"""

        # إضافة صف الإجمالي
        total_net_class = "positive" if total_net_changes > 0 else "negative" if total_net_changes < 0 else "zero"

        html += f"""
                    <tr class="total-row">
                        <td></td>
                        <td></td>
                        <td style="font-weight: bold;">الإجمالي</td>
                        <td></td>
                        <td>{total_items_sum:,}</td>
                        <td>{total_dispensed_sum:,}</td>
                        <td class="{total_net_class}">{total_net_changes:,}</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="footer">
            <p>تم إنشاء هذا التقرير بواسطة نظام إدارة المخازن - جميع الحقوق محفوظة</p>
        </div>
    </div>
</body>
</html>"""

        return html




    def setup_shortcuts(self):
        """إعداد مفاتيح الاختصار"""
        try:
            # إنشاء معالج السياق
            self.context_handler = ContextHandler()
            
            # تعيين دوال العمليات
            self.context_handler.set_save_callback(self.shortcut_save)
            self.context_handler.set_delete_callback(self.shortcut_delete)
            self.context_handler.set_copy_callback(self.shortcut_copy)
            self.context_handler.set_paste_callback(self.shortcut_paste)
            
            # تفعيل مفاتيح الاختصار
            target_window = getattr(self, 'reports_window', None)
            if target_window:
                self.global_shortcuts = GlobalShortcuts(target_window, self.context_handler)
        except Exception as e:
            print(f"خطأ في إعداد مفاتيح الاختصار: {e}")
    
    def shortcut_save(self):
        """عملية الحفظ (F1)"""
        try:
            # البحث عن دوال الحفظ المتاحة
            save_methods = ['save_data', 'save_changes', 'save_item', 'save', 'add_item']
            for method_name in save_methods:
                if hasattr(self, method_name):
                    getattr(self, method_name)()
                    print(f"تم تنفيذ {method_name} بمفتاح F1")
                    return
            print("عملية الحفظ غير متاحة")
        except Exception as e:
            print(f"خطأ في عملية الحفظ: {e}")
    
    def shortcut_delete(self):
        """عملية الحذف (F2)"""
        try:
            # البحث عن دوال الحذف المتاحة
            delete_methods = ['delete_selected', 'delete_item', 'delete_data', 'delete']
            for method_name in delete_methods:
                if hasattr(self, method_name):
                    getattr(self, method_name)()
                    print(f"تم تنفيذ {method_name} بمفتاح F2")
                    return
            print("عملية الحذف غير متاحة")
        except Exception as e:
            print(f"خطأ في عملية الحذف: {e}")
    
    def shortcut_copy(self):
        """عملية النسخ (F3)"""
        try:
            # البحث عن دوال النسخ المتاحة
            copy_methods = ['copy_data', 'copy_selected', 'copy_item']
            for method_name in copy_methods:
                if hasattr(self, method_name):
                    getattr(self, method_name)()
                    print(f"تم تنفيذ {method_name} بمفتاح F3")
                    return
            
            # نسخ عامة
            import pyperclip
            pyperclip.copy("تم النسخ من النافذة")
            print("تم نسخ البيانات العامة")
        except Exception as e:
            print(f"خطأ في عملية النسخ: {e}")
    
    def shortcut_paste(self):
        """عملية اللصق (F4)"""
        try:
            # البحث عن دوال اللصق المتاحة
            paste_methods = ['paste_data', 'paste_item']
            for method_name in paste_methods:
                if hasattr(self, method_name):
                    getattr(self, method_name)()
                    print(f"تم تنفيذ {method_name} بمفتاح F4")
                    return
            
            # لصق عام
            import pyperclip
            clipboard_text = pyperclip.paste()
            if clipboard_text:
                print(f"تم لصق: {clipboard_text[:50]}")
            else:
                print("لا توجد بيانات في الحافظة")
        except Exception as e:
            print(f"خطأ في عملية اللصق: {e}")

# اختبار النافذة
if __name__ == "__main__":
    root = tk.Tk()
    root.withdraw()  # إخفاء النافذة الرئيسية
    
    # إنشاء نافذة الاختبار
    window = ReportsWindow(root, None)
    root.mainloop()
