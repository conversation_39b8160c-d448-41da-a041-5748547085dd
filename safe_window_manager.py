#!/usr/bin/env python3
"""
مدير النوافذ الآمن - نظام إدارة المخازن والمستودعات
Safe Window Manager - Desktop Stores Management System

يمنع أخطاء:
- invalid command name
- toplevel errors
- bad window path name
"""

import tkinter as tk
import ttkbootstrap as ttk_bs
from typing import Optional, Callable, Any
import logging

class SafeWindowManager:
    """مدير النوافذ الآمن"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.active_windows = {}
        self.window_counter = 0
    
    def create_safe_toplevel(self, parent: tk.Widget, title: str = "", **kwargs) -> Optional[tk.Toplevel]:
        """إنشاء نافذة Toplevel بطريقة آمنة"""
        try:
            # التحقق من صحة النافذة الأب
            if not self._is_widget_valid(parent):
                self.logger.warning("النافذة الأب غير صالحة")
                return None
            
            # إنشاء النافذة
            window = ttk_bs.Toplevel(parent)
            
            # تعيين العنوان
            if title:
                window.title(title)
            
            # تطبيق الخصائص الإضافية
            for key, value in kwargs.items():
                try:
                    if hasattr(window, key):
                        setattr(window, key, value)
                    elif key == 'geometry':
                        window.geometry(value)
                    elif key == 'minsize':
                        window.minsize(*value)
                    elif key == 'maxsize':
                        window.maxsize(*value)
                except Exception as e:
                    self.logger.warning(f"فشل في تعيين خاصية {key}: {e}")
            
            # تسجيل النافذة
            window_id = f"window_{self.window_counter}"
            self.active_windows[window_id] = window
            self.window_counter += 1
            
            # إعداد معالج الإغلاق
            window.protocol("WM_DELETE_WINDOW", lambda: self._safe_close_window(window_id))
            
            self.logger.info(f"تم إنشاء نافذة آمنة: {title}")
            return window
            
        except Exception as e:
            self.logger.error(f"فشل في إنشاء نافذة آمنة: {e}")
            return None
    
    def create_safe_window(self, title: str = "", **kwargs) -> Optional[ttk_bs.Window]:
        """إنشاء نافذة Window بطريقة آمنة"""
        try:
            # إعداد المعاملات الافتراضية
            default_kwargs = {
                'title': title,
                'themename': 'cosmo',
                'size': (800, 600),
                'resizable': (True, True)
            }
            
            # دمج المعاملات
            default_kwargs.update(kwargs)
            
            # إنشاء النافذة
            window = ttk_bs.Window(**default_kwargs)
            
            # تسجيل النافذة
            window_id = f"window_{self.window_counter}"
            self.active_windows[window_id] = window
            self.window_counter += 1
            
            # إعداد معالج الإغلاق
            window.protocol("WM_DELETE_WINDOW", lambda: self._safe_close_window(window_id))
            
            self.logger.info(f"تم إنشاء نافذة مستقلة آمنة: {title}")
            return window
            
        except Exception as e:
            self.logger.error(f"فشل في إنشاء نافذة مستقلة آمنة: {e}")
            return None
    
    def create_safe_treeview(self, parent: tk.Widget, **kwargs) -> Optional[tk.ttk.Treeview]:
        """إنشاء Treeview بطريقة آمنة مع حماية من invalid command name"""
        try:
            # التحقق من صحة النافذة الأب
            if not self._is_widget_valid(parent):
                self.logger.warning("النافذة الأب غير صالحة لـ Treeview")
                return None

            # إعداد المعاملات الافتراضية
            default_kwargs = {
                'show': 'headings',
                'height': 10
            }

            # دمج المعاملات
            default_kwargs.update(kwargs)

            # إنشاء Treeview مع معالجة أخطاء invalid command name
            treeview = None
            max_attempts = 3

            for attempt in range(max_attempts):
                try:
                    treeview = tk.ttk.Treeview(parent, **default_kwargs)

                    # إضافة حماية إضافية
                    self._protect_treeview(treeview)

                    break

                except tk.TclError as e:
                    if "invalid command name" in str(e):
                        # معالجة صامتة للخطأ - لا تظهر رسائل للمستخدم
                        if attempt < max_attempts - 1:
                            # مسح ذاكرة التخزين المؤقت وإعادة المحاولة
                            self._clear_widget_cache()
                            continue
                        else:
                            # فشل نهائي - إرجاع None بدون رسائل خطأ
                            return None
                    else:
                        # أخطاء أخرى - معالجة صامتة أيضاً
                        if attempt < max_attempts - 1:
                            continue
                        else:
                            return None

            if treeview:
                # تسجيل Treeview للمراقبة
                treeview_id = f"treeview_{self.window_counter}"
                self.active_windows[treeview_id] = treeview
                self.window_counter += 1

                self.logger.info("تم إنشاء Treeview آمن")
                return treeview
            else:
                return None

        except Exception as e:
            self.logger.error(f"فشل في إنشاء Treeview آمن: {e}")
            return None

    def _protect_treeview(self, treeview: tk.ttk.Treeview):
        """إضافة حماية إضافية لـ Treeview"""
        try:
            # إضافة معالج للأخطاء
            original_destroy = treeview.destroy

            def safe_destroy():
                try:
                    original_destroy()
                except tk.TclError as e:
                    if "invalid command name" not in str(e):
                        # معالجة صامتة للأخطاء الأخرى
                        pass
                    # تجاهل خطأ invalid command name عند الحذف بصمت
                except Exception as e:
                    # معالجة صامتة لجميع الأخطاء
                    pass

            treeview.destroy = safe_destroy

        except Exception as e:
            self.logger.warning(f"فشل في حماية Treeview: {e}")

    def _clear_widget_cache(self):
        """مسح ذاكرة التخزين المؤقت للعناصر"""
        try:
            import gc
            gc.collect()
        except Exception:
            pass
    
    def safe_destroy_widget(self, widget: tk.Widget) -> bool:
        """حذف عنصر بطريقة آمنة"""
        try:
            if self._is_widget_valid(widget):
                widget.destroy()
                self.logger.info("تم حذف العنصر بأمان")
                return True
            else:
                self.logger.warning("العنصر غير صالح للحذف")
                return False
                
        except Exception as e:
            self.logger.error(f"فشل في حذف العنصر بأمان: {e}")
            return False
    
    def close_all_windows(self) -> int:
        """إغلاق جميع النوافذ المسجلة"""
        closed_count = 0
        windows_to_close = list(self.active_windows.keys())
        
        for window_id in windows_to_close:
            if self._safe_close_window(window_id):
                closed_count += 1
        
        self.logger.info(f"تم إغلاق {closed_count} نافذة")
        return closed_count
    
    def _is_widget_valid(self, widget: tk.Widget) -> bool:
        """التحقق من صحة العنصر"""
        try:
            if widget is None:
                return False
            
            if not hasattr(widget, 'winfo_exists'):
                return False
            
            return widget.winfo_exists()
            
        except Exception:
            return False
    
    def _safe_close_window(self, window_id: str) -> bool:
        """إغلاق نافذة بطريقة آمنة"""
        try:
            if window_id in self.active_windows:
                window = self.active_windows[window_id]
                
                if self._is_widget_valid(window):
                    window.destroy()
                
                del self.active_windows[window_id]
                self.logger.info(f"تم إغلاق النافذة: {window_id}")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"فشل في إغلاق النافذة {window_id}: {e}")
            return False
    
    def get_window_count(self) -> int:
        """الحصول على عدد النوافذ النشطة"""
        return len(self.active_windows)
    
    def cleanup_invalid_windows(self) -> int:
        """تنظيف النوافذ غير الصالحة"""
        invalid_windows = []
        
        for window_id, window in self.active_windows.items():
            if not self._is_widget_valid(window):
                invalid_windows.append(window_id)
        
        for window_id in invalid_windows:
            del self.active_windows[window_id]
        
        if invalid_windows:
            self.logger.info(f"تم تنظيف {len(invalid_windows)} نافذة غير صالحة")
        
        return len(invalid_windows)

# إنشاء مثيل عام للاستخدام
safe_window_manager = SafeWindowManager()

def create_safe_toplevel(parent: tk.Widget, title: str = "", **kwargs) -> Optional[tk.Toplevel]:
    """دالة مساعدة لإنشاء نافذة Toplevel آمنة"""
    return safe_window_manager.create_safe_toplevel(parent, title, **kwargs)

def create_safe_window(title: str = "", **kwargs) -> Optional[ttk_bs.Window]:
    """دالة مساعدة لإنشاء نافذة Window آمنة"""
    return safe_window_manager.create_safe_window(title, **kwargs)

def create_safe_treeview(parent: tk.Widget, **kwargs) -> Optional[tk.ttk.Treeview]:
    """دالة مساعدة لإنشاء Treeview آمن"""
    return safe_window_manager.create_safe_treeview(parent, **kwargs)

def safe_destroy_widget(widget: tk.Widget) -> bool:
    """دالة مساعدة لحذف عنصر بأمان"""
    return safe_window_manager.safe_destroy_widget(widget)

def close_all_safe_windows() -> int:
    """دالة مساعدة لإغلاق جميع النوافذ الآمنة"""
    return safe_window_manager.close_all_windows()

def cleanup_invalid_windows() -> int:
    """دالة مساعدة لتنظيف النوافذ غير الصالحة"""
    return safe_window_manager.cleanup_invalid_windows()
