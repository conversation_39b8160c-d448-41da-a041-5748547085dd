@echo off
chcp 65001 > nul
title بناء نظام إدارة المخازن - الإصدار العربي الكامل

cls
echo.
echo ========================================
echo      بناء نظام إدارة المخازن
echo        الإصدار العربي الكامل
echo ========================================
echo.
echo سيتم إنشاء:
echo - ملف تنفيذي باسم عربي كامل
echo - جميع المكتبات في مجلد واحد
echo - حزمة جاهزة للنقل والتوزيع
echo.
echo ========================================
echo.

cd /d "%~dp0"

echo 🔍 فحص المتطلبات...
python -c "import PyInstaller; print('✅ PyInstaller متوفر')" 2>nul
if %errorlevel% neq 0 (
    echo ❌ PyInstaller غير مثبت!
    echo.
    echo تثبيت PyInstaller...
    pip install pyinstaller
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت PyInstaller
        pause
        exit /b 1
    )
)

echo.
echo 🔨 بدء عملية البناء...
echo ⏳ قد يستغرق هذا عدة دقائق...
echo.

python create_arabic_package.py

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo 🎉 تم بناء التطبيق بنجاح!
    echo ========================================
    echo.
    echo 📁 يمكنك العثور على التطبيق في:
    echo    dist\نظام_إدارة_المخازن_والمستودعات\
    echo.
    echo 📄 الملف التنفيذي:
    echo    نظام_إدارة_المخازن_والمستودعات.exe
    echo.
    echo 🚀 ملفات التشغيل:
    echo    - تشغيل_البرنامج.bat
    echo    - تشغيل_بصلاحيات_المدير.bat
    echo    - تشغيل_سريع.bat
    echo.
    echo 📚 ملفات التوثيق:
    echo    - معلومات_الإصدار.txt
    echo    - دليل_المستخدم_السريع.txt
    echo    - استكشاف_الأخطاء_وحلها.txt
    echo.
    echo ✅ المميزات المحققة:
    echo    - اسم عربي كامل للتطبيق
    echo    - جميع المكتبات في مجلد واحد
    echo    - سهولة النقل بين الأجهزة
    echo    - يعمل بدون تثبيت أي برامج إضافية
    echo.
    echo 🎊 الحزمة جاهزة للاستخدام والتوزيع!
    echo.
    echo هل تريد فتح مجلد التطبيق؟ (y/n)
    set /p choice=
    if /i "%choice%"=="y" (
        explorer "dist\نظام_إدارة_المخازن_والمستودعات"
    )
) else (
    echo.
    echo ========================================
    echo ❌ فشل في بناء التطبيق!
    echo ========================================
    echo.
    echo يرجى مراجعة رسائل الخطأ أعلاه
    echo وتأكد من:
    echo - تثبيت جميع المتطلبات
    echo - وجود جميع الملفات المطلوبة
    echo - توفر مساحة كافية على القرص
    echo.
)

echo.
echo اضغط أي مفتاح للخروج...
pause > nul