#!/usr/bin/env python3
"""
شاشة عمليات الصرف
Transactions Window
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from datetime import datetime, date
import threading
import pandas as pd

# واردات PDF
from reportlab.lib.pagesizes import A4, landscape
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from utils.transaction_pdf import pdf_generator
from reportlab.lib.units import inch
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import os

from config import APP_CONFIG, UI_CONFIG, get_message
from models import Transaction, Beneficiary, Item, TransactionItem, User
from database import db_manager

class TransactionsWindow:
    """شاشة عمليات الصرف"""
    
    def __init__(self, parent, main_window, embedded=False):
        self.parent = parent
        self.main_window = main_window
        self.embedded = embedded
        self.transactions_window = None
        self.transactions_tree = None
        self.search_var = None
        self.status_var = None
        self.date_from_var = None
        self.date_to_var = None
        
        # بيانات العمليات للترتيب
        self.transactions_data = []
        self.filtered_data = []

        if self.embedded:
            self.setup_embedded_interface()
        else:
            self.setup_window()
        self.load_transactions()
    
    def sort_transactions_by_number(self):
        """ترتيب العمليات حسب رقم العملية من الأصغر إلى الأكبر (ترتيب ذكي)"""
        def smart_sort_key(transaction):
            """مفتاح ترتيب ذكي يتعامل مع الأرقام والنصوص"""
            # الحصول على رقم العملية من البيانات
            if isinstance(transaction, dict):
                transaction_number = transaction.get('id', '') or ''
            else:
                transaction_number = getattr(transaction, 'id', '') or ''
            
            transaction_number = str(transaction_number).strip()
            
            if not transaction_number:
                # العناصر بدون رقم تذهب للنهاية
                return (2, 0, transaction_number)
            
            # محاولة تحويل إلى رقم
            try:
                clean_number = transaction_number.lstrip('0') or '0'
                numeric_value = float(clean_number)
                return (0, numeric_value, transaction_number)
            except (ValueError, TypeError):
                # إذا لم يكن رقماً، ترتيب أبجدي
                return (1, 0, transaction_number.lower())
        
        # ترتيب البيانات
        self.transactions_data.sort(key=smart_sort_key)
    
    def sort_filtered_data(self):
        """ترتيب البيانات المفلترة حسب رقم العملية"""
        def smart_sort_key(transaction):
            """مفتاح ترتيب ذكي يتعامل مع الأرقام والنصوص"""
            # الحصول على رقم العملية من البيانات
            if isinstance(transaction, dict):
                transaction_number = transaction.get('id', '') or ''
            else:
                transaction_number = getattr(transaction, 'id', '') or ''
            
            transaction_number = str(transaction_number).strip()
            
            if not transaction_number:
                # العناصر بدون رقم تذهب للنهاية
                return (2, 0, transaction_number)
            
            # محاولة تحويل إلى رقم
            try:
                clean_number = transaction_number.lstrip('0') or '0'
                numeric_value = float(clean_number)
                return (0, numeric_value, transaction_number)
            except (ValueError, TypeError):
                # إذا لم يكن رقماً، ترتيب أبجدي
                return (1, 0, transaction_number.lower())
        
        # ترتيب البيانات المفلترة
        self.filtered_data.sort(key=smart_sort_key)
    
    def setup_embedded_interface(self):
        """إعداد الواجهة المدمجة في الشاشة الرئيسية"""
        # مسح المحتوى الحالي
        self.main_window.clear_main_content()

        # تحديث شريط الحالة
        if hasattr(self.main_window, 'status_var'):
            self.main_window.status_var.set("عمليات الصرف")

        # إعداد المحتوى في الإطار الرئيسي
        self.setup_embedded_content()

        # تفعيل مفاتيح الاختصار العامة
        self.setup_shortcuts()

    def setup_window(self):
        """إعداد النافذة"""
        self.transactions_window = tk.Toplevel(self.parent)
        self.transactions_window.title("💳 عمليات الصرف")
        self.transactions_window.geometry("1300x750")
        self.transactions_window.resizable(True, True)

        # توسيط النافذة
        self.center_window()

        # تسجيل النافذة في مراقبة النشاط (معطل)
        # try:
        #     from activity_monitor import register_window_for_activity_monitoring
        #     register_window_for_activity_monitoring(self.transactions_window)
        # except Exception as e:
        #     print(f"تحذير: فشل في تسجيل النافذة في مراقبة النشاط: {e}")

        # إعداد المحتوى
        self.setup_content()

        # جعل النافذة في المقدمة
        self.transactions_window.lift()
        self.transactions_window.focus_force()
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.transactions_window.update_idletasks()
        
        screen_width = self.transactions_window.winfo_screenwidth()
        screen_height = self.transactions_window.winfo_screenheight()
        
        window_width = 1300
        window_height = 750
        
        x = max(0, (screen_width - window_width) // 2)
        y = max(0, (screen_height - window_height) // 2)
        
        self.transactions_window.geometry(f"{window_width}x{window_height}+{x}+{y}")
    
    def setup_embedded_content(self):
        """إعداد المحتوى المدمج في الشاشة الرئيسية"""
        # شريط العنوان والأدوات
        self.create_embedded_header(self.main_window.main_frame)

        # شريط البحث والفلاتر
        self.create_embedded_filters(self.main_window.main_frame)

        # جدول العمليات
        self.create_embedded_transactions_table(self.main_window.main_frame)

        # شريط الحالة
        self.create_embedded_status_bar(self.main_window.main_frame)

    def setup_content(self):
        """إعداد محتوى النافذة"""
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.transactions_window)
        main_frame.pack(fill=BOTH, expand=True, padx=10, pady=10)

        # شريط العنوان والأدوات
        self.create_header(main_frame)

        # شريط البحث والفلاتر
        self.create_filters(main_frame)

        # جدول العمليات
        self.create_transactions_table(main_frame)

        # شريط الحالة
        self.create_status_bar(main_frame)
    
    def create_header(self, parent):
        """إنشاء شريط العنوان والأدوات"""
        header_frame = ttk_bs.Frame(parent)
        header_frame.pack(fill=X, pady=(0, 10))
        
        # العنوان
        title_label = ttk_bs.Label(
            header_frame,
            text="💳 عمليات الصرف",
            bootstyle="primary"
        )
        title_label.pack(side=LEFT)
        
        # أزرار الأدوات
        tools_frame = ttk_bs.Frame(header_frame)
        tools_frame.pack(side=RIGHT)
        
        # زر عملية جديدة
        new_btn = ttk_bs.Button(
            tools_frame,
            text="➕ عملية جديدة",
            command=self.new_transaction,
            bootstyle="success",
            width=20
        )
        new_btn.pack(side=RIGHT, padx=5)
        
        # زر تحديث
        refresh_btn = ttk_bs.Button(
            tools_frame,
            text="🔄 تحديث",
            command=self.load_transactions,
            bootstyle="outline-primary",
            width=15
        )
        refresh_btn.pack(side=RIGHT, padx=5)
        
        # زر التصدير
        export_btn = ttk_bs.Button(
            tools_frame,
            text="📤 تصدير",
            command=self.show_export_options,
            bootstyle="outline-info",
            width=15
        )
        export_btn.pack(side=RIGHT, padx=5)
    
    def create_filters(self, parent):
        """إنشاء شريط البحث والفلاتر"""
        filters_frame = ttk_bs.LabelFrame(parent, text="🔍 البحث والفلاتر", bootstyle="info")
        filters_frame.pack(fill=X, pady=(0, 10))
        
        # الصف الأول - البحث والحالة
        row1_frame = ttk_bs.Frame(filters_frame)
        row1_frame.pack(fill=X, padx=10, pady=5)
        
        # البحث
        ttk_bs.Label(row1_frame, text="البحث:").pack(side=LEFT, padx=(0, 5))
        self.search_var = tk.StringVar()
        search_entry = ttk_bs.Entry(
            row1_frame,
            textvariable=self.search_var,
            width=30
        )
        search_entry.pack(side=LEFT, padx=(0, 20))
        search_entry.bind('<KeyRelease>', lambda e: self.filter_transactions())
        
        # الصف الثاني - فلتر التاريخ
        row2_frame = ttk_bs.Frame(filters_frame)
        row2_frame.pack(fill=X, padx=10, pady=5)
        
        # من تاريخ
        ttk_bs.Label(row2_frame, text="من تاريخ:").pack(side=LEFT, padx=(0, 5))
        self.date_from_var = tk.StringVar()
        date_from_entry = ttk_bs.Entry(
            row2_frame,
            textvariable=self.date_from_var,
            width=15
        )
        date_from_entry.pack(side=LEFT, padx=(0, 20))
        date_from_entry.bind('<KeyRelease>', lambda e: self.filter_transactions())
        
        # إلى تاريخ
        ttk_bs.Label(row2_frame, text="إلى تاريخ:").pack(side=LEFT, padx=(0, 5))
        self.date_to_var = tk.StringVar()
        date_to_entry = ttk_bs.Entry(
            row2_frame,
            textvariable=self.date_to_var,
            width=15
        )
        date_to_entry.pack(side=LEFT, padx=(0, 20))
        date_to_entry.bind('<KeyRelease>', lambda e: self.filter_transactions())
        
        # زر مسح الفلاتر
        clear_btn = ttk_bs.Button(
            row2_frame,
            text="🗑️ مسح الفلاتر",
            command=self.clear_filters,
            bootstyle="outline-warning",
            width=18
        )
        clear_btn.pack(side=LEFT, padx=20)

    def create_embedded_header(self, parent):
        """إنشاء شريط العنوان والأدوات للعرض المدمج"""
        header_frame = ttk_bs.Frame(parent)
        header_frame.pack(fill=X, pady=10)

        # العنوان
        title_label = ttk_bs.Label(
            header_frame,
            text="💳 عمليات الصرف",
            bootstyle="primary"
        )
        title_label.pack(side=LEFT)

        # أزرار الأدوات
        tools_frame = ttk_bs.Frame(header_frame)
        tools_frame.pack(side=RIGHT)

        # زر عملية جديدة
        new_btn = ttk_bs.Button(
            tools_frame,
            text="➕ عملية جديدة",
            command=self.new_transaction,
            bootstyle="success",
            width=20
        )
        new_btn.pack(side=RIGHT, padx=5)

        # زر تحديث
        refresh_btn = ttk_bs.Button(
            tools_frame,
            text="🔄 تحديث",
            command=self.load_transactions,
            bootstyle="outline-primary",
            width=15
        )
        refresh_btn.pack(side=RIGHT, padx=5)

        # زر التصدير
        export_btn = ttk_bs.Button(
            tools_frame,
            text="📤 تصدير",
            command=self.show_export_options,
            bootstyle="outline-info",
            width=15
        )
        export_btn.pack(side=RIGHT, padx=5)

    def create_embedded_filters(self, parent):
        """إنشاء شريط البحث والفلاتر للعرض المدمج"""
        filters_frame = ttk_bs.LabelFrame(parent, text="🔍 البحث والفلاتر", bootstyle="info")
        filters_frame.pack(fill=X, pady=(0, 10))

        # الصف الأول - البحث
        row1_frame = ttk_bs.Frame(filters_frame)
        row1_frame.pack(fill=X, padx=10, pady=5)

        # البحث
        ttk_bs.Label(row1_frame, text="البحث:").pack(side=LEFT, padx=(0, 5))
        self.search_var = tk.StringVar()
        search_entry = ttk_bs.Entry(
            row1_frame,
            textvariable=self.search_var,
            width=30
        )
        search_entry.pack(side=LEFT, padx=(0, 20))
        search_entry.bind('<KeyRelease>', lambda e: self.filter_transactions())

        # زر مسح الفلاتر
        clear_btn = ttk_bs.Button(
            row1_frame,
            text="🗑️ مسح الفلاتر",
            command=self.clear_filters,
            bootstyle="outline-warning",
            width=18
        )
        clear_btn.pack(side=LEFT, padx=20)

    def create_embedded_transactions_table(self, parent):
        """إنشاء جدول العمليات للعرض المدمج"""
        table_frame = ttk_bs.LabelFrame(parent, text="📋 قائمة العمليات", bootstyle="primary")
        table_frame.pack(fill=BOTH, expand=True, pady=(0, 10))

        # إنشاء Treeview مع الأعمدة المطلوبة فقط
        columns = ("id", "date", "beneficiary", "total_items", "user")
        self.transactions_tree = ttk.Treeview(
            table_frame,
            columns=columns,
            show="headings",
            height=15
        )

        # تعيين عناوين الأعمدة
        headings = {
            "id": "رقم العملية",
            "date": "تاريخ العملية",
            "beneficiary": "المستفيد",
            "total_items": "عدد الأصناف",
            "user": "مدخل البيانات"
        }

        for col, heading in headings.items():
            self.transactions_tree.heading(col, text=heading)
            self.transactions_tree.column(col, width=150, anchor="center")

        # تعيين عرض أعمدة محددة
        self.transactions_tree.column("id", width=120)
        self.transactions_tree.column("date", width=150)
        self.transactions_tree.column("beneficiary", width=200)
        self.transactions_tree.column("total_items", width=120)
        self.transactions_tree.column("user", width=180)

        # شريط التمرير
        scrollbar_y = ttk.Scrollbar(table_frame, orient=VERTICAL, command=self.transactions_tree.yview)
        self.transactions_tree.configure(yscrollcommand=scrollbar_y.set)

        # تخطيط الجدول
        self.transactions_tree.pack(side=LEFT, fill=BOTH, expand=True, padx=(10, 0), pady=10)
        scrollbar_y.pack(side=RIGHT, fill=Y, pady=10)

        # ربط الأحداث
        self.transactions_tree.bind('<Double-1>', self.on_transaction_double_click)
        self.transactions_tree.bind('<Button-3>', self.show_embedded_context_menu)

    def create_embedded_status_bar(self, parent):
        """إنشاء شريط الحالة للعرض المدمج"""
        status_frame = ttk_bs.Frame(parent)
        status_frame.pack(fill=X)

        self.status_var = tk.StringVar(value="جاري تحميل البيانات...")
        status_label = ttk_bs.Label(
            status_frame,
            textvariable=self.status_var,
            bootstyle="secondary"
        )
        status_label.pack(side=LEFT)

    def create_transactions_table(self, parent):
        """إنشاء جدول العمليات"""
        table_frame = ttk_bs.LabelFrame(parent, text="📋 قائمة العمليات", bootstyle="primary")
        table_frame.pack(fill=BOTH, expand=True, pady=(0, 10))
        
        # إنشاء Treeview
        columns = ("id", "date", "beneficiary", "total_items", "status", "notes", "user", "created_at")
        self.transactions_tree = ttk.Treeview(
            table_frame,
            columns=columns,
            show="headings",
            height=15
        )
        
        # تعيين عناوين الأعمدة
        headings = {
            "id": "رقم العملية",
            "date": "تاريخ العملية",
            "beneficiary": "المستفيد",
            "total_items": "عدد الأصناف",
            "status": "الحالة",
            "notes": "ملاحظات",
            "user": "المستخدم",
            "created_at": "تاريخ الإنشاء"
        }
        
        for col, heading in headings.items():
            self.transactions_tree.heading(col, text=heading)
            self.transactions_tree.column(col, width=120, anchor="center")
        
        # تعيين عرض أعمدة محددة
        self.transactions_tree.column("id", width=100)
        self.transactions_tree.column("date", width=120)
        self.transactions_tree.column("beneficiary", width=200)
        self.transactions_tree.column("total_items", width=100)
        self.transactions_tree.column("status", width=100)
        self.transactions_tree.column("notes", width=250)
        self.transactions_tree.column("user", width=150)
        self.transactions_tree.column("created_at", width=150)
        
        # شريط التمرير
        scrollbar_y = ttk.Scrollbar(table_frame, orient=VERTICAL, command=self.transactions_tree.yview)
        scrollbar_x = ttk.Scrollbar(table_frame, orient=HORIZONTAL, command=self.transactions_tree.xview)
        self.transactions_tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)
        
        # تخطيط الجدول
        self.transactions_tree.pack(side=LEFT, fill=BOTH, expand=True, padx=(10, 0), pady=10)
        scrollbar_y.pack(side=RIGHT, fill=Y, pady=10)
        scrollbar_x.pack(side=BOTTOM, fill=X, padx=10)
        
        # ربط الأحداث
        self.transactions_tree.bind('<Double-1>', self.on_transaction_double_click)
        self.transactions_tree.bind('<Button-3>', self.show_context_menu)
    
    def create_status_bar(self, parent):
        """إنشاء شريط الحالة"""
        status_frame = ttk_bs.Frame(parent)
        status_frame.pack(fill=X)
        
        self.status_var = tk.StringVar(value="جاري تحميل البيانات...")
        status_label = ttk_bs.Label(
            status_frame,
            textvariable=self.status_var,
            bootstyle="secondary"
        )
        status_label.pack(side=LEFT)
    
    def load_transactions(self):
        """تحميل عمليات الصرف"""
        try:
            # مسح البيانات الحالية
            for item in self.transactions_tree.get_children():
                self.transactions_tree.delete(item)

            self.status_var.set("جاري تحميل البيانات...")

            # التحقق من وجود جدول العمليات
            try:
                db_manager.execute_query("SELECT COUNT(*) FROM transactions")
            except:
                # إنشاء جدول العمليات إذا لم يكن موجوداً
                db_manager.execute_query("""
                    CREATE TABLE IF NOT EXISTS transactions (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        transaction_number TEXT UNIQUE NOT NULL,
                        beneficiary_id INTEGER NOT NULL,
                        transaction_date DATE NOT NULL,
                        transaction_type TEXT DEFAULT 'صرف',
                        status TEXT DEFAULT 'مكتمل',
                        notes TEXT,
                        total_amount REAL DEFAULT 0,
                        user_id INTEGER,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                """)

                # إنشاء جدول تفاصيل العمليات
                db_manager.execute_query("""
                    CREATE TABLE IF NOT EXISTS transaction_items (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        transaction_id INTEGER NOT NULL,
                        item_id INTEGER NOT NULL,
                        quantity REAL NOT NULL,
                        unit_price REAL DEFAULT 0,
                        total_price REAL DEFAULT 0,
                        notes TEXT,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (transaction_id) REFERENCES transactions(id) ON DELETE CASCADE
                    )
                """)

            # تحميل البيانات من قاعدة البيانات
            query = """
                SELECT t.id, t.transaction_date, b.name as beneficiary_name,
                       COUNT(ti.id) as total_items, t.status, t.notes,
                       u.full_name as user_name, t.created_at
                FROM transactions t
                LEFT JOIN beneficiaries b ON t.beneficiary_id = b.id
                LEFT JOIN transaction_items ti ON t.id = ti.transaction_id
                LEFT JOIN users u ON t.user_id = u.id
                GROUP BY t.id, t.transaction_date, b.name, t.status, t.notes, u.full_name, t.created_at
                ORDER BY t.transaction_date DESC, t.id DESC
            """

            transactions = db_manager.fetch_all(query)
            
            # تحويل البيانات إلى قائمة من القواميس وحفظها
            self.transactions_data = []
            for transaction in transactions:
                try:
                    transaction_dict = dict(transaction)
                    self.transactions_data.append(transaction_dict)
                except:
                    pass
            
            # تطبيق الترتيب الذكي حسب رقم العملية
            self.sort_transactions_by_number()
            
            # تحديث البيانات المفلترة
            self.filtered_data = self.transactions_data.copy()
            
            # إضافة البيانات المرتبة للجدول
            for transaction_dict in self.transactions_data:

                # تنسيق التاريخ
                transaction_date = transaction_dict.get('transaction_date', '')
                if transaction_date:
                    try:
                        date_obj = datetime.fromisoformat(transaction_date)
                        formatted_date = date_obj.strftime('%Y-%m-%d')
                    except:
                        formatted_date = transaction_date
                else:
                    formatted_date = ''

                created_at = transaction_dict.get('created_at', '')
                if created_at:
                    try:
                        date_obj = datetime.fromisoformat(created_at)
                        formatted_created = date_obj.strftime('%Y-%m-%d %H:%M')
                    except:
                        formatted_created = created_at
                else:
                    formatted_created = ''

                # تنسيق الحالة
                status = transaction_dict.get('status', '')
                status_display = {
                    'pending': 'معلق',
                    'completed': 'مكتمل',
                    'cancelled': 'ملغي'
                }.get(status, status)
                
                if self.embedded:
                    # عرض مبسط للشاشة الرئيسية
                    self.transactions_tree.insert('', 'end', values=(
                        transaction_dict.get('id', ''),
                        formatted_date,
                        transaction_dict.get('beneficiary_name', ''),
                        transaction_dict.get('total_items', 0),
                        transaction_dict.get('user_name', '')
                    ))
                else:
                    # عرض كامل للنافذة المنفصلة
                    self.transactions_tree.insert('', 'end', values=(
                        transaction_dict.get('id', ''),
                        formatted_date,
                        transaction_dict.get('beneficiary_name', ''),
                        transaction_dict.get('total_items', 0),
                        status_display,
                        transaction_dict.get('notes', ''),
                        transaction_dict.get('user_name', ''),
                        formatted_created
                    ))
            
            # تحديث شريط الحالة
            count = len(transactions) if transactions else 0
            if count == 0:
                self.status_var.set("لا توجد عمليات صرف")
            else:
                self.status_var.set(f"تم تحميل {count} عملية")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل عمليات الصرف: {e}")
            self.status_var.set("خطأ في تحميل البيانات")
    
    def filter_transactions(self):
        """فلترة العمليات مع الحفاظ على الترتيب"""
        search_text = self.search_var.get().lower() if self.search_var else ""
        date_from = self.date_from_var.get() if self.date_from_var else ""
        date_to = self.date_to_var.get() if self.date_to_var else ""
        
        # تطبيق الفلاتر
        self.filtered_data = []
        
        for transaction in self.transactions_data:
            # فلتر البحث
            if search_text:
                searchable_text = f"{transaction.get('id', '')} {transaction.get('beneficiary_name', '')} {transaction.get('notes', '')}".lower()
                if search_text not in searchable_text:
                    continue
            
            # فلتر التاريخ (يمكن تطويره لاحقاً)
            # if date_from or date_to:
            #     # تطبيق فلتر التاريخ
            #     pass
            
            self.filtered_data.append(transaction)
        
        # ترتيب البيانات المفلترة حسب رقم العملية (ضمان الترتيب الثابت)
        self.sort_filtered_data()
        
        # تحديث الجدول
        self.update_transactions_table()
    
    def update_transactions_table(self):
        """تحديث جدول العمليات بالبيانات المفلترة والمرتبة"""
        # مسح البيانات الحالية
        if self.transactions_tree:
            for item in self.transactions_tree.get_children():
                self.transactions_tree.delete(item)
        
        # ترتيب البيانات المفلترة قبل العرض (ضمان الترتيب الثابت)
        self.sort_filtered_data()
        
        # إضافة البيانات المفلترة المرتبة
        for transaction_dict in self.filtered_data:
            # تنسيق التاريخ
            transaction_date = transaction_dict.get('transaction_date', '')
            if transaction_date:
                try:
                    date_obj = datetime.fromisoformat(transaction_date)
                    formatted_date = date_obj.strftime('%Y-%m-%d')
                except:
                    formatted_date = transaction_date
            else:
                formatted_date = ''

            created_at = transaction_dict.get('created_at', '')
            if created_at:
                try:
                    date_obj = datetime.fromisoformat(created_at)
                    formatted_created = date_obj.strftime('%Y-%m-%d %H:%M')
                except:
                    formatted_created = created_at
            else:
                formatted_created = ''

            # تنسيق الحالة
            status = transaction_dict.get('status', '')
            status_display = {
                'pending': 'معلق',
                'completed': 'مكتمل',
                'cancelled': 'ملغي'
            }.get(status, status)
            
            if self.embedded:
                # عرض مبسط للشاشة الرئيسية
                self.transactions_tree.insert('', 'end', values=(
                    transaction_dict.get('id', ''),
                    formatted_date,
                    transaction_dict.get('beneficiary_name', ''),
                    transaction_dict.get('total_items', 0),
                    transaction_dict.get('user_name', '')
                ))
            else:
                # عرض كامل للنافذة المنفصلة
                self.transactions_tree.insert('', 'end', values=(
                    transaction_dict.get('id', ''),
                    formatted_date,
                    transaction_dict.get('beneficiary_name', ''),
                    transaction_dict.get('total_items', 0),
                    status_display,
                    transaction_dict.get('notes', ''),
                    transaction_dict.get('user_name', ''),
                    formatted_created
                ))
        
        # تحديث شريط الحالة
        count = len(self.filtered_data)
        total = len(self.transactions_data)
        if hasattr(self, 'status_var') and self.status_var:
            if count == 0:
                self.status_var.set("لا توجد عمليات صرف")
            elif count == total:
                self.status_var.set(f"تم تحميل {count} عملية")
            else:
                self.status_var.set(f"عرض {count} من {total} عملية")
    
    def clear_filters(self):
        """مسح جميع الفلاتر مع الحفاظ على الترتيب"""
        if self.search_var:
            self.search_var.set("")
        if self.date_from_var:
            self.date_from_var.set("")
        if self.date_to_var:
            self.date_to_var.set("")
        
        # إعادة تعيين البيانات المفلترة إلى جميع البيانات
        self.filtered_data = self.transactions_data.copy()
        
        # ترتيب البيانات حسب رقم العملية
        self.sort_filtered_data()
        
        # تحديث الجدول
        self.update_transactions_table()
    
    def on_transaction_double_click(self, event):
        """معالج النقر المزدوج على عملية"""
        selection = self.transactions_tree.selection()
        if selection:
            item = self.transactions_tree.item(selection[0])
            values = item['values']
            
            # عرض تفاصيل العملية
            self.show_transaction_details(values[0])
    
    def show_transaction_details(self, transaction_id):
        """عرض تفاصيل العملية"""
        try:
            # فتح نافذة تفاصيل العملية الجديدة
            from ui.transaction_details_window import TransactionDetailsWindow
            parent_window = self.parent if self.embedded else self.transactions_window
            TransactionDetailsWindow(parent_window, transaction_id, self.main_window)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في عرض تفاصيل العملية: {e}")
    
    def show_embedded_context_menu(self, event):
        """عرض القائمة السياقية للعرض المدمج"""
        selection = self.transactions_tree.selection()
        if selection:
            context_menu = tk.Menu(self.parent, tearoff=0)
            context_menu.add_command(label="تفاصيل عملية الصرف", command=lambda: self.on_transaction_double_click(None))
            context_menu.add_command(label="تعديل عمليات الصرف", command=self.edit_transaction)

            try:
                context_menu.tk_popup(event.x_root, event.y_root)
            finally:
                context_menu.grab_release()

    def show_context_menu(self, event):
        """عرض القائمة السياقية"""
        selection = self.transactions_tree.selection()
        if selection:
            context_menu = tk.Menu(self.transactions_window, tearoff=0)
            context_menu.add_command(label="عرض التفاصيل", command=lambda: self.on_transaction_double_click(None))
            context_menu.add_command(label="طباعة", command=self.print_transaction)
            context_menu.add_separator()
            context_menu.add_command(label="حذف", command=self.delete_transaction)

            try:
                context_menu.tk_popup(event.x_root, event.y_root)
            finally:
                context_menu.grab_release()
    
    def new_transaction(self):
        """إنشاء عملية صرف جديدة"""
        try:
            from ui.new_transaction_window import NewTransactionWindow
            parent_window = self.parent if self.embedded else self.transactions_window
            NewTransactionWindow(parent_window, self.main_window)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح شاشة عملية صرف جديدة: {e}")

    def edit_transaction(self):
        """تعديل عملية الصرف"""
        selection = self.transactions_tree.selection()
        if selection:
            item = self.transactions_tree.item(selection[0])
            values = item['values']
            transaction_id = values[0]

            try:
                # فتح نافذة تعديل العملية الجديدة
                from ui.edit_transaction_window import EditTransactionWindow
                parent_window = self.parent if self.embedded else self.transactions_window
                EditTransactionWindow(parent_window, transaction_id, self.main_window, None)
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في فتح نافذة التعديل: {e}")
        else:
            messagebox.showwarning("تحذير", "يرجى اختيار عملية صرف للتعديل")

    def print_transaction(self):
        """طباعة العملية"""
        messagebox.showinfo("قريباً", "طباعة العملية قيد التطوير")

    def delete_transaction(self):
        """حذف العملية مع إرجاع الكميات للمخزون"""
        selection = self.transactions_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار عملية صرف للحذف")
            return

        item = self.transactions_tree.item(selection[0])
        values = item['values']
        transaction_id = values[0]

        try:
            # فتح نافذة تفاصيل العملية للحذف
            from ui.transaction_details_window import TransactionDetailsWindow
            parent_window = self.parent if self.embedded else self.transactions_window
            details_window = TransactionDetailsWindow(parent_window, transaction_id, self.main_window)

            # تنفيذ الحذف مباشرة
            details_window.delete_transaction()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حذف العملية: {e}")

    def get_beneficiary_info(self, transaction):
        """الحصول على معلومات المستفيد"""
        beneficiary_name = ""
        beneficiary_number = ""
        
        if hasattr(transaction, 'beneficiary_id') and transaction.beneficiary_id:
            try:
                beneficiary = Beneficiary.get_by_id(transaction.beneficiary_id)
                if beneficiary:
                    beneficiary_name = beneficiary.name or ""
                    beneficiary_number = beneficiary.number or ""
            except Exception as e:
                print(f"خطأ في الحصول على بيانات المستفيد {transaction.beneficiary_id}: {e}")
        elif isinstance(transaction, dict):
            beneficiary_name = transaction.get('beneficiary_name', '')
            beneficiary_number = transaction.get('beneficiary_number', '')
            
        return beneficiary_name, beneficiary_number

    def get_user_info(self, transaction):
        """الحصول على معلومات مدخل البيانات"""
        user_name = ""
        
        if hasattr(transaction, 'user_id') and transaction.user_id:
            try:
                user = User.get_by_id(transaction.user_id)
                if user:
                    user_name = user.username or ""
            except Exception as e:
                print(f"خطأ في الحصول على بيانات المستخدم {transaction.user_id}: {e}")
        elif isinstance(transaction, dict):
            user_name = transaction.get('user_name', '')
            
        return user_name

    def show_export_options(self):
        """عرض نافذة خيارات التصدير"""
        try:
            # التحقق من وجود بيانات للتصدير
            if not self.transactions_data:
                messagebox.showwarning("تحذير", "لا توجد عمليات صرف للتصدير")
                return

            # إنشاء نافذة الخيارات
            options_window = tk.Toplevel(self.parent)
            options_window.title("خيارات التصدير")
            options_window.geometry("400x300")
            options_window.resizable(False, False)
            
            # توسيط النافذة في وسط الشاشة
            options_window.transient(self.parent)
            options_window.grab_set()
            
            # حساب موضع الوسط
            options_window.update_idletasks()
            screen_width = options_window.winfo_screenwidth()
            screen_height = options_window.winfo_screenheight()
            window_width = 400
            window_height = 300
            x = (screen_width - window_width) // 2
            y = (screen_height - window_height) // 2
            options_window.geometry(f"{window_width}x{window_height}+{x}+{y}")
            
            # العنوان
            title_label = ttk_bs.Label(
                options_window, 
                text="📤 اختر نوع التصدير", 
                font=('Arial', 14, 'bold'),
                bootstyle="primary"
            )
            title_label.pack(pady=20)
            
            # الخيارات
            options_frame = ttk_bs.Frame(options_window)
            options_frame.pack(pady=20, padx=20, fill=BOTH, expand=True)
            
            # خيار التصدير المفصل
            detailed_btn = ttk_bs.Button(
                options_frame,
                text="📊 تصدير مفصل\n(مع تفاصيل الأصناف)",
                command=lambda: self.execute_export_option(options_window, 'detailed'),
                bootstyle="success",
                width=30
            )
            detailed_btn.pack(pady=10, fill=X)
            
            # خيار التصدير المبسط
            simple_btn = ttk_bs.Button(
                options_frame,
                text="📋 تصدير مبسط\n(العمليات الأساسية فقط)",
                command=lambda: self.execute_export_option(options_window, 'simple'),
                bootstyle="info",
                width=30
            )
            simple_btn.pack(pady=10, fill=X)
            
            # خيار تصدير البيانات المفلترة
            if hasattr(self, 'filtered_data') and self.filtered_data:
                filtered_btn = ttk_bs.Button(
                    options_frame,
                    text="🔍 تصدير البيانات المفلترة\n(النتائج المعروضة حالياً)",
                    command=lambda: self.execute_export_option(options_window, 'filtered'),
                    bootstyle="warning",
                    width=30
                )
                filtered_btn.pack(pady=10, fill=X)
            
            # زر الإلغاء
            cancel_btn = ttk_bs.Button(
                options_frame,
                text="❌ إلغاء",
                command=options_window.destroy,
                bootstyle="outline-danger",
                width=30
            )
            cancel_btn.pack(pady=20, fill=X)
            
            # جعل النافذة في المقدمة
            options_window.lift()
            options_window.focus_force()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في عرض خيارات التصدير: {e}")

    def execute_export_option(self, options_window, export_type):
        """تنفيذ خيار التصدير المحدد"""
        try:
            # إغلاق نافذة الخيارات
            options_window.destroy()
            
            # تنفيذ نوع التصدير المطلوب
            if export_type == 'detailed':
                self.export_transactions()
            elif export_type == 'simple':
                self.export_transactions_simple()
            elif export_type == 'filtered':
                self.export_filtered_transactions()
                
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تنفيذ التصدير: {e}")

    def export_transactions(self):
        """تصدير عمليات الصرف إلى ملف Excel"""
        try:
            # التحقق من وجود بيانات للتصدير
            if not self.transactions_data:
                messagebox.showwarning("تحذير", "لا توجد عمليات صرف للتصدير")
                return

            # اختيار مكان الحفظ
            file_path = filedialog.asksaveasfilename(
                title="حفظ ملف PDF - عمليات الصرف",
                defaultextension=".pdf",
                filetypes=[
                    ("PDF files", "*.pdf"),
                    ("All files", "*.*")
                ],
                initialfile=f"عمليات_الصرف_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
            )

            if not file_path:
                return

            # عرض شريط التقدم
            progress_window = self.show_export_progress()
            
            # تحضير البيانات للتصدير
            export_data = []
            total_transactions = len(self.transactions_data)
            
            for i, transaction in enumerate(self.transactions_data):
                try:
                    # تحديث شريط التقدم
                    progress = int((i / total_transactions) * 100)
                    self.update_export_progress(progress_window, progress, f"معالجة العملية {i+1} من {total_transactions}")
                    
                    # الحصول على بيانات المستفيد
                    beneficiary_name = transaction.get('beneficiary_name', '')
                    beneficiary_number = ""
                    
                    # الحصول على بيانات مدخل البيانات
                    user_name = transaction.get('user_name', '')

                    # الحصول على أصناف العملية
                    transaction_id = transaction.get('id')
                    transaction_items = TransactionItem.get_by_transaction(transaction_id) if transaction_id else []
                    total_items = len(transaction_items)
                    total_quantity = sum(item.quantity for item in transaction_items)
                    
                    # إنشاء سجل للعملية الأساسية
                    base_record = {
                        'رقم العملية': transaction.get('transaction_number') or transaction.get('id', ''),
                        'تاريخ العملية': transaction.get('transaction_date', ''),
                        'اسم المستفيد': beneficiary_name,
                        'رقم المستفيد': beneficiary_number,
                        'نوع العملية': transaction.get('transaction_type', 'صرف'),
                        'حالة العملية': transaction.get('status', ''),
                        'عدد الأصناف': total_items,
                        'إجمالي الكمية': total_quantity,
                        'إجمالي القيمة': transaction.get('total_amount', 0),
                        'ملاحظات العملية': transaction.get('notes', ''),
                        'مدخل البيانات': user_name,
                        'تاريخ الإنشاء': transaction.get('created_at', '')
                    }

                    # إضافة تفاصيل الأصناف
                    if transaction_items:
                        for j, item in enumerate(transaction_items):
                            # الحصول على بيانات الصنف
                            item_name = ""
                            item_code = ""
                            item_unit = ""
                            try:
                                item_obj = Item.get_by_id(item.item_id)
                                if item_obj:
                                    item_name = item_obj.name
                                    item_code = item_obj.code or ""
                                    item_unit = item_obj.unit or ""
                            except:
                                pass

                            # إنشاء سجل مفصل لكل صنف
                            detailed_record = base_record.copy()
                            detailed_record.update({
                                'رقم الصنف': j + 1,
                                'كود الصنف': item_code,
                                'اسم الصنف': item_name,
                                'الوحدة': item_unit,
                                'الكمية': item.quantity,
                                'سعر الوحدة': item.unit_price,
                                'إجمالي سعر الصنف': item.total_price,
                                'ملاحظات الصنف': item.notes or ''
                            })
                            export_data.append(detailed_record)
                    else:
                        # إذا لم توجد أصناف، أضف السجل الأساسي فقط
                        base_record.update({
                            'رقم الصنف': '',
                            'كود الصنف': '',
                            'اسم الصنف': '',
                            'الوحدة': '',
                            'الكمية': '',
                            'سعر الوحدة': '',
                            'إجمالي سعر الصنف': '',
                            'ملاحظات الصنف': ''
                        })
                        export_data.append(base_record)

                except Exception as e:
                    print(f"خطأ في معالجة العملية {transaction.get('id', 'غير معروف')}: {e}")
                    continue

            # إنشاء ملف PDF
            self.update_export_progress(progress_window, 95, "إنشاء ملف PDF...")
            
            # إنشاء PDF أفقي
            doc = SimpleDocTemplate(
                file_path,
                pagesize=landscape(A4),
                rightMargin=0.5*inch,
                leftMargin=0.5*inch,
                topMargin=0.5*inch,
                bottomMargin=0.5*inch
            )
            
            # إعداد الخط العربي
            try:
                # محاولة تحميل خط Amiri العربي المفتوح المصدر
                fonts_dir = os.path.join(os.path.dirname(__file__), '..', 'assets', 'fonts')
                amiri_path = os.path.join(fonts_dir, 'Amiri-Regular.ttf')

                if os.path.exists(amiri_path):
                    pdfmetrics.registerFont(TTFont('Amiri', amiri_path))
                    arabic_font = 'Amiri'
                else:
                    arabic_font = 'Helvetica'
            except:
                arabic_font = 'Helvetica'
            
            # إعداد الأنماط مع دعم RTL
            styles = getSampleStyleSheet()
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontName=arabic_font,
                fontSize=16,
                alignment=2,  # محاذاة لليمين (Right)
                spaceAfter=20
            )
            
            # محتوى PDF
            story = []
            
            # العنوان
            title = Paragraph("تقرير عمليات الصرف المفصل", title_style)
            story.append(title)
            story.append(Spacer(1, 12))
            
            # معلومات التقرير
            info_text = f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}<br/>عدد العمليات: {len(self.transactions_data)}<br/>إجمالي السجلات: {len(export_data)}"
            info_para = Paragraph(info_text, styles['Normal'])
            story.append(info_para)
            story.append(Spacer(1, 20))
            
            # إعداد بيانات الجدول
            if export_data:
                # عناوين الأعمدة
                headers = [
                    'رقم العملية', 'تاريخ العملية', 'المستفيد', 'نوع العملية',
                    'عدد الأصناف', 'إجمالي الكمية', 'اسم الصنف', 'الكمية',
                    'مدخل البيانات'
                ]
                
                # بيانات الجدول
                table_data = [headers]
                
                for row in export_data:
                    table_row = [
                        pdf_generator.process_arabic_text(str(row.get('رقم العملية', ''))),
                        pdf_generator.process_arabic_text(str(row.get('تاريخ العملية', ''))),
                        pdf_generator.process_arabic_text(str(row.get('اسم المستفيد', ''))),
                        pdf_generator.process_arabic_text(str(row.get('نوع العملية', ''))),
                        pdf_generator.process_arabic_text(str(row.get('عدد الأصناف', ''))),
                        pdf_generator.process_arabic_text(str(row.get('إجمالي الكمية', ''))),
                        pdf_generator.process_arabic_text(str(row.get('اسم الصنف', ''))),
                        pdf_generator.process_arabic_text(str(row.get('الكمية', ''))),
                        pdf_generator.process_arabic_text(str(row.get('مدخل البيانات', '')))
                    ]
                    table_data.append(table_row)
                
                # إنشاء الجدول مع تمييز لوني للصفوف
                table = Table(table_data)
                row_colors = []
                for i, row in enumerate(table_data):
                    if i == 0:
                        # لون خلفية العنوان
                        row_colors.append(('BACKGROUND', (0, i), (-1, i), colors.grey))
                        row_colors.append(('TEXTCOLOR', (0, i), (-1, i), colors.whitesmoke))
                        row_colors.append(('FONTNAME', (0, i), (-1, i), arabic_font))
                        row_colors.append(('FONTSIZE', (0, i), (-1, i), 10))
                        row_colors.append(('BOTTOMPADDING', (0, i), (-1, i), 12))
                    else:
                        # تمييز الصفوف بالتناوب
                        bg_color = colors.beige if i % 2 == 1 else colors.whitesmoke
                        row_colors.append(('BACKGROUND', (0, i), (-1, i), bg_color))
                        row_colors.append(('FONTNAME', (0, i), (-1, i), arabic_font))
                        row_colors.append(('FONTSIZE', (0, i), (-1, i), 8))
                        
                        # تمييز لوني حسب توفر الكمية (أخضر إذا الكمية > 0، أحمر إذا 0)
                        try:
                            quantity = float(row[7])  # عمود الكمية
                            if quantity > 0:
                                row_colors.append(('BACKGROUND', (0, i), (-1, i), colors.lightgreen))
                            else:
                                row_colors.append(('BACKGROUND', (0, i), (-1, i), colors.salmon))
                        except:
                            pass
                
                # إضافة حدود للجدول
                row_colors.append(('GRID', (0, 0), (-1, -1), 1, colors.black))
                
                table.setStyle(TableStyle(row_colors))
                
                story.append(table)
            
            # بناء PDF
            doc.build(story)

            # إغلاق شريط التقدم
            self.close_export_progress(progress_window)

            # عرض رسالة النجاح
            messagebox.showinfo(
                "نجح التصدير",
                f"تم تصدير {len(self.transactions_data)} عملية صرف بنجاح إلى ملف PDF:\n{file_path}\n\n"
                f"إجمالي السجلات المُصدرة: {len(export_data)}"
            )

            # فتح نافذة الطباعة تلقائياً بعد التصدير
            try:
                import webbrowser
                webbrowser.open(file_path)
            except Exception as e:
                print(f"فشل في فتح ملف الطباعة تلقائياً: {e}")

        except Exception as e:
            # إغلاق شريط التقدم في حالة الخطأ
            try:
                self.close_export_progress(progress_window)
            except:
                pass
            
            messagebox.showerror("خطأ في التصدير", f"فشل في تصدير عمليات الصرف:\n{str(e)}")
            print(f"خطأ مفصل في التصدير: {e}")

    def show_export_progress(self):
        """عرض نافذة شريط التقدم للتصدير"""
        try:
            progress_window = tk.Toplevel(self.parent)
            progress_window.title("تصدير عمليات الصرف")
            progress_window.geometry("400x150")
            progress_window.resizable(False, False)
            
            # توسيط النافذة في وسط الشاشة
            progress_window.transient(self.parent)
            progress_window.grab_set()
            
            # حساب موضع الوسط
            progress_window.update_idletasks()
            screen_width = progress_window.winfo_screenwidth()
            screen_height = progress_window.winfo_screenheight()
            window_width = 400
            window_height = 150
            x = (screen_width - window_width) // 2
            y = (screen_height - window_height) // 2
            progress_window.geometry(f"{window_width}x{window_height}+{x}+{y}")
            
            # العنوان
            title_label = ttk.Label(progress_window, text="جاري تصدير عمليات الصرف...", font=('Arial', 12, 'bold'))
            title_label.pack(pady=20)
            
            # شريط التقدم
            progress_bar = ttk.Progressbar(progress_window, length=300, mode='determinate')
            progress_bar.pack(pady=10)
            
            # نص الحالة
            status_label = ttk.Label(progress_window, text="بدء التصدير...")
            status_label.pack(pady=10)
            
            # تحديث النافذة
            progress_window.update()
            
            return {
                'window': progress_window,
                'progress_bar': progress_bar,
                'status_label': status_label
            }
        except:
            return None

    def update_export_progress(self, progress_window, progress, status_text):
        """تحديث شريط التقدم"""
        try:
            if progress_window:
                progress_window['progress_bar']['value'] = progress
                progress_window['status_label'].config(text=status_text)
                progress_window['window'].update()
        except:
            pass

    def close_export_progress(self, progress_window):
        """إغلاق نافذة شريط التقدم"""
        try:
            if progress_window and progress_window['window']:
                progress_window['window'].destroy()
        except:
            pass

    def export_transactions_simple(self):
        """تصدير عمليات الصرف بشكل مبسط (العمليات فقط بدون تفاصيل الأصناف)"""
        try:
            # التحقق من وجود بيانات للتصدير
            if not self.transactions_data:
                messagebox.showwarning("تحذير", "لا توجد عمليات صرف للتصدير")
                return

            # اختيار مكان الحفظ
            file_path = filedialog.asksaveasfilename(
                title="حفظ ملف PDF - عمليات الصرف (مبسط)",
                defaultextension=".pdf",
                filetypes=[
                    ("PDF files", "*.pdf"),
                    ("All files", "*.*")
                ],
                initialfile=f"عمليات_الصرف_مبسط_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
            )

            if not file_path:
                return

            # عرض شريط التقدم
            progress_window = self.show_export_progress()
            
            # تحضير البيانات للتصدير
            export_data = []
            total_transactions = len(self.transactions_data)
            
            for i, transaction in enumerate(self.transactions_data):
                try:
                    # تحديث شريط التقدم
                    progress = int((i / total_transactions) * 100)
                    self.update_export_progress(progress_window, progress, f"معالجة العملية {i+1} من {total_transactions}")
                    
                    # الحصول على بيانات المستفيد
                    beneficiary_name = transaction.get('beneficiary_name', '')
                    
                    # الحصول على بيانات مدخل البيانات
                    user_name = transaction.get('user_name', '')

                    # الحصول على إحصائيات الأصناف
                    transaction_id = transaction.get('id')
                    transaction_items = TransactionItem.get_by_transaction(transaction_id) if transaction_id else []
                    total_items = len(transaction_items)
                    total_quantity = sum(item.quantity for item in transaction_items)
                    
                    # إنشاء سجل العملية
                    record = {
                        'رقم العملية': transaction.get('transaction_number') or transaction.get('id', ''),
                        'تاريخ العملية': transaction.get('transaction_date', ''),
                        'اسم المستفيد': beneficiary_name,
                        'نوع العملية': transaction.get('transaction_type', 'صرف'),
                        'حالة العملية': transaction.get('status', ''),
                        'عدد الأصناف': total_items,
                        'إجمالي الكمية': total_quantity,
                        'إجمالي القيمة': transaction.get('total_amount', 0),
                        'ملاحظات': transaction.get('notes', ''),
                        'مدخل البيانات': user_name,
                        'تاريخ الإنشاء': transaction.get('created_at', '')
                    }
                    
                    export_data.append(record)

                except Exception as e:
                    print(f"خطأ في معالجة العملية {transaction.get('id', 'غير معروف')}: {e}")
                    continue

            # إنشاء ملف PDF
            self.update_export_progress(progress_window, 95, "إنشاء ملف PDF...")
            
            # إنشاء PDF أفقي
            doc = SimpleDocTemplate(
                file_path,
                pagesize=landscape(A4),
                rightMargin=0.5*inch,
                leftMargin=0.5*inch,
                topMargin=0.5*inch,
                bottomMargin=0.5*inch
            )
            
            # إعداد الخط العربي
            try:
                # محاولة تحميل خطوط عربية متعددة
                fonts_dir = os.path.join(os.path.dirname(__file__), '..', 'assets', 'fonts')
                arial_path = os.path.join(fonts_dir, 'arial.ttf')
                tahoma_path = os.path.join(fonts_dir, 'tahoma.ttf')
                segoeui_path = os.path.join(fonts_dir, 'segoeui.ttf')

                if os.path.exists(arial_path):
                    pdfmetrics.registerFont(TTFont('Arial', arial_path))
                if os.path.exists(tahoma_path):
                    pdfmetrics.registerFont(TTFont('Tahoma', tahoma_path))
                if os.path.exists(segoeui_path):
                    pdfmetrics.registerFont(TTFont('SegoeUI', segoeui_path))

                # اختيار الخط الافتراضي
                if os.path.exists(arial_path):
                    arabic_font = 'Arial'
                elif os.path.exists(tahoma_path):
                    arabic_font = 'Tahoma'
                elif os.path.exists(segoeui_path):
                    arabic_font = 'SegoeUI'
                else:
                    arabic_font = 'Helvetica'
            except:
                arabic_font = 'Helvetica'
            
            # إعداد الأنماط مع دعم RTL
            styles = getSampleStyleSheet()
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontName=arabic_font,
                fontSize=16,
                alignment=2,  # محاذاة لليمين (Right)
                spaceAfter=20
            )
            
            # محتوى PDF
            story = []
            
            # العنوان
            title = Paragraph("تقرير عمليات الصرف المبسط", title_style)
            story.append(title)
            story.append(Spacer(1, 12))
            
            # معلومات التقرير
            info_text = f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}<br/>عدد العمليات: {len(self.transactions_data)}"
            info_para = Paragraph(info_text, styles['Normal'])
            story.append(info_para)
            story.append(Spacer(1, 20))
            
            # إعداد بيانات الجدول
            if export_data:
                # عناوين الأعمدة
                headers = [
                    'رقم العملية', 'تاريخ العملية', 'المستفيد', 'نوع العملية',
                    'عدد الأصناف', 'إجمالي الكمية', 'إجمالي القيمة', 'مدخل البيانات'
                ]
                
                # بيانات الجدول
                table_data = [headers]
                
                for row in export_data:
                    table_row = [
                        pdf_generator.process_arabic_text(str(row.get('رقم العملية', ''))),
                        pdf_generator.process_arabic_text(str(row.get('تاريخ العملية', ''))),
                        pdf_generator.process_arabic_text(str(row.get('اسم المستفيد', ''))),
                        pdf_generator.process_arabic_text(str(row.get('نوع العملية', ''))),
                        pdf_generator.process_arabic_text(str(row.get('عدد الأصناف', ''))),
                        pdf_generator.process_arabic_text(str(row.get('إجمالي الكمية', ''))),
                        pdf_generator.process_arabic_text(str(row.get('إجمالي القيمة', ''))),
                        pdf_generator.process_arabic_text(str(row.get('مدخل البيانات', '')))
                    ]
                    table_data.append(table_row)
                
                # إنشاء الجدول مع تمييز لوني للصفوف
                table = Table(table_data)
                row_colors = []
                for i, row in enumerate(table_data):
                    if i == 0:
                        # لون خلفية العنوان
                        row_colors.append(('BACKGROUND', (0, i), (-1, i), colors.grey))
                        row_colors.append(('TEXTCOLOR', (0, i), (-1, i), colors.whitesmoke))
                        row_colors.append(('FONTNAME', (0, i), (-1, i), arabic_font))
                        row_colors.append(('FONTSIZE', (0, i), (-1, i), 10))
                        row_colors.append(('BOTTOMPADDING', (0, i), (-1, i), 12))
                    else:
                        # تمييز الصفوف بالتناوب
                        bg_color = colors.beige if i % 2 == 1 else colors.whitesmoke
                        row_colors.append(('BACKGROUND', (0, i), (-1, i), bg_color))
                        row_colors.append(('FONTNAME', (0, i), (-1, i), arabic_font))
                        row_colors.append(('FONTSIZE', (0, i), (-1, i), 8))
                        
                        # تمييز لوني حسب إجمالي الكمية (أخضر إذا الكمية > 0، أحمر إذا 0)
                        try:
                            quantity = float(row[5])  # عمود إجمالي الكمية
                            if quantity > 0:
                                row_colors.append(('BACKGROUND', (0, i), (-1, i), colors.lightgreen))
                            else:
                                row_colors.append(('BACKGROUND', (0, i), (-1, i), colors.salmon))
                        except:
                            pass
                
                # إضافة حدود للجدول
                row_colors.append(('GRID', (0, 0), (-1, -1), 1, colors.black))
                row_colors.append(('ALIGN', (0, 0), (-1, -1), 'CENTER'))
                
                table.setStyle(TableStyle(row_colors))
                
                story.append(table)
            
            # بناء PDF
            doc.build(story)

            # إغلاق شريط التقدم
            self.close_export_progress(progress_window)

            # عرض رسالة النجاح
            messagebox.showinfo(
                "نجح التصدير",
                f"تم تصدير {len(export_data)} عملية صرف بنجاح إلى ملف PDF:\n{file_path}"
            )

            # فتح نافذة الطباعة تلقائياً بعد التصدير
            try:
                import webbrowser
                webbrowser.open(file_path)
            except Exception as e:
                print(f"فشل في فتح ملف الطباعة تلقائياً: {e}")

        except Exception as e:
            # إغلاق شريط التقدم في حالة الخطأ
            try:
                self.close_export_progress(progress_window)
            except:
                pass
            
            messagebox.showerror("خطأ في التصدير", f"فشل في تصدير عمليات الصرف:\n{str(e)}")
            print(f"خطأ مفصل في التصدير: {e}")

    def export_filtered_transactions(self):
        """تصدير البيانات المفلترة فقط"""
        try:
            # التحقق من وجود بيانات مفلترة للتصدير
            if not hasattr(self, 'filtered_data') or not self.filtered_data:
                # إذا لم توجد بيانات مفلترة، استخدم جميع البيانات
                if not self.transactions_data:
                    messagebox.showwarning("تحذير", "لا توجد عمليات صرف للتصدير")
                    return
                else:
                    # استخدام جميع البيانات إذا لم يتم تطبيق فلاتر
                    self.filtered_data = self.transactions_data

            # استخدام البيانات المفلترة بدلاً من جميع البيانات
            original_data = self.transactions_data
            self.transactions_data = self.filtered_data
            
            # عرض خيارات التصدير
            choice = messagebox.askyesnocancel(
                "نوع التصدير",
                "اختر نوع التصدير للبيانات المفلترة:\n\n"
                "نعم = تصدير مفصل (مع الأصناف)\n"
                "لا = تصدير مبسط (العمليات فقط)\n"
                "إلغاء = إلغاء العملية"
            )
            
            if choice is None:  # إلغاء
                return
            elif choice:  # تصدير مفصل
                self.export_transactions()
            else:  # تصدير مبسط
                self.export_transactions_simple()
            
            # استعادة البيانات الأصلية
            self.transactions_data = original_data
            
        except Exception as e:
            # استعادة البيانات الأصلية في حالة الخطأ
            if 'original_data' in locals():
                self.transactions_data = original_data
            messagebox.showerror("خطأ", f"فشل في تصدير البيانات المفلترة: {e}")

    def setup_shortcuts(self):
        """إعداد مفاتيح الاختصار العامة"""
        try:
            # إنشاء معالج السياق
            self.context_handler = ContextHandler()
            
            # تعيين دوال العمليات (يمكن تخصيصها حسب النافذة)
            self.context_handler.set_save_callback(self.shortcut_save)
            self.context_handler.set_delete_callback(self.shortcut_delete)
            self.context_handler.set_copy_callback(self.shortcut_copy)
            self.context_handler.set_paste_callback(self.shortcut_paste)
            
            # إضافة اختصار للتصدير (Ctrl+E)
            window = getattr(self, 'transactions_window', None) or getattr(self, 'parent', None)
            if window:
                window.bind('<Control-e>', lambda e: self.export_transactions_simple())
                window.bind('<Control-E>', lambda e: self.export_transactions_simple())
            
            # تفعيل مفاتيح الاختصار
            window = getattr(self, 'window', None) or getattr(self, 'parent', None)
            if window:
                self.global_shortcuts = GlobalShortcuts(window, self.context_handler)
        except Exception as e:
            print(f"خطأ في إعداد مفاتيح الاختصار: {e}")
    
    def shortcut_save(self):
        """عملية الحفظ (F1)"""
        try:
            if hasattr(self, 'save_data'):
                self.save_data()
            elif hasattr(self, 'save_changes'):
                self.save_changes()
            elif hasattr(self, 'save'):
                self.save()
            else:
                print("عملية الحفظ غير متاحة")
        except Exception as e:
            print(f"خطأ في عملية الحفظ: {e}")
    
    def shortcut_delete(self):
        """عملية الحذف (F2)"""
        try:
            if hasattr(self, 'delete_selected'):
                self.delete_selected()
            elif hasattr(self, 'delete_item'):
                self.delete_item()
            elif hasattr(self, 'delete'):
                self.delete()
            else:
                print("عملية الحذف غير متاحة")
        except Exception as e:
            print(f"خطأ في عملية الحذف: {e}")
    
    def shortcut_copy(self):
        """عملية النسخ (F3)"""
        try:
            if hasattr(self, 'copy_data'):
                self.copy_data()
            else:
                # نسخ عامة للبيانات المحددة
                import pyperclip
                pyperclip.copy("تم النسخ من النافذة")
                print("تم نسخ البيانات")
        except Exception as e:
            print(f"خطأ في عملية النسخ: {e}")
    
    def shortcut_paste(self):
        """عملية اللصق (F4)"""
        try:
            if hasattr(self, 'paste_data'):
                self.paste_data()
            else:
                import pyperclip
                clipboard_text = pyperclip.paste()
                if clipboard_text:
                    print(f"تم لصق: {clipboard_text[:50]}")
                else:
                    print("لا توجد بيانات في الحافظة")
        except Exception as e:
            print(f"خطأ في عملية اللصق: {e}")

# اختبار النافذة
if __name__ == "__main__":
    root = tk.Tk()
    root.withdraw()  # إخفاء النافذة الرئيسية
    
    # إنشاء نافذة الاختبار
    window = TransactionsWindow(root, None)
    root.mainloop()
