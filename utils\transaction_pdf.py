"""
مولد PDF لإذن الصرف - تطبيق إدارة المخازن
Transaction PDF Generator - Desktop Stores Management System
"""

from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch, cm
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
from reportlab.lib.enums import TA_CENTER, TA_RIGHT, TA_LEFT
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from datetime import datetime
import os
import tempfile
import subprocess
from tkinter import messagebox

class TransactionPDFGenerator:
    """مولد PDF لإذن الصرف"""
    
    def __init__(self):
        self.setup_arabic_fonts()
        self.setup_styles()
    
    def setup_arabic_fonts(self):
        """إعداد الخطوط العربية"""
        try:
            # محاولة استخدام خط Arial Unicode MS إذا كان متوفراً
            import platform
            system = platform.system()

            if system == "Windows":
                # مسارات الخطوط في Windows - إضافة المزيد من الخيارات
                font_paths = [
                    "C:/Windows/Fonts/arial.ttf",
                    "C:/Windows/Fonts/tahoma.ttf",
                    "C:/Windows/Fonts/calibri.ttf",
                    "C:/Windows/Fonts/times.ttf",
                    "C:/Windows/Fonts/verdana.ttf"
                ]

                # محاولة تسجيل خط عريض أيضاً
                bold_font_paths = [
                    "C:/Windows/Fonts/arialbd.ttf",
                    "C:/Windows/Fonts/tahomabd.ttf",
                    "C:/Windows/Fonts/calibrib.ttf",
                    "C:/Windows/Fonts/timesbd.ttf",
                    "C:/Windows/Fonts/verdanab.ttf"
                ]
            else:
                # مسارات الخطوط في Linux/Mac
                font_paths = [
                    "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
                    "/System/Library/Fonts/Arial.ttf",
                    "/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf"
                ]
                bold_font_paths = [
                    "/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf",
                    "/System/Library/Fonts/Arial Bold.ttf",
                    "/usr/share/fonts/truetype/liberation/LiberationSans-Bold.ttf"
                ]

            # محاولة تسجيل خط عادي يدعم العربية
            font_registered = False
            for font_path in font_paths:
                if os.path.exists(font_path):
                    try:
                        pdfmetrics.registerFont(TTFont('Arabic', font_path))
                        print(f"✅ تم تسجيل الخط العربي: {font_path}")
                        font_registered = True
                        break
                    except Exception as e:
                        continue

            # محاولة تسجيل خط عريض يدعم العربية
            if system == "Windows":
                for bold_font_path in bold_font_paths:
                    if os.path.exists(bold_font_path):
                        try:
                            pdfmetrics.registerFont(TTFont('ArabicBold', bold_font_path))
                            print(f"✅ تم تسجيل الخط العربي العريض: {bold_font_path}")
                            break
                        except Exception as e:
                            continue

            if not font_registered:
                print("⚠️ لم يتم العثور على خط يدعم العربية، سيتم استخدام الخط الافتراضي")

        except Exception as e:
            print(f"تحذير: خطأ في إعداد الخطوط العربية: {e}")
    
    def setup_styles(self):
        """إعداد أنماط النص"""
        self.styles = getSampleStyleSheet()

        # تحديد الخط المناسب
        try:
            # محاولة استخدام الخط العربي المسجل
            font_name = 'Arabic'
            pdfmetrics.getFont(font_name)

            # محاولة استخدام الخط العريض
            try:
                font_name_bold = 'ArabicBold'
                pdfmetrics.getFont(font_name_bold)
            except:
                font_name_bold = 'Arabic'  # استخدام نفس الخط إذا لم يتوفر العريض

        except:
            # استخدام الخط الافتراضي
            font_name = 'Helvetica'
            font_name_bold = 'Helvetica-Bold'

        # نمط العنوان الرئيسي
        self.styles.add(ParagraphStyle(
            name='ArabicTitle',
            parent=self.styles['Title'],
            fontName=font_name_bold,
            fontSize=18,
            alignment=TA_CENTER,
            spaceAfter=20,
            textColor=colors.black
        ))

        # نمط العنوان الفرعي
        self.styles.add(ParagraphStyle(
            name='ArabicHeading',
            parent=self.styles['Heading1'],
            fontName=font_name_bold,
            fontSize=14,
            alignment=TA_CENTER,
            spaceAfter=12,
            textColor=colors.black
        ))

        # نمط النص العادي
        self.styles.add(ParagraphStyle(
            name='ArabicNormal',
            parent=self.styles['Normal'],
            fontName=font_name,
            fontSize=12,
            alignment=TA_CENTER,
            spaceAfter=6,
            textColor=colors.black
        ))

        # نمط النص المتوسط
        self.styles.add(ParagraphStyle(
            name='ArabicCenter',
            parent=self.styles['Normal'],
            fontName=font_name,
            fontSize=12,
            alignment=TA_CENTER,
            spaceAfter=6,
            textColor=colors.black
        ))

    def process_arabic_text(self, text):
        """معالجة النص العربي لضمان العرض الصحيح"""
        try:
            # تنظيف النص وضمان التشفير الصحيح
            if isinstance(text, bytes):
                text = text.decode('utf-8')

            # محاولة استخدام arabic_reshaper و bidi لتحسين النص العربي
            try:
                import arabic_reshaper
                from bidi.algorithm import get_display

                # إعادة تشكيل النص العربي
                reshaped_text = arabic_reshaper.reshape(text)
                # تطبيق خوارزمية bidi للعرض الصحيح
                bidi_text = get_display(reshaped_text)
                return bidi_text

            except ImportError:
                # إذا لم تكن المكتبات متوفرة، استخدم النص كما هو
                print("تحذير: مكتبات arabic_reshaper أو python-bidi غير متوفرة")
                return text
            except Exception as e:
                print(f"تحذير: خطأ في معالجة النص العربي: {e}")
                return text

        except Exception as e:
            print(f"خطأ في معالجة النص العربي: {e}")
            return text

    def extract_transaction_data_from_content(self, content):
        """استخراج بيانات العملية من المحتوى"""
        try:
            transaction_data = {
                'transaction_number': '',
                'transaction_date': '',
                'beneficiary_name': '',
                'beneficiary_rank': '',
                'beneficiary_unit': '',
                'beneficiary_department': '',
                'receiver_name': '',
                'receiver_rank': '',
                'receiver_unit': '',
                'receiver_department': '',
                'items': []
            }

            lines = content.split('\n')
            current_section = None

            for line in lines:
                line = line.strip()
                if 'إذن صرف رقم:' in line:
                    # استخراج رقم العملية
                    parts = line.split('إذن صرف رقم:')
                    if len(parts) > 1:
                        transaction_data['transaction_number'] = parts[1].strip()
                elif 'تاريخ العملية:' in line:
                    # استخراج تاريخ العملية
                    parts = line.split('تاريخ العملية:')
                    if len(parts) > 1:
                        transaction_data['transaction_date'] = parts[1].strip()
                elif 'المستفيد:' in line:
                    # استخراج اسم المستفيد
                    parts = line.split('المستفيد:')
                    if len(parts) > 1:
                        transaction_data['beneficiary_name'] = parts[1].strip()
                elif 'الرتبة:' in line and 'المستلم' not in line:
                    # استخراج رتبة المستفيد
                    parts = line.split('الرتبة:')
                    if len(parts) > 1:
                        transaction_data['beneficiary_rank'] = parts[1].strip()
                elif 'الوحدة:' in line and 'المستلم' not in line:
                    # استخراج وحدة المستفيد
                    parts = line.split('الوحدة:')
                    if len(parts) > 1:
                        unit_value = parts[1].strip()
                        # تجاهل القيم الافتراضية
                        if unit_value and unit_value != 'غير محدد':
                            transaction_data['beneficiary_unit'] = unit_value
                elif 'الإدارة:' in line and 'المستلم' not in line:
                    # استخراج إدارة المستفيد - قد تحتوي على وحدة وإدارة
                    parts = line.split('الإدارة:')
                    if len(parts) > 1:
                        dept_info = parts[1].strip()
                        if ' - ' in dept_info:
                            # تقسيم الوحدة والإدارة
                            dept_parts = dept_info.split(' - ')
                            # إذا لم تكن الوحدة محددة من قبل أو كانت "غير محدد"
                            if not transaction_data['beneficiary_unit'] or transaction_data['beneficiary_unit'] == 'غير محدد':
                                transaction_data['beneficiary_unit'] = dept_parts[0].strip()
                            if len(dept_parts) > 1:
                                transaction_data['beneficiary_department'] = dept_parts[1].strip()
                        else:
                            transaction_data['beneficiary_department'] = dept_info
                elif 'المندوب المستلم:' in line:
                    # استخراج اسم المندوب المستلم
                    parts = line.split('المندوب المستلم:')
                    if len(parts) > 1:
                        transaction_data['receiver_name'] = parts[1].strip()
                elif 'رتبة المستلم:' in line:
                    # استخراج رتبة المستلم
                    parts = line.split('رتبة المستلم:')
                    if len(parts) > 1:
                        transaction_data['receiver_rank'] = parts[1].strip()
                elif 'إدارة/قسم المستلم:' in line:
                    # استخراج إدارة/قسم المستلم
                    parts = line.split('إدارة/قسم المستلم:')
                    if len(parts) > 1:
                        # تقسيم الإدارة والوحدة
                        dept_info = parts[1].strip()
                        if ' - ' in dept_info:
                            dept_parts = dept_info.split(' - ')
                            transaction_data['receiver_unit'] = dept_parts[0].strip()
                            transaction_data['receiver_department'] = dept_parts[1].strip()
                        else:
                            transaction_data['receiver_department'] = dept_info
                elif 'الأصناف المصروفة' in line:
                    current_section = 'items'
                elif current_section == 'items' and line.startswith(('1.', '2.', '3.', '4.', '5.', '6.', '7.', '8.', '9.')):
                    # استخراج بيانات الصنف
                    item_parts = line.split('-')
                    if len(item_parts) >= 2:
                        item_name = item_parts[0].split('.', 1)[1].strip() if '.' in item_parts[0] else item_parts[0].strip()
                        quantity_part = item_parts[1].strip() if len(item_parts) > 1 else ''

                        # محاولة استخراج الكمية من النص
                        quantity = ''
                        if 'الكمية' in quantity_part:
                            qty_parts = quantity_part.split('الكمية')
                            if len(qty_parts) > 1:
                                quantity = qty_parts[1].strip().replace(':', '').strip()

                        transaction_data['items'].append({
                            'item_name': item_name,
                            'quantity': quantity,
                            'notes': ''
                        })

            return transaction_data
        except Exception as e:
            print(f"خطأ في استخراج بيانات العملية: {e}")
            return {}

    def create_official_header(self, transaction_data=None):
        """إنشاء الترويسة الرسمية محسنة للسندات السعودية"""
        try:
            # الحصول على البيانات من العملية
            transaction_number = ""
            transaction_date = ""

            if transaction_data:
                transaction_number = transaction_data.get('transaction_number', '')
                transaction_date = transaction_data.get('transaction_date', '')

            # تحديد الخط المناسب
            try:
                font_name = 'Arabic'
                pdfmetrics.getFont(font_name)
            except:
                font_name = 'Helvetica'

            # إنشاء الشعار
            logo_cell = ""
            logo_path = os.path.join(os.path.dirname(__file__), '..', 'assets', 'logo.png')
            if os.path.exists(logo_path):
                try:
                    logo_cell = Image(logo_path, width=2.5*cm, height=2.5*cm)
                except:
                    logo_cell = Paragraph("🇸🇦",
                                        ParagraphStyle(name='LogoStyle', fontName=font_name,
                                                     fontSize=24, alignment=TA_CENTER))
            else:
                logo_cell = Paragraph("🇸🇦",
                                    ParagraphStyle(name='LogoStyle', fontName=font_name,
                                                 fontSize=24, alignment=TA_CENTER))

            # إنشاء جدول للترويسة مع 3 أعمدة و 5 صفوف
            header_data = []

            # الصف الأول - معلومات السند
            row1 = [
                Paragraph(self.process_arabic_text(f"الرقم: {transaction_number}"),
                         ParagraphStyle(name='LeftCell', fontName=font_name, fontSize=10, alignment=TA_LEFT)),
                "",  # خلية فارغة للشعار
                Paragraph(self.process_arabic_text("المملكة العربية السعودية"),
                         ParagraphStyle(name='RightCell', fontName=font_name, fontSize=12, alignment=TA_RIGHT, textColor=colors.black))
            ]

            # الصف الثاني - التاريخ والشعار
            row2 = [
                Paragraph(self.process_arabic_text(f"التاريخ: {transaction_date}"),
                         ParagraphStyle(name='LeftCell', fontName=font_name, fontSize=10, alignment=TA_LEFT)),
                logo_cell,  # الشعار
                Paragraph(self.process_arabic_text("وزارة الدفاع"),
                         ParagraphStyle(name='RightCell', fontName=font_name, fontSize=12, alignment=TA_RIGHT, textColor=colors.black))
            ]

            # الصف الثالث - المرفقات
            row3 = [
                Paragraph(self.process_arabic_text("المرفقات: لا يوجد"),
                         ParagraphStyle(name='LeftCell', fontName=font_name, fontSize=10, alignment=TA_LEFT)),
                "",  # خلية فارغة للشعار (مدموجة)
                Paragraph(self.process_arabic_text("رئاسة هيئة الأركان العامة"),
                         ParagraphStyle(name='RightCell', fontName=font_name, fontSize=11, alignment=TA_RIGHT))
            ]

            # الصف الرابع - الموضوع
            row4 = [
                Paragraph(self.process_arabic_text("الموضوع: سند صرف"),
                         ParagraphStyle(name='LeftCell', fontName=font_name, fontSize=10, alignment=TA_LEFT)),
                "",  # خلية فارغة للشعار
                Paragraph(self.process_arabic_text("قوات الدفاع الجوي الملكي السعودي"),
                         ParagraphStyle(name='RightCell', fontName=font_name, fontSize=11, alignment=TA_RIGHT))
            ]

            # الصف الخامس - خط فاصل
            row5 = [
                Paragraph("", ParagraphStyle(name='Separator', fontSize=1)),
                Paragraph("", ParagraphStyle(name='Separator', fontSize=1)),
                Paragraph("", ParagraphStyle(name='Separator', fontSize=1))
            ]

            header_data = [row1, row2, row3, row4, row5]

            # إنشاء الجدول
            header_table = Table(
                header_data,
                colWidths=[6*cm, 4*cm, 6*cm],  # توزيع المساحة
                rowHeights=[0.8*cm, 1.8*cm, 0.8*cm, 0.8*cm, 0.3*cm]  # ارتفاعات مختلفة للصفوف
            )

            # تنسيق الجدول
            header_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (0, -1), 'LEFT'),      # العمود الأيسر
                ('ALIGN', (1, 0), (1, -1), 'CENTER'),    # العمود الأوسط (الشعار)
                ('ALIGN', (2, 0), (2, -1), 'RIGHT'),     # العمود الأيمن
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),  # محاذاة وسطية
                ('SPAN', (1, 1), (1, 3)),                # دمج خلايا الشعار (الصف 2 إلى 4)
                ('FONTNAME', (0, 0), (-1, -1), font_name),
                ('LEFTPADDING', (0, 0), (-1, -1), 8),
                ('RIGHTPADDING', (0, 0), (-1, -1), 8),
                ('TOPPADDING', (0, 0), (-1, -1), 4),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 4),
                # خط سفلي للترويسة
                ('LINEBELOW', (0, 3), (-1, 3), 1, colors.black),
                ('LINEBELOW', (0, 4), (-1, 4), 2, colors.black),
            ]))

            return header_table

        except Exception as e:
            print(f"خطأ في إنشاء الترويسة: {e}")
            # إرجاع ترويسة بسيطة في حالة الخطأ
            return Paragraph("المملكة العربية السعودية - وزارة الدفاع", self.styles['ArabicTitle'])

    def create_items_table(self, transaction_data):
        """إنشاء جدول الأصناف المصروفة محسن"""
        try:
            # تحديد الخط المناسب
            try:
                font_name = 'Arabic'
                pdfmetrics.getFont(font_name)
            except:
                font_name = 'Helvetica'

            # رؤوس الجدول بترتيب منطقي: ت، الصنف، الكمية، الوحدة، ملاحظات
            headers = ['ت', 'الصنف', 'الكمية', 'الوحدة', 'ملاحظات']

            # بيانات الجدول
            table_data = [headers]

            # إضافة بيانات الأصناف (إذا كانت متوفرة)
            items = transaction_data.get('items', [])
            if items:
                for i, item in enumerate(items, 1):
                    row = [
                        str(i),  # ت
                        item.get('item_name', ''),  # الصنف
                        str(item.get('quantity', '')),  # الكمية
                        'قطعة',  # الوحدة (افتراضية)
                        item.get('notes', ''),  # ملاحظات
                    ]
                    table_data.append(row)
            
            # إضافة صفوف فارغة للتعبئة اليدوية (حتى 15 صف)
            current_rows = len(table_data) - 1  # عدد الصفوف الحالية (بدون الرأس)
            for i in range(current_rows + 1, 16):  # حتى 15 صف
                table_data.append([str(i), '', '', '', ''])

            # تحويل البيانات إلى فقرات
            processed_data = []
            for row_idx, row in enumerate(table_data):
                processed_row = []
                for col_idx, cell in enumerate(row):
                    if row_idx == 0:  # رؤوس الجدول
                        para = Paragraph(
                            self.process_arabic_text(str(cell)),
                            ParagraphStyle(
                                name='TableHeader',
                                fontName=font_name,
                                fontSize=11,
                                alignment=TA_CENTER,
                                textColor=colors.white,
                                leading=14
                            )
                        )
                    else:  # بيانات الجدول
                        # تحديد المحاذاة حسب العمود
                        if col_idx == 0:  # عمود الترقيم
                            alignment = TA_CENTER
                        elif col_idx == 1:  # عمود الصنف
                            alignment = TA_RIGHT
                        elif col_idx == 2 or col_idx == 3:  # عمود الكمية والوحدة
                            alignment = TA_CENTER
                        else:  # عمود الملاحظات
                            alignment = TA_RIGHT
                            
                        para = Paragraph(
                            self.process_arabic_text(str(cell)),
                            ParagraphStyle(
                                name='TableCell',
                                fontName=font_name,
                                fontSize=10,
                                alignment=alignment,
                                textColor=colors.black,
                                leading=12
                            )
                        )
                    processed_row.append(para)
                processed_data.append(processed_row)

            # إنشاء الجدول: ت، الصنف، الكمية، الوحدة، ملاحظات
            items_table = Table(
                processed_data,
                colWidths=[1*cm, 6*cm, 2*cm, 2*cm, 4*cm],  # عرض الأعمدة
                rowHeights=[0.8*cm] * len(processed_data)  # ارتفاع موحد للصفوف
            )

            # تنسيق الجدول
            items_table.setStyle(TableStyle([
                # تنسيق رؤوس الجدول
                ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
                ('FONTNAME', (0, 0), (-1, 0), font_name),
                ('FONTSIZE', (0, 0), (-1, 0), 11),
                ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),

                # تنسيق بيانات الجدول
                ('FONTNAME', (0, 1), (-1, -1), font_name),
                ('FONTSIZE', (0, 1), (-1, -1), 10),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.black),
                
                # تلوين الصفوف بالتناوب
                ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.lightgrey]),

                # المساحات الداخلية
                ('LEFTPADDING', (0, 0), (-1, -1), 4),
                ('RIGHTPADDING', (0, 0), (-1, -1), 4),
                ('TOPPADDING', (0, 0), (-1, -1), 6),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
                
                # خطوط أكثر وضوحاً للرأس
                ('LINEBELOW', (0, 0), (-1, 0), 2, colors.darkblue),
            ]))

            return items_table

        except Exception as e:
            print(f"خطأ في إنشاء جدول الأصناف: {e}")
            return None

    def create_signature_section(self, transaction_data):
        """إنشاء قسم التوقيعات المحسن للسندات الرسمية"""
        try:
            # تحديد الخط المناسب
            try:
                font_name = 'Arabic'
                pdfmetrics.getFont(font_name)
            except:
                font_name = 'Helvetica'

            # إنشاء جدول التوقيعات مع 3 أعمدة
            signature_data = []
            
            # الصف الأول - العناوين
            row1 = [
                Paragraph(self.process_arabic_text("المستفيد"),
                         ParagraphStyle(name='SigTitle', fontName=font_name, fontSize=12, 
                                      alignment=TA_CENTER, textColor=colors.black, leading=14)),
                Paragraph(self.process_arabic_text("المندوب المستلم"),
                         ParagraphStyle(name='SigTitle', fontName=font_name, fontSize=12, 
                                      alignment=TA_CENTER, textColor=colors.black, leading=14)),
                Paragraph(self.process_arabic_text("أمين المخزن"),
                         ParagraphStyle(name='SigTitle', fontName=font_name, fontSize=12, 
                                      alignment=TA_CENTER, textColor=colors.black, leading=14))
            ]
            
            # الصف الثاني - الأسماء
            beneficiary_name = transaction_data.get('beneficiary_name', '')
            receiver_name = transaction_data.get('receiver_name', '')
            
            row2 = [
                Paragraph(self.process_arabic_text(f"الاسم: {beneficiary_name if beneficiary_name else '............................'}"),
                         ParagraphStyle(name='SigData', fontName=font_name, fontSize=10, 
                                      alignment=TA_RIGHT, textColor=colors.black, leading=14)),
                Paragraph(self.process_arabic_text(f"الاسم: {receiver_name if receiver_name else '............................'}"),
                         ParagraphStyle(name='SigData', fontName=font_name, fontSize=10, 
                                      alignment=TA_RIGHT, textColor=colors.black, leading=14)),
                Paragraph(self.process_arabic_text("الاسم: ............................"),
                         ParagraphStyle(name='SigData', fontName=font_name, fontSize=10, 
                                      alignment=TA_RIGHT, textColor=colors.black, leading=14))
            ]
            
            # الصف الثالث - الرتب
            beneficiary_rank = transaction_data.get('beneficiary_rank', '')
            receiver_rank = transaction_data.get('receiver_rank', '')
            
            row3 = [
                Paragraph(self.process_arabic_text(f"الرتبة: {beneficiary_rank if beneficiary_rank else '............................'}"),
                         ParagraphStyle(name='SigData', fontName=font_name, fontSize=10, 
                                      alignment=TA_RIGHT, textColor=colors.black, leading=14)),
                Paragraph(self.process_arabic_text(f"الرتبة: {receiver_rank if receiver_rank else '............................'}"),
                         ParagraphStyle(name='SigData', fontName=font_name, fontSize=10, 
                                      alignment=TA_RIGHT, textColor=colors.black, leading=14)),
                Paragraph(self.process_arabic_text("الرتبة: ............................"),
                         ParagraphStyle(name='SigData', fontName=font_name, fontSize=10, 
                                      alignment=TA_RIGHT, textColor=colors.black, leading=14))
            ]
            
            # الصف الرابع - الوحدات
            row4 = [
                Paragraph(self.process_arabic_text("الوحدة: ............................"),
                         ParagraphStyle(name='SigData', fontName=font_name, fontSize=10, 
                                      alignment=TA_RIGHT, textColor=colors.black, leading=14)),
                Paragraph(self.process_arabic_text("الوحدة: ............................"),
                         ParagraphStyle(name='SigData', fontName=font_name, fontSize=10, 
                                      alignment=TA_RIGHT, textColor=colors.black, leading=14)),
                Paragraph(self.process_arabic_text("الوحدة: ............................"),
                         ParagraphStyle(name='SigData', fontName=font_name, fontSize=10, 
                                      alignment=TA_RIGHT, textColor=colors.black, leading=14))
            ]
            
            # الصف الخامس - التوقيعات
            row5 = [
                Paragraph(self.process_arabic_text("التوقيع: ............................"),
                         ParagraphStyle(name='SigData', fontName=font_name, fontSize=10, 
                                      alignment=TA_RIGHT, textColor=colors.black, leading=14)),
                Paragraph(self.process_arabic_text("التوقيع: ............................"),
                         ParagraphStyle(name='SigData', fontName=font_name, fontSize=10, 
                                      alignment=TA_RIGHT, textColor=colors.black, leading=14)),
                Paragraph(self.process_arabic_text("التوقيع: ............................"),
                         ParagraphStyle(name='SigData', fontName=font_name, fontSize=10, 
                                      alignment=TA_RIGHT, textColor=colors.black, leading=14))
            ]
            
            # الصف السادس - التاريخ
            row6 = [
                Paragraph(self.process_arabic_text("التاريخ: ............................"),
                         ParagraphStyle(name='SigData', fontName=font_name, fontSize=10, 
                                      alignment=TA_RIGHT, textColor=colors.black, leading=14)),
                Paragraph(self.process_arabic_text("التاريخ: ............................"),
                         ParagraphStyle(name='SigData', fontName=font_name, fontSize=10, 
                                      alignment=TA_RIGHT, textColor=colors.black, leading=14)),
                Paragraph(self.process_arabic_text("التاريخ: ............................"),
                         ParagraphStyle(name='SigData', fontName=font_name, fontSize=10, 
                                      alignment=TA_RIGHT, textColor=colors.black, leading=14))
            ]

            signature_data = [row1, row2, row3, row4, row5, row6]

            signature_table = Table(
                signature_data,
                colWidths=[5.3*cm, 5.3*cm, 5.3*cm],  # ثلاثة أعمدة متساوية
                rowHeights=[1*cm, 0.8*cm, 0.8*cm, 0.8*cm, 1.2*cm, 0.8*cm]  # ارتفاعات مختلفة
            )

            # تنسيق جدول التوقيعات
            signature_table.setStyle(TableStyle([
                # تنسيق العناوين (الصف الأول)
                ('BACKGROUND', (0, 0), (-1, 0), colors.lightblue),
                ('FONTNAME', (0, 0), (-1, 0), font_name),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                
                # تنسيق البيانات
                ('FONTNAME', (0, 1), (-1, -1), font_name),
                ('FONTSIZE', (0, 1), (-1, -1), 10),
                ('ALIGN', (0, 1), (-1, -1), 'RIGHT'),
                
                # الحدود والخطوط
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('LINEBELOW', (0, 0), (-1, 0), 2, colors.darkblue),
                
                # المساحات الداخلية
                ('LEFTPADDING', (0, 0), (-1, -1), 8),
                ('RIGHTPADDING', (0, 0), (-1, -1), 8),
                ('TOPPADDING', (0, 0), (-1, -1), 6),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
                
                # مساحة إضافية لصف التوقيع
                ('TOPPADDING', (0, 4), (-1, 4), 15),
                ('BOTTOMPADDING', (0, 4), (-1, 4), 15),
            ]))

            return signature_table

        except Exception as e:
            print(f"خطأ في إنشاء قسم التوقيعات: {e}")
            return None
    def create_transaction_pdf(self, content, filename):
        """إنشاء ملف PDF لإذن الصرف بالتخطيط الجديد"""
        try:
            # إنشاء ملف مؤقت
            temp_file = tempfile.NamedTemporaryFile(suffix='.pdf', delete=False)
            temp_file.close()

            # إنشاء المستند
            doc = SimpleDocTemplate(
                temp_file.name,
                pagesize=A4,
                rightMargin=2*cm,
                leftMargin=2*cm,
                topMargin=4*cm,
                bottomMargin=2*cm
            )

            # تحليل المحتوى وإنشاء العناصر
            story = []

            # استخراج بيانات العملية من المحتوى
            transaction_data = self.extract_transaction_data_from_content(content)

            # 1. إضافة الترويسة الرسمية
            header = self.create_official_header(transaction_data)
            story.append(header)
            story.append(Spacer(1, 20))

            # 2. إضافة عنوان إذن الصرف المحسن
            transaction_number = transaction_data.get('transaction_number', '')
            title_text = f"إذن صرف رقم: {transaction_number}"

            try:
                font_name = 'Arabic'
                pdfmetrics.getFont(font_name)
            except:
                font_name = 'Helvetica'

            # إنشاء صندوق للعنوان
            title_table_data = [[
                Paragraph(
                    self.process_arabic_text(title_text),
                    ParagraphStyle(
                        name='MainTitle',
                        fontName=font_name,
                        fontSize=16,
                        alignment=TA_CENTER,
                        textColor=colors.white,
                        leading=20
                    )
                )
            ]]
            
            title_table = Table(title_table_data, colWidths=[16*cm])
            title_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, -1), colors.darkblue),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('TOPPADDING', (0, 0), (-1, -1), 12),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
                ('LEFTPADDING', (0, 0), (-1, -1), 10),
                ('RIGHTPADDING', (0, 0), (-1, -1), 10),
            ]))
            
            story.append(title_table)
            story.append(Spacer(1, 15))

            # 3. إضافة معلومات العملية
            info_data = []
            transaction_date = transaction_data.get('transaction_date', '')
            beneficiary_name = transaction_data.get('beneficiary_name', '')
            
            if transaction_date or beneficiary_name:
                info_row = []
                if transaction_date:
                    info_row.append(Paragraph(
                        self.process_arabic_text(f"تاريخ العملية: {transaction_date}"),
                        ParagraphStyle(name='InfoStyle', fontName=font_name, fontSize=11, 
                                     alignment=TA_RIGHT, textColor=colors.black)
                    ))
                else:
                    info_row.append("")
                    
                if beneficiary_name:
                    info_row.append(Paragraph(
                        self.process_arabic_text(f"المستفيد: {beneficiary_name}"),
                        ParagraphStyle(name='InfoStyle', fontName=font_name, fontSize=11, 
                                     alignment=TA_LEFT, textColor=colors.black)
                    ))
                else:
                    info_row.append("")
                    
                info_data.append(info_row)
                
                info_table = Table(info_data, colWidths=[8*cm, 8*cm])
                info_table.setStyle(TableStyle([
                    ('ALIGN', (0, 0), (0, -1), 'RIGHT'),
                    ('ALIGN', (1, 0), (1, -1), 'LEFT'),
                    ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                    ('TOPPADDING', (0, 0), (-1, -1), 8),
                    ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
                ]))
                story.append(info_table)
                story.append(Spacer(1, 10))

            # 4. إضافة عنوان الأصناف المصروفة
            items_title_data = [[
                Paragraph(
                    self.process_arabic_text("الأصناف المصروفة"),
                    ParagraphStyle(
                        name='ItemsTitle',
                        fontName=font_name,
                        fontSize=14,
                        alignment=TA_CENTER,
                        textColor=colors.white,
                        leading=16
                    )
                )
            ]]
            
            items_title_table = Table(items_title_data, colWidths=[16*cm])
            items_title_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, -1), colors.darkgreen),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('TOPPADDING', (0, 0), (-1, -1), 8),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
            ]))
            
            story.append(items_title_table)
            story.append(Spacer(1, 5))

            # 5. إضافة جدول الأصناف
            items_table = self.create_items_table(transaction_data)
            if items_table:
                story.append(items_table)
            story.append(Spacer(1, 20))

            # 6. إضافة ملاحظات إضافية إذا وجدت
            notes = transaction_data.get('notes', '')
            if notes and notes.strip():
                notes_title_data = [[
                    Paragraph(
                        self.process_arabic_text("ملاحظات"),
                        ParagraphStyle(
                            name='NotesTitle',
                            fontName=font_name,
                            fontSize=12,
                            alignment=TA_CENTER,
                            textColor=colors.white,
                            leading=14
                        )
                    )
                ]]
                
                notes_title_table = Table(notes_title_data, colWidths=[16*cm])
                notes_title_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, -1), colors.darkorange),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                    ('TOPPADDING', (0, 0), (-1, -1), 6),
                    ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
                ]))
                
                story.append(notes_title_table)
                
                notes_content = Paragraph(
                    self.process_arabic_text(notes),
                    ParagraphStyle(
                        name='NotesContent',
                        fontName=font_name,
                        fontSize=11,
                        alignment=TA_RIGHT,
                        textColor=colors.black,
                        leading=14,
                        leftIndent=10,
                        rightIndent=10,
                        spaceAfter=10
                    )
                )
                story.append(notes_content)
                story.append(Spacer(1, 15))

            # 7. إضافة قسم التوقيعات في الأسفل
            signature_section = self.create_signature_section(transaction_data)
            if signature_section:
                story.append(signature_section)
                
            # 8. إضافة تذييل السند
            footer_data = [[
                Paragraph(
                    self.process_arabic_text(f"تم إنشاء هذا السند بتاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"),
                    ParagraphStyle(
                        name='Footer',
                        fontName=font_name,
                        fontSize=8,
                        alignment=TA_CENTER,
                        textColor=colors.grey,
                        leading=10
                    )
                )
            ]]
            
            footer_table = Table(footer_data, colWidths=[16*cm])
            footer_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('TOPPADDING', (0, 0), (-1, -1), 10),
                ('LINEABOVE', (0, 0), (-1, -1), 0.5, colors.grey),
            ]))
            
            story.append(Spacer(1, 20))
            story.append(footer_table)

            # بناء المستند
            doc.build(story)

            return True, temp_file.name

        except Exception as e:
            print(f"خطأ في إنشاء PDF: {e}")
            return False, str(e)

    def save_as_pdf(self, content, suggested_filename):
        """حفظ المحتوى كملف PDF"""
        try:
            from tkinter import filedialog
            
            # اختيار مكان الحفظ
            file_path = filedialog.asksaveasfilename(
                title="حفظ إذن الصرف كـ PDF",
                defaultextension=".pdf",
                filetypes=[
                    ("ملفات PDF", "*.pdf"),
                    ("جميع الملفات", "*.*")
                ],
                initialfile=f"{suggested_filename}.pdf"
            )
            
            if not file_path:
                return False, "تم إلغاء العملية"
            
            # إنشاء PDF
            success, temp_path = self.create_transaction_pdf(content, suggested_filename)
            
            if success:
                # نسخ الملف المؤقت إلى المكان المحدد
                import shutil
                shutil.copy2(temp_path, file_path)
                
                # حذف الملف المؤقت
                try:
                    os.unlink(temp_path)
                except:
                    pass
                
                return True, file_path
            else:
                return False, temp_path
                
        except Exception as e:
            return False, f"فشل في حفظ الملف: {e}"
    
    def print_pdf(self, content, suggested_filename):
        """طباعة المحتوى كـ PDF"""
        try:
            # إنشاء PDF مؤقت
            success, temp_path = self.create_transaction_pdf(content, suggested_filename)

            if not success:
                return False, temp_path

            # فتح الملف للطباعة
            try:
                if os.name == 'nt':  # Windows
                    # محاولة طرق متعددة لفتح PDF
                    try:
                        # الطريقة الأولى: طباعة مباشرة
                        os.startfile(temp_path, "print")
                        return True, "تم إرسال الملف للطباعة"
                    except:
                        # الطريقة الثانية: فتح في البرنامج الافتراضي
                        try:
                            os.startfile(temp_path)
                            return True, "تم إرسال الملف للطباعة"
                        except:
                            # الطريقة الثالثة: استخدام cmd
                            subprocess.run(['cmd', '/c', 'start', '', temp_path], check=True)
                            return True, "تم إرسال الملف للطباعة"
                else:  # Linux/Mac
                    try:
                        subprocess.run(['lpr', temp_path], check=True)
                        return True, "تم إرسال الملف للطباعة"
                    except:
                        subprocess.run(['xdg-open', temp_path])
                        return True, "تم إرسال الملف للطباعة"

            except Exception as print_error:
                # إذا فشلت جميع الطرق، احفظ الملف في مكان يمكن الوصول إليه
                desktop_path = os.path.join(os.path.expanduser("~"), "Desktop", f"{suggested_filename}.pdf")
                try:
                    import shutil
                    shutil.copy2(temp_path, desktop_path)

                    # عرض رسالة مفيدة للمستخدم
                    message = f"""تم حفظ ملف PDF على سطح المكتب

📁 مكان الملف: {desktop_path}

💡 لطباعة الملف:
1. اذهب إلى سطح المكتب
2. انقر مرتين على الملف لفتحه
3. اضغط Ctrl+P للطباعة

⚠️ ملاحظة: قد تحتاج لتثبيت برنامج قارئ PDF مثل Adobe Reader"""

                    return True, message
                except Exception as save_error:
                    return False, f"فشل في حفظ الملف: {save_error}\nالخطأ الأصلي: {print_error}"

        except Exception as e:
            return False, f"فشل في طباعة الملف: {e}"

# إنشاء مثيل عام للاستخدام
pdf_generator = TransactionPDFGenerator()
