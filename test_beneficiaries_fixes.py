#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار إصلاحات شاشة المستفيدين
Test Beneficiaries Window Fixes
"""

import os
import sys

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_edit_form_population():
    """اختبار تعبئة نموذج التعديل"""
    print("🧪 اختبار تعبئة نموذج التعديل...")
    
    try:
        from models import Beneficiary
        from database import db_manager
        
        # إنشاء مستفيد للاختبار
        test_beneficiary_data = {
            'name': 'مستفيد اختبار التعديل',
            'number': 'EDIT_TEST001',
            'rank': 'ضابط - نقيب',
            'department_id': 1,
            'unit_id': 1,
            'is_active': 1
        }
        
        # إدراج المستفيد
        insert_query = """
            INSERT INTO beneficiaries (name, number, rank, department_id, unit_id, is_active, created_at)
            VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        """
        
        result = db_manager.execute_query(
            insert_query,
            (
                test_beneficiary_data['name'],
                test_beneficiary_data['number'],
                test_beneficiary_data['rank'],
                test_beneficiary_data['department_id'],
                test_beneficiary_data['unit_id'],
                test_beneficiary_data['is_active']
            )
        )
        
        if result:
            beneficiary_id = result.lastrowid
            print(f"✅ تم إنشاء مستفيد اختبار برقم: {beneficiary_id}")
            
            # استرجاع المستفيد
            beneficiary = Beneficiary.get_by_id(beneficiary_id)
            
            if beneficiary:
                print(f"✅ تم استرجاع المستفيد: {beneficiary.name}")
                print(f"   📋 الرقم العام: {beneficiary.number}")
                print(f"   🎖️ الرتبة: {beneficiary.rank}")
                print(f"   🏢 الإدارة: {beneficiary.department_id}")
                print(f"   🏛️ الوحدة: {beneficiary.unit_id}")
                
                # تنظيف البيانات التجريبية
                db_manager.execute_query("DELETE FROM beneficiaries WHERE number = ?", (test_beneficiary_data['number'],))
                print("🧹 تم تنظيف البيانات التجريبية")
                
                return True
            else:
                print("❌ فشل في استرجاع المستفيد")
                return False
        else:
            print("❌ فشل في إنشاء المستفيد")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار التعديل: {e}")
        import traceback
        print(f"📋 تفاصيل الخطأ: {traceback.format_exc()}")
        return False

def test_delete_all_function():
    """اختبار دالة حذف الكل"""
    print("🧪 اختبار دالة حذف الكل...")
    
    try:
        from database import db_manager
        
        # إنشاء بيانات اختبار
        test_data = [
            ('مستفيد حذف 1', 'DEL_TEST001'),
            ('مستفيد حذف 2', 'DEL_TEST002'),
            ('مستفيد حذف 3', 'DEL_TEST003')
        ]
        
        # إدراج البيانات
        for name, number in test_data:
            db_manager.execute_query(
                "INSERT INTO beneficiaries (name, number, is_active, created_at) VALUES (?, ?, 1, CURRENT_TIMESTAMP)",
                (name, number)
            )
        
        print(f"✅ تم إنشاء {len(test_data)} مستفيد للاختبار")
        
        # عد المستفيدين قبل الحذف
        count_before = db_manager.fetch_one("SELECT COUNT(*) FROM beneficiaries WHERE number LIKE 'DEL_TEST%'")[0]
        print(f"📊 عدد المستفيدين قبل الحذف: {count_before}")
        
        # تنفيذ الحذف
        delete_result = db_manager.execute_query("DELETE FROM beneficiaries WHERE number LIKE 'DEL_TEST%'")
        deleted_count = delete_result.rowcount if delete_result else 0
        
        print(f"🗑️ تم حذف {deleted_count} مستفيد")
        
        # عد المستفيدين بعد الحذف
        count_after = db_manager.fetch_one("SELECT COUNT(*) FROM beneficiaries WHERE number LIKE 'DEL_TEST%'")[0]
        print(f"📊 عدد المستفيدين بعد الحذف: {count_after}")
        
        if count_after == 0 and deleted_count == len(test_data):
            print("✅ دالة الحذف تعمل بشكل صحيح")
            return True
        else:
            print("❌ دالة الحذف لا تعمل بشكل صحيح")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الحذف: {e}")
        import traceback
        print(f"📋 تفاصيل الخطأ: {traceback.format_exc()}")
        return False

def test_beneficiary_model():
    """اختبار نموذج المستفيد"""
    print("🧪 اختبار نموذج المستفيد...")
    
    try:
        from models import Beneficiary
        
        # اختبار استرجاع جميع المستفيدين
        beneficiaries = Beneficiary.get_all()
        print(f"📊 عدد المستفيدين في قاعدة البيانات: {len(beneficiaries)}")
        
        if beneficiaries:
            # عرض أول مستفيد كمثال
            first_beneficiary = beneficiaries[0]
            print(f"👤 مثال - المستفيد الأول:")
            print(f"   📋 الاسم: {first_beneficiary.name}")
            print(f"   🔢 الرقم: {first_beneficiary.number}")
            print(f"   🎖️ الرتبة: {first_beneficiary.rank}")
            print(f"   ✅ نشط: {first_beneficiary.is_active}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النموذج: {e}")
        import traceback
        print(f"📋 تفاصيل الخطأ: {traceback.format_exc()}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 اختبار إصلاحات شاشة المستفيدين")
    print("=" * 60)
    
    tests = [
        ("اختبار نموذج المستفيد", test_beneficiary_model),
        ("اختبار تعبئة نموذج التعديل", test_edit_form_population),
        ("اختبار دالة حذف الكل", test_delete_all_function)
    ]
    
    results = []
    
    for test_name, test_function in tests:
        print(f"\n🔄 {test_name}")
        print("-" * 40)
        
        try:
            result = test_function()
            results.append((test_name, result))
            
            if result:
                print(f"✅ {test_name}: نجح")
            else:
                print(f"❌ {test_name}: فشل")
                
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {e}")
            results.append((test_name, False))
    
    # ملخص النتائج
    print("\n" + "=" * 60)
    print("📊 ملخص نتائج الاختبار")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{status} - {test_name}")
    
    print("-" * 60)
    print(f"📈 النتيجة النهائية: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت!")
        print("✅ إصلاحات شاشة المستفيدين تعمل بشكل صحيح")
    else:
        print("⚠️ بعض الاختبارات فشلت")
        print("🔧 قد تحتاج إصلاحات إضافية")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
