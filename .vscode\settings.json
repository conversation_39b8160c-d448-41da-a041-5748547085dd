{"python.defaultInterpreterPath": "python", "python.terminal.activateEnvironment": true, "terminal.integrated.cwd": "${workspaceFolder}", "python.analysis.extraPaths": ["${workspaceFolder}", "${workspaceFolder}/ui", "${workspaceFolder}/utils"], "files.encoding": "utf8", "files.autoSave": "after<PERSON>elay", "python.linting.enabled": true, "python.linting.pylintEnabled": false, "python.linting.flake8Enabled": true, "python.formatting.provider": "black", "zencoder.enableRepoIndexing": true}