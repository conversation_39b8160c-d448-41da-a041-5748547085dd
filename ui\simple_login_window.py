#!/usr/bin/env python3
"""
نافذة تسجيل دخول مبسطة وموثوقة
Simple and Reliable Login Window
"""

import tkinter as tk
import ttkbootstrap as ttk_bs
from tkinter import messagebox
import sys
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# استيراد الإعدادات
from config import UI_CONFIG, APP_CONFIG

class SimpleLoginWindow:
    """نافذة تسجيل دخول مبسطة"""
    
    def __init__(self, auth_manager):
        self.auth_manager = auth_manager
        self.on_login_success = None
        self.login_window = None
        self.username_var = None
        self.password_var = None
        
        # إنشاء النافذة
        self.create_window()
    
    def create_window(self):
        """إنشاء النافذة"""
        # إنشاء نافذة جذر مستقلة مع ثيم cosmo
        self.login_window = ttk_bs.Window(
            title="🏪 تسجيل الدخول - نظام إدارة المخازن",
            themename=APP_CONFIG.get("theme", "cosmo"),
            size=(450, 550),
            resizable=(False, False)
        )

        # إخفاء النافذة مؤقتاً أثناء الإعداد
        self.login_window.withdraw()

        # تعيين أيقونة النافذة إذا كانت متوفرة
        try:
            icon_path = Path(__file__).parent.parent / "assets" / "icons" / "app_icon.ico"
            if icon_path.exists():
                self.login_window.iconbitmap(str(icon_path))
        except:
            pass

        # إنشاء المحتوى أولاً
        self.create_content()

        # توسيط النافذة بدقة بعد إنشاء المحتوى
        self.center_window()

        # إظهار النافذة في المقدمة
        self.login_window.deiconify()
        self.login_window.lift()
        self.login_window.attributes('-topmost', True)
        self.login_window.focus_force()
        self.login_window.attributes('-topmost', False)

        print(f"✅ نافذة تسجيل الدخول: {self.login_window.geometry()}")
        print(f"🎯 النافذة تفتح في وسط الشاشة")
    
    def center_window(self):
        """توسيط النافذة بدقة على الشاشة"""
        # تحديث النافذة للحصول على الأبعاد الصحيحة
        self.login_window.update_idletasks()

        # أبعاد الشاشة
        screen_width = self.login_window.winfo_screenwidth()
        screen_height = self.login_window.winfo_screenheight()

        # أبعاد النافذة
        window_width = 450
        window_height = 550

        # حساب الموضع للتوسيط المثالي في وسط الشاشة تماماً
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2

        # تعديل طفيف للموضع العمودي لجعلها أكثر توسطاً بصرياً
        y = max(50, y - 30)  # رفع النافذة قليلاً للأعلى

        # تعيين الموضع والحجم بدقة
        self.login_window.geometry(f"{window_width}x{window_height}+{x}+{y}")

        # التأكد من أن النافذة في المقدمة وفي الوسط
        self.login_window.update()
        self.login_window.deiconify()

        print(f"📐 أبعاد الشاشة: {screen_width}x{screen_height}")
        print(f"📐 أبعاد النافذة: {window_width}x{window_height}")
        print(f"📍 موضع النافذة: x={x}, y={y}")
        print(f"🎯 النافذة موسطة في الشاشة")
    
    def create_content(self):
        """إنشاء محتوى النافذة مع ألوان لوحة التحكم"""
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.login_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=35, pady=35)

        # إطار العنوان مع خلفية بيضاء
        header_frame = ttk_bs.Frame(main_frame, bootstyle="light")
        header_frame.pack(fill=tk.X, pady=(0, 30))

        # العنوان الرئيسي مع ألوان متناسقة مع لوحة التحكم
        title_label = ttk_bs.Label(
            header_frame,
            text="🏪 نظام إدارة المخازن والمستودعات",
            font=(UI_CONFIG["font_family"], UI_CONFIG["font_size_xlarge"], "bold"),
            bootstyle="dark"
        )
        title_label.pack(pady=15)

        # خط فاصل بلون رمادي فاتح
        separator = ttk_bs.Separator(main_frame, bootstyle="secondary")
        separator.pack(fill=tk.X, pady=(0, 25))

        # العنوان الفرعي
        subtitle_label = ttk_bs.Label(
            main_frame,
            text="🔐 تسجيل الدخول إلى النظام",
            font=(UI_CONFIG["font_family"], UI_CONFIG["font_size_large"], "bold"),
            bootstyle="secondary"
        )
        subtitle_label.pack(pady=(0, 25))
        
        # إطار الحقول مع تصميم محسن
        fields_frame = ttk_bs.Frame(main_frame)
        fields_frame.pack(fill=tk.X, pady=(0, 25))

        # حقل اسم المستخدم مع ألوان النظام
        username_label = ttk_bs.Label(
            fields_frame,
            text="👤 اسم المستخدم:",
            font=(UI_CONFIG["font_family"], UI_CONFIG["font_size_normal"], "bold"),
            bootstyle="dark"
        )
        username_label.pack(anchor=tk.E, pady=(0, 8))

        # تحميل آخر اسم مستخدم محفوظ أو استخدام admin كافتراضي
        saved_username = self.load_saved_username()
        self.username_var = tk.StringVar(value=saved_username)
        username_entry = ttk_bs.Entry(
            fields_frame,
            textvariable=self.username_var,
            width=UI_CONFIG["entry_width"],
            font=(UI_CONFIG["font_family"], UI_CONFIG["font_size_normal"]),
            bootstyle="light"
        )
        username_entry.pack(fill=tk.X, pady=(0, 20))

        # ربط دالة تنظيف البيانات عند التغيير
        self.username_var.trace('w', self.clean_username)

        # حقل كلمة المرور مع ألوان النظام
        password_label = ttk_bs.Label(
            fields_frame,
            text="🔒 كلمة المرور:",
            font=(UI_CONFIG["font_family"], UI_CONFIG["font_size_normal"], "bold"),
            bootstyle="dark"
        )
        password_label.pack(anchor=tk.E, pady=(0, 8))

        self.password_var = tk.StringVar(value="")
        password_entry = ttk_bs.Entry(
            fields_frame,
            textvariable=self.password_var,
            show="*",
            width=UI_CONFIG["entry_width"],
            font=(UI_CONFIG["font_family"], UI_CONFIG["font_size_normal"]),
            bootstyle="light"
        )
        password_entry.pack(fill=tk.X, pady=(0, 30))

        # ربط دالة تنظيف البيانات عند التغيير
        self.password_var.trace('w', self.clean_password)
        
        # إطار الأزرار مع ألوان النظام
        buttons_frame = ttk_bs.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(15, 0))

        # زر تسجيل الدخول بألوان متناسقة مع النظام
        login_button = ttk_bs.Button(
            buttons_frame,
            text="🔑 تسجيل الدخول",
            command=self.login,
            bootstyle="success",
            width=UI_CONFIG["button_width"] + 5
        )
        login_button.pack(pady=(0, 15))

        # زر الخروج
        exit_button = ttk_bs.Button(
            buttons_frame,
            text="❌ خروج",
            command=self.exit_app,
            bootstyle="danger-outline",
            width=UI_CONFIG["button_width"] + 5
        )
        exit_button.pack(pady=(0, 15))

        # خط فاصل
        separator2 = ttk_bs.Separator(main_frame, bootstyle="secondary")
        separator2.pack(fill=tk.X, pady=(10, 15))

        # إضافة معلومات إضافية في الأسفل
        info_frame = ttk_bs.Frame(main_frame)
        info_frame.pack(fill=tk.X, pady=(0, 0))

        info_label = ttk_bs.Label(
            info_frame,
            text=f"الإصدار {APP_CONFIG.get('app_version', '2.0.0')} - {APP_CONFIG.get('app_name', 'نظام إدارة المخازن')}",
            font=(UI_CONFIG["font_family"], UI_CONFIG["font_size_small"]),
            bootstyle="secondary"
        )
        info_label.pack()
        
        # ربط Enter بتسجيل الدخول
        self.login_window.bind('<Return>', lambda e: self.login())

        # ربط Escape بالخروج
        self.login_window.bind('<Escape>', lambda e: self.exit_app())

        # معالج إغلاق النافذة
        self.login_window.protocol("WM_DELETE_WINDOW", self.exit_app)

        # تركيز على حقل اسم المستخدم
        username_entry.focus_set()

    def clean_username(self, *args):
        """تنظيف اسم المستخدم من الأحرف الخفية"""
        try:
            current = self.username_var.get()
            # إزالة الأحرف الخفية والمسافات الإضافية
            cleaned = current.replace('\u200b', '').replace('\u200c', '').replace('\u200d', '').strip()
            if cleaned != current:
                self.username_var.set(cleaned)
        except:
            pass

    def clean_password(self, *args):
        """تنظيف كلمة المرور من الأحرف الخفية"""
        try:
            current = self.password_var.get()
            # إزالة الأحرف الخفية فقط (بدون strip للمسافات لأنها قد تكون جزء من كلمة المرور)
            cleaned = current.replace('\u200b', '').replace('\u200c', '').replace('\u200d', '')
            if cleaned != current:
                self.password_var.set(cleaned)
        except:
            pass

    def login(self):
        """تسجيل الدخول مع تحقق محسن"""
        try:
            username = self.username_var.get().strip()
            password = self.password_var.get().strip()

            # التحقق من وجود البيانات
            if not username or not password:
                self.show_error_message("يرجى إدخال اسم المستخدم وكلمة المرور")
                return

            # التحقق من صحة البيانات
            if len(username) < 1 or len(password) < 1:
                self.show_error_message("اسم المستخدم وكلمة المرور مطلوبان")
                return

            # تنظيف إضافي للبيانات
            username = username.replace('\u200b', '').replace('\u200c', '').replace('\u200d', '')  # إزالة أحرف التحكم الخفية
            password = password.replace('\u200b', '').replace('\u200c', '').replace('\u200d', '')

            print(f"🔍 محاولة تسجيل دخول: '{username}'")

            # تعطيل زر تسجيل الدخول مؤقتاً
            self.set_login_state(False)

            # محاولة تسجيل الدخول
            success, message, user = self.auth_manager.authenticate(username, password)

            print(f"📊 نتيجة المصادقة: نجح={success}, رسالة={message}")

            if success and user:
                print(f"✅ نجح تسجيل الدخول: {user.username} ({user.full_name})")

                # حفظ بيانات الدخول الصحيحة
                self.save_credentials(username)

                # عرض رسالة نجاح مؤقتة
                self.show_success_message("تم تسجيل الدخول بنجاح!")

                # تأخير قصير قبل الانتقال
                self.login_window.after(1000, lambda: self.complete_login(user))

            else:
                print(f"❌ فشل تسجيل الدخول: {message}")
                # عرض رسالة خطأ واضحة
                if "بيانات الدخول غير صحيحة" in message:
                    self.show_error_message("يرجى فحص اسم المستخدم وكلمة المرور")
                else:
                    self.show_error_message(message)
                self.set_login_state(True)
                # مسح كلمة المرور فقط
                self.password_var.set("")
                # التركيز على حقل كلمة المرور
                self.focus_password_field()

        except Exception as e:
            print(f"❌ خطأ في تسجيل الدخول: {e}")
            self.show_error_message(f"حدث خطأ في تسجيل الدخول: {str(e)}")
            self.set_login_state(True)

    def complete_login(self, user):
        """إكمال عملية تسجيل الدخول"""
        try:
            # إخفاء نافذة تسجيل الدخول
            self.login_window.withdraw()

            # استدعاء دالة النجاح
            if self.on_login_success:
                self.on_login_success(user)

            # إغلاق نافذة تسجيل الدخول
            self.login_window.quit()

        except Exception as e:
            print(f"❌ خطأ في إكمال تسجيل الدخول: {e}")

    def focus_password_field(self):
        """التركيز على حقل كلمة المرور"""
        try:
            # البحث عن حقل كلمة المرور والتركيز عليه
            for widget in self.login_window.winfo_children():
                if hasattr(widget, 'winfo_children'):
                    for child in widget.winfo_children():
                        if hasattr(child, 'winfo_children'):
                            for grandchild in child.winfo_children():
                                if isinstance(grandchild, ttk_bs.Entry) and hasattr(grandchild, 'cget'):
                                    if grandchild.cget('show') == '*':  # حقل كلمة المرور
                                        grandchild.focus_set()
                                        return
        except Exception as e:
            print(f"خطأ في التركيز على حقل كلمة المرور: {e}")

    def load_saved_username(self):
        """تحميل اسم المستخدم المحفوظ"""
        try:
            from pathlib import Path
            import json

            credentials_file = Path("data/saved_credentials.json")
            if credentials_file.exists():
                with open(credentials_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    username = data.get('username', 'admin')
                    # التحقق من أن اسم المستخدم صحيح (ليس رقم)
                    if username and not username.isdigit():
                        return username

            # إذا لم يوجد ملف أو كان اسم المستخدم رقم، استخدم admin
            return "admin"

        except Exception as e:
            print(f"خطأ في تحميل اسم المستخدم المحفوظ: {e}")
            return "admin"

    def save_credentials(self, username):
        """حفظ بيانات الدخول الصحيحة فقط"""
        try:
            from pathlib import Path
            import json
            from datetime import datetime

            # التحقق من أن اسم المستخدم صحيح (ليس رقم وليس فارغ)
            if not username or username.isdigit() or len(username.strip()) == 0:
                print(f"⚠️ تم تجاهل حفظ اسم مستخدم غير صحيح: {username}")
                return

            # إنشاء مجلد البيانات إذا لم يكن موجود
            data_dir = Path("data")
            data_dir.mkdir(exist_ok=True)

            credentials_file = data_dir / "saved_credentials.json"

            data = {
                "username": username.strip(),
                "remember": True,
                "last_login": datetime.now().isoformat()
            }

            with open(credentials_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

            print(f"✅ تم حفظ بيانات الدخول: {username}")

        except Exception as e:
            print(f"خطأ في حفظ بيانات الدخول: {e}")

    def set_login_state(self, enabled):
        """تعيين حالة زر تسجيل الدخول"""
        try:
            # البحث عن زر تسجيل الدخول وتعطيله/تفعيله
            for widget in self.login_window.winfo_children():
                self._update_button_state(widget, enabled)
        except Exception as e:
            print(f"تحذير: خطأ في تعيين حالة الزر: {e}")

    def _update_button_state(self, widget, enabled):
        """تحديث حالة الأزرار بشكل تكراري"""
        try:
            if hasattr(widget, 'winfo_children'):
                for child in widget.winfo_children():
                    self._update_button_state(child, enabled)

            if isinstance(widget, ttk_bs.Button) and "تسجيل الدخول" in str(widget.cget('text')):
                if enabled:
                    widget.configure(state='normal', text="🔑 تسجيل الدخول")
                else:
                    widget.configure(state='disabled', text="⏳ جاري التحقق...")
        except:
            pass

    def show_error_message(self, message):
        """عرض رسالة خطأ"""
        try:
            # إنشاء نافذة خطأ مخصصة
            error_window = tk.Toplevel(self.login_window)
            error_window.title("خطأ")
            error_window.geometry("300x150")
            error_window.resizable(False, False)
            error_window.configure(bg='#f8d7da')

            # توسيط النافذة
            error_window.transient(self.login_window)
            error_window.grab_set()

            # محتوى رسالة الخطأ
            error_frame = ttk_bs.Frame(error_window)
            error_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

            error_label = ttk_bs.Label(
                error_frame,
                text=f"❌ {message}",
                font=("Arial", 11),
                bootstyle="danger",
                wraplength=250
            )
            error_label.pack(pady=(10, 20))

            ok_button = ttk_bs.Button(
                error_frame,
                text="موافق",
                command=error_window.destroy,
                width=15,
            bootstyle="danger"
            )
            ok_button.pack()

            # توسيط نافذة الخطأ
            error_window.update_idletasks()
            x = self.login_window.winfo_x() + (self.login_window.winfo_width() // 2) - 150
            y = self.login_window.winfo_y() + (self.login_window.winfo_height() // 2) - 75
            error_window.geometry(f"300x150+{x}+{y}")

            # تركيز على النافذة
            error_window.focus_set()

        except Exception as e:
            # في حالة فشل النافذة المخصصة، استخدم messagebox العادي
            messagebox.showerror("خطأ", message)

    def show_success_message(self, message):
        """عرض رسالة نجاح"""
        try:
            # إنشاء نافذة نجاح مخصصة
            success_window = tk.Toplevel(self.login_window)
            success_window.title("نجح")
            success_window.geometry("300x150")
            success_window.resizable(False, False)
            success_window.configure(bg='#d4edda')

            # توسيط النافذة
            success_window.transient(self.login_window)
            success_window.grab_set()

            # محتوى رسالة النجاح
            success_frame = ttk_bs.Frame(success_window)
            success_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

            success_label = ttk_bs.Label(
                success_frame,
                text=f"✅ {message}",
                font=("Arial", 11),
                bootstyle="success",
                wraplength=250
            )
            success_label.pack(pady=(20, 20))

            # توسيط نافذة النجاح
            success_window.update_idletasks()
            x = self.login_window.winfo_x() + (self.login_window.winfo_width() // 2) - 150
            y = self.login_window.winfo_y() + (self.login_window.winfo_height() // 2) - 75
            success_window.geometry(f"300x150+{x}+{y}")

            # إغلاق تلقائي بعد ثانية واحدة
            success_window.after(1000, success_window.destroy)

        except Exception as e:
            print(f"تحذير: خطأ في عرض رسالة النجاح: {e}")
    
    def exit_app(self):
        """الخروج من التطبيق"""
        if messagebox.askyesno("تأكيد الخروج", "هل تريد الخروج من التطبيق؟"):
            self.login_window.quit()
            sys.exit(0)
    
    def show(self):
        """عرض النافذة"""
        self.login_window.mainloop()
    
    def destroy(self):
        """إغلاق النافذة"""
        if self.login_window:
            self.login_window.destroy()

def test_simple_login():
    """اختبار نافذة تسجيل الدخول المبسطة"""
    print("🧪 اختبار نافذة تسجيل الدخول المبسطة...")
    
    try:
        from auth_manager import AuthManager
        
        auth_manager = AuthManager()
        
        def on_success(user):
            print(f"🎉 تم تسجيل الدخول بنجاح: {user.username}")
        
        login_window = SimpleLoginWindow(auth_manager)
        login_window.on_login_success = on_success
        login_window.show()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_simple_login()
