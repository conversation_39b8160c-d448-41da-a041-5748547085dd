"""
نافذة مواصفات وإمكانيات النظام
System Specifications and Capabilities Window
"""

import tkinter as tk
from tkinter import ttk, scrolledtext
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
import os
import sqlite3
from pathlib import Path

class SystemSpecsWindow:
    """نافذة عرض مواصفات وإمكانيات النظام الكاملة"""
    
    def __init__(self, parent):
        self.parent = parent
        self.window = None
        self.create_window()
    
    def create_window(self):
        """إنشاء النافذة"""
        self.window = ttk_bs.Toplevel(self.parent)
        self.window.title("🚀 مواصفات وإمكانيات النظام الكاملة")
        self.window.geometry("1000x700")
        self.window.resizable(True, True)
        
        # توسيط النافذة
        self.center_window()
        
        # إنشاء المحتوى
        self.create_content()
        
        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        self.window.grab_set()
        self.window.focus_set()
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        width = 1000
        height = 700
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_content(self):
        """إنشاء محتوى النافذة"""
        # إطار رئيسي
        main_frame = ttk_bs.Frame(self.window)
        main_frame.pack(fill=BOTH, expand=True, padx=10, pady=10)
        
        # عنوان النافذة
        title_frame = ttk_bs.Frame(main_frame)
        title_frame.pack(fill=X, pady=(0, 10))
        
        title_label = ttk_bs.Label(
            title_frame,
            text="🚀 مواصفات وإمكانيات نظام إدارة المخازن الكاملة",
            font=("Arial", 16, "bold"),
            bootstyle="primary"
        )
        title_label.pack()
        
        # إنشاء Notebook للتبويبات
        notebook = ttk_bs.Notebook(main_frame)
        notebook.pack(fill=BOTH, expand=True)
        
        # التبويبات
        self.create_overview_tab(notebook)
        self.create_database_tab(notebook)
        self.create_performance_tab(notebook)
        self.create_features_tab(notebook)
        self.create_scenarios_tab(notebook)
        self.create_technical_tab(notebook)
        
        # إطار الأزرار
        buttons_frame = ttk_bs.Frame(main_frame)
        buttons_frame.pack(fill=X, pady=(10, 0))
        
        # زر إغلاق
        close_btn = ttk_bs.Button(
            buttons_frame,
            text="إغلاق",
            command=self.window.destroy,
            width=15,
            bootstyle="secondary"
        )
        close_btn.pack(side=RIGHT, padx=(5, 0))
        
        # زر تحديث البيانات
        refresh_btn = ttk_bs.Button(
            buttons_frame,
            text="🔄 تحديث البيانات",
            command=self.refresh_data,
            width=22,
            bootstyle="primary"
        )
        refresh_btn.pack(side=RIGHT)
    
    def create_overview_tab(self, notebook):
        """تبويب النظرة العامة"""
        frame = ttk_bs.Frame(notebook)
        notebook.add(frame, text="📊 نظرة عامة")
        
        # إطار قابل للتمرير
        canvas = tk.Canvas(frame)
        scrollbar = ttk_bs.Scrollbar(frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk_bs.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # المحتوى
        content = """
🏷️ معلومات النظام الأساسية

• اسم النظام: نظام إدارة المخازن والمستودعات المتقدم
• الإصدار: v2.1.0
• نوع التطبيق: تطبيق سطح المكتب (Desktop Application)
• قاعدة البيانات: SQLite (محلية ومحمولة)
• لغة البرمجة: Python 3.8+
• واجهة المستخدم: tkinter + ttkbootstrap
• نظام التشغيل: Windows 10/11
• حجم التطبيق: ~50-100 MB (بعد التحويل لـ EXE)

🎯 الحدود العملية الموصى بها

👥 المستفيدين:
• الحد الأمثل: 100,000 مستفيد
• الحد الأقصى العملي: 500,000 مستفيد
• حجم البيانات لـ 100,000 مستفيد: ~50 MB

📦 الأصناف:
• الحد الأمثل: 50,000 صنف
• الحد الأقصى العملي: 200,000 صنف
• حجم البيانات لـ 50,000 صنف: ~40 MB

📋 الجدول التنظيمي:
• الحد الأمثل: 10,000 عنصر
• الحد الأقصى العملي: 50,000 عنصر
• حجم البيانات لـ 10,000 عنصر: ~4 MB

📥 عمليات الإضافة:
• الحد الأمثل: 500,000 عملية
• الحد الأقصى العملي: 2,000,000 عملية
• حجم البيانات لـ 500,000 عملية: ~300 MB

📤 عمليات الصرف:
• الحد الأمثل: 1,000,000 عملية
• الحد الأقصى العملي: 5,000,000 عملية
• حجم البيانات لـ 1,000,000 عملية: ~400 MB

🏆 الخلاصة
• يتحمل ملايين السجلات
• مناسب لجميع أحجام المؤسسات
• يدوم عقود من الاستخدام
• آمن ومحمي بالكامل
• سهل الاستخدام والنقل
        """
        
        text_widget = scrolledtext.ScrolledText(
            scrollable_frame,
            wrap=tk.WORD,
            font=("Arial", 11),
            height=25,
            width=80
        )
        text_widget.pack(fill=BOTH, expand=True, padx=10, pady=10)
        text_widget.insert(tk.END, content)
        text_widget.config(state=tk.DISABLED)
    
    def create_database_tab(self, notebook):
        """تبويب قاعدة البيانات"""
        frame = ttk_bs.Frame(notebook)
        notebook.add(frame, text="💾 قاعدة البيانات")
        
        # إطار قابل للتمرير
        canvas = tk.Canvas(frame)
        scrollbar = ttk_bs.Scrollbar(frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk_bs.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # الحصول على بيانات قاعدة البيانات الحالية
        db_info = self.get_database_info()
        
        content = f"""
💾 معلومات قاعدة البيانات الحالية

📊 الحالة الحالية:
• حجم قاعدة البيانات: {db_info['size']}
• عدد الجداول: {db_info['tables_count']}
• المستفيدين: {db_info['beneficiaries']} مستفيد
• الجدول التنظيمي: {db_info['org_chart']} عنصر
• عمليات الإضافة: {db_info['added_items']} عملية
• عمليات الصرف: {db_info['transactions']} عملية

🎯 الحدود النظرية لـ SQLite:
• الحد الأقصى لحجم قاعدة البيانات: 281 تيرابايت
• الحد الأقصى لعدد السجلات في الجدول: 18 كوينتليون سجل
• الحد الأقصى لعدد الأعمدة: 2,000 عمود
• الحد الأقصى لحجم السجل الواحد: 1 جيجابايت
• الحد الأقصى لطول النص: 1 مليار حرف

💡 التوقعات العملية:

📊 حتى 10,000 سجل لكل جدول:
• سرعة الاستعلام: فورية (< 0.1 ثانية)
• استهلاك الذاكرة: منخفض (< 50 MB)
• حجم قاعدة البيانات: < 100 MB

📊 حتى 100,000 سجل لكل جدول:
• سرعة الاستعلام: سريعة (< 0.5 ثانية)
• استهلاك الذاكرة: متوسط (50-200 MB)
• حجم قاعدة البيانات: 100 MB - 1 GB

📊 حتى 1,000,000 سجل لكل جدول:
• سرعة الاستعلام: جيدة (0.5-2 ثانية)
• استهلاك الذاكرة: عالي (200-500 MB)
• حجم قاعدة البيانات: 1-5 GB

⚠️ أكثر من 1,000,000 سجل لكل جدول:
• سرعة الاستعلام: بطيئة (2-10 ثواني)
• استهلاك الذاكرة: عالي جداً (> 500 MB)
• حجم قاعدة البيانات: > 5 GB
• يُنصح بتحسين الفهارس والاستعلامات
        """
        
        text_widget = scrolledtext.ScrolledText(
            scrollable_frame,
            wrap=tk.WORD,
            font=("Arial", 11),
            height=25,
            width=80
        )
        text_widget.pack(fill=BOTH, expand=True, padx=10, pady=10)
        text_widget.insert(tk.END, content)
        text_widget.config(state=tk.DISABLED)
    
    def create_performance_tab(self, notebook):
        """تبويب الأداء"""
        frame = ttk_bs.Frame(notebook)
        notebook.add(frame, text="⚡ الأداء")
        
        content = """
⚡ سرعة العمليات المتوقعة

• تسجيل الدخول: < 1 ثانية
• البحث عن مستفيد: < 0.5 ثانية
• إضافة صنف جديد: < 2 ثانية
• إنشاء أذن صرف: < 3 ثواني
• طباعة تقرير: < 5 ثواني
• نسخة احتياطية: < 10 ثواني

📊 استهلاك الموارد

الاستهلاك العادي:
• الذاكرة (RAM): 50-100 MB
• المعالج (CPU): 1-5%
• القرص الصلب: 100 MB

الاستهلاك الأقصى:
• الذاكرة (RAM): 500 MB
• المعالج (CPU): 20%
• القرص الصلب: 10 GB

💻 متطلبات النظام

الحد الأدنى:
• نظام التشغيل: Windows 10
• المعالج: Intel i3 أو AMD مكافئ
• الذاكرة: 4 GB RAM
• مساحة التخزين: 500 MB
• الشاشة: 1024x768

الموصى به:
• نظام التشغيل: Windows 11
• المعالج: Intel i5 أو أعلى
• الذاكرة: 8 GB RAM أو أكثر
• مساحة التخزين: 2 GB أو أكثر
• الشاشة: 1920x1080 أو أعلى

🚀 نصائح لتحسين الأداء

1. إدارة البيانات:
• أرشفة البيانات القديمة (أكثر من 5 سنوات)
• حذف السجلات غير المستخدمة بانتظام
• ضغط قاعدة البيانات شهرياً (VACUUM)

2. تحسين الأداء:
• إنشاء فهارس للحقول المستخدمة في البحث
• تجنب الاستعلامات المعقدة على الجداول الكبيرة
• استخدام التصفح بالصفحات للنتائج الكبيرة

3. إدارة التخزين:
• نسخ احتياطية منتظمة
• مراقبة مساحة القرص الصلب
• استخدام SSD لتحسين سرعة الوصول
        """
        
        text_widget = scrolledtext.ScrolledText(
            frame,
            wrap=tk.WORD,
            font=("Arial", 11)
        )
        text_widget.pack(fill=BOTH, expand=True, padx=10, pady=10)
        text_widget.insert(tk.END, content)
        text_widget.config(state=tk.DISABLED)
    
    def create_features_tab(self, notebook):
        """تبويب الميزات"""
        frame = ttk_bs.Frame(notebook)
        notebook.add(frame, text="🛠️ الميزات")
        
        content = """
🛠️ الميزات والإمكانيات الكاملة

👤 إدارة المستخدمين والصلاحيات:
• نظام تسجيل دخول آمن
• إدارة المستخدمين والأدوار
• صلاحيات متدرجة (عرض، إضافة، تعديل، حذف)
• تشفير كلمات المرور
• سجل العمليات والمراجعة
• حماية من محاولات الاختراق

🏢 الهيكل التنظيمي:
• إدارة الوحدات والإدارات
• إدارة الأقسام والفروع
• الجدول التنظيمي الشامل
• ربط المستفيدين بالهيكل التنظيمي
• تقارير الهيكل التنظيمي

👥 إدارة المستفيدين:
• إضافة وتعديل بيانات المستفيدين
• البحث المتقدم والفلترة
• ربط المستفيدين بالوحدات التنظيمية
• تتبع تاريخ المستفيدين
• تقارير المستفيدين المفصلة

📦 إدارة المخزون:
• إدارة الأصناف والفئات
• تتبع الكميات والمخزون
• إدارة الحد الأدنى والأقصى
• تتبع تواريخ الانتهاء
• إدارة المواقع والموردين
• الباركود والترميز

📥 عمليات الإضافة:
• إضافة أصناف جديدة للمخزن
• تسجيل كميات الوارد
• ربط العمليات بالمستخدمين
• تتبع مصادر الإضافة
• تقارير عمليات الإضافة

📤 عمليات الصرف:
• إنشاء أذونات الصرف
• ربط الصرف بالمستفيدين
• تتبع الكميات المصروفة
• نظام الموافقات
• طباعة أذونات الصرف
• تقارير الصرف المفصلة

📊 التقارير والإحصائيات:
• تقارير المخزون الحالي
• تقارير الحركة (وارد/صادر)
• تقارير المستفيدين
• تقارير الأصناف الناقصة
• تقارير منتهية الصلاحية
• إحصائيات شاملة
• تصدير التقارير (PDF, Excel)

🔍 البحث والفلترة:
• البحث السريع
• البحث المتقدم
• فلترة متعددة المعايير
• حفظ معايير البحث
• البحث الذكي والاقتراحات

💾 النسخ الاحتياطي والاستعادة:
• نسخ احتياطية تلقائية
• نسخ احتياطية يدوية
• استعادة البيانات
• جدولة النسخ الاحتياطية
• ضغط ملفات النسخ الاحتياطية

🎨 واجهة المستخدم:
• تصميم عصري وجذاب
• سهولة الاستخدام
• دعم اللغة العربية كاملاً
• ثيمات متعددة
• واجهة قابلة للتخصيص
• اختصارات لوحة المفاتيح
        """
        
        text_widget = scrolledtext.ScrolledText(
            frame,
            wrap=tk.WORD,
            font=("Arial", 11)
        )
        text_widget.pack(fill=BOTH, expand=True, padx=10, pady=10)
        text_widget.insert(tk.END, content)
        text_widget.config(state=tk.DISABLED)
    
    def create_scenarios_tab(self, notebook):
        """تبويب السيناريوهات"""
        frame = ttk_bs.Frame(notebook)
        notebook.add(frame, text="📅 سيناريوهات الاستخدام")
        
        content = """
📅 سيناريوهات الاستخدام الزمني

🏢 مؤسسة صغيرة (100 موظف)

السنة الأولى:
• المستفيدين: 1,000
• الأصناف: 5,000
• عمليات الصرف: 18,000
• حجم قاعدة البيانات: 50 MB

3 سنوات:
• المستفيدين: 2,000
• الأصناف: 8,000
• عمليات الصرف: 55,000
• حجم قاعدة البيانات: 150 MB

5 سنوات:
• المستفيدين: 3,000
• الأصناف: 10,000
• عمليات الصرف: 90,000
• حجم قاعدة البيانات: 250 MB

10 سنوات:
• المستفيدين: 5,000
• الأصناف: 15,000
• عمليات الصرف: 200,000
• حجم قاعدة البيانات: 500 MB

🏢 مؤسسة متوسطة (1,000 موظف)

السنة الأولى:
• المستفيدين: 5,000
• الأصناف: 15,000
• عمليات الصرف: 70,000
• حجم قاعدة البيانات: 200 MB

3 سنوات:
• المستفيدين: 8,000
• الأصناف: 20,000
• عمليات الصرف: 220,000
• حجم قاعدة البيانات: 600 MB

5 سنوات:
• المستفيدين: 12,000
• الأصناف: 25,000
• عمليات الصرف: 400,000
• حجم قاعدة البيانات: 1.2 GB

10 سنوات:
• المستفيدين: 20,000
• الأصناف: 35,000
• عمليات الصرف: 800,000
• حجم قاعدة البيانات: 2 GB

🏢 مؤسسة كبيرة (10,000 موظف)

السنة الأولى:
• المستفيدين: 20,000
• الأصناف: 50,000
• عمليات الصرف: 350,000
• حجم قاعدة البيانات: 1 GB

3 سنوات:
• المستفيدين: 35,000
• الأصناف: 70,000
• عمليات الصرف: 1,100,000
• حجم قاعدة البيانات: 3 GB

5 سنوات:
• المستفيدين: 50,000
• الأصناف: 90,000
• عمليات الصرف: 2,000,000
• حجم قاعدة البيانات: 5 GB

10 سنوات:
• المستفيدين: 80,000
• الأصناف: 120,000
• عمليات الصرف: 4,000,000
• حجم قاعدة البيانات: 10 GB

🎯 التوصيات النهائية

للاستخدام الأمثل:
• حجم قاعدة البيانات: أقل من 2 GB
• عدد السجلات الإجمالي: أقل من 1 مليون
• أرشفة البيانات: كل 3-5 سنوات
• نسخ احتياطية: يومية أو أسبوعية

علامات التحذير:
• حجم قاعدة البيانات > 5 GB: فكر في التقسيم
• الاستعلامات > 5 ثواني: حسّن الفهارس
• استهلاك الذاكرة > 1 GB: قلل البيانات المحملة
        """
        
        text_widget = scrolledtext.ScrolledText(
            frame,
            wrap=tk.WORD,
            font=("Arial", 11)
        )
        text_widget.pack(fill=BOTH, expand=True, padx=10, pady=10)
        text_widget.insert(tk.END, content)
        text_widget.config(state=tk.DISABLED)
    
    def create_technical_tab(self, notebook):
        """تبويب المواصفات التقنية"""
        frame = ttk_bs.Frame(notebook)
        notebook.add(frame, text="🔧 مواصفات تقنية")
        
        content = """
🔧 المواصفات التقنية الكاملة

📚 المكتبات المطلوبة:
• Python 3.8+
• tkinter (مدمجة)
• ttkbootstrap
• sqlite3 (مدمجة)
• bcrypt
• pillow
• reportlab
• openpyxl
• pandas

🚀 مميزات التطبيق بعد التحويل لـ EXE:

📦 خصائص ملف EXE:
• حجم الملف: 50-100 MB
• لا يحتاج تثبيت Python
• يعمل على أي جهاز Windows
• قاعدة بيانات محمولة
• لا يحتاج اتصال إنترنت
• أمان عالي للبيانات

📁 هيكل الملفات:
نظام_إدارة_المخازن/
├── نظام_إدارة_المخازن.exe (الملف الرئيسي)
├── data/
│   └── stores_management.db (قاعدة البيانات)
├── reports/ (التقارير المُصدرة)
├── backups/ (النسخ الاحتياطية)
├── logs/ (ملفات السجل)
└── README.txt (دليل الاستخدام)

🔄 قابلية النقل:
• محمول بالكامل: يمكن نسخه على فلاشة
• يعمل من أي مكان: لا يحتاج تثبيت
• البيانات محفوظة: تنتقل مع التطبيق
• نسخ متعددة: يمكن تشغيل عدة نسخ

🛡️ الأمان والحماية:

🔐 مستويات الأمان:
• تشفير كلمات المرور: bcrypt
• حماية قاعدة البيانات: SQLite آمنة
• سجل العمليات: تتبع جميع الأنشطة
• صلاحيات متدرجة: تحكم في الوصول
• نسخ احتياطية مشفرة: حماية البيانات

🔒 حماية البيانات:
• لا توجد اتصالات خارجية
• البيانات محلية بالكامل
• لا يتم إرسال بيانات للإنترنت
• تحكم كامل في البيانات

🔍 مؤشرات المراقبة:
• حجم ملف قاعدة البيانات
• زمن الاستعلامات
• استهلاك الذاكرة
• مساحة القرص المتاحة

🌟 التميز:
• أداء فائق السرعة
• واجهة عربية كاملة
• لا يحتاج إنترنت
• محمول بالكامل
• مجاني ومفتوح المصدر

📅 معلومات الإصدار:
• تاريخ الإنشاء: ديسمبر 2024
• آخر تحديث: الإصدار v2.1.0
• تطوير: فريق تطوير نظام إدارة المخازن

🌟 نظام إدارة المخازن - الحل الشامل والمتكامل 🌟
        """
        
        text_widget = scrolledtext.ScrolledText(
            frame,
            wrap=tk.WORD,
            font=("Arial", 11)
        )
        text_widget.pack(fill=BOTH, expand=True, padx=10, pady=10)
        text_widget.insert(tk.END, content)
        text_widget.config(state=tk.DISABLED)
    
    def get_database_info(self):
        """الحصول على معلومات قاعدة البيانات الحالية"""
        try:
            # البحث عن قاعدة البيانات
            db_paths = [
                "stores_management.db",
                "data/stores_management.db",
                Path.home() / "Documents" / "Desktop_Stores_Data" / "stores_management.db"
            ]
            
            db_path = None
            for path in db_paths:
                if os.path.exists(path):
                    db_path = path
                    break
            
            if not db_path:
                return {
                    'size': 'غير موجود',
                    'tables_count': 0,
                    'beneficiaries': 0,
                    'org_chart': 0,
                    'added_items': 0,
                    'transactions': 0
                }
            
            # حجم الملف
            file_size = os.path.getsize(db_path)
            if file_size < 1024:
                size_str = f"{file_size} بايت"
            elif file_size < 1024 * 1024:
                size_str = f"{file_size / 1024:.1f} KB"
            else:
                size_str = f"{file_size / (1024 * 1024):.1f} MB"
            
            # الاتصال بقاعدة البيانات
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # عدد الجداول
            cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
            tables_count = cursor.fetchone()[0]
            
            # عدد المستفيدين
            try:
                cursor.execute("SELECT COUNT(*) FROM beneficiaries")
                beneficiaries = cursor.fetchone()[0]
            except:
                beneficiaries = 0
            
            # عدد عناصر الجدول التنظيمي
            try:
                cursor.execute("SELECT COUNT(*) FROM organizational_chart")
                org_chart = cursor.fetchone()[0]
            except:
                org_chart = 0
            
            # عدد عمليات الإضافة
            try:
                cursor.execute("SELECT COUNT(*) FROM added_items")
                added_items = cursor.fetchone()[0]
            except:
                added_items = 0
            
            # عدد عمليات الصرف
            try:
                cursor.execute("SELECT COUNT(*) FROM transactions")
                transactions = cursor.fetchone()[0]
            except:
                transactions = 0
            
            conn.close()
            
            return {
                'size': size_str,
                'tables_count': tables_count,
                'beneficiaries': beneficiaries,
                'org_chart': org_chart,
                'added_items': added_items,
                'transactions': transactions
            }
            
        except Exception as e:
            return {
                'size': f'خطأ: {e}',
                'tables_count': 0,
                'beneficiaries': 0,
                'org_chart': 0,
                'added_items': 0,
                'transactions': 0
            }
    
    def refresh_data(self):
        """تحديث البيانات"""
        # إعادة إنشاء النافذة لتحديث البيانات
        self.window.destroy()
        self.create_window()