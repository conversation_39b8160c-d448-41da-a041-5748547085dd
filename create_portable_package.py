#!/usr/bin/env python3
"""
إنشاء حزمة محمولة نهائية - نظام إدارة المخازن
Create Final Portable Package - Desktop Stores Management System
"""

import os
import shutil
from pathlib import Path
import time

def create_final_package():
    """إنشاء الحزمة المحمولة النهائية"""
    print("📦 إنشاء الحزمة المحمولة النهائية...")
    print("=" * 60)
    
    # مسارات المجلدات
    dist_dir = Path("dist/نظام_إدارة_المخازن")
    final_package_dir = Path("Desktop_Stores_Management_Portable")
    
    if not dist_dir.exists():
        print("❌ مجلد التطبيق المبني غير موجود!")
        return False
    
    # حذف الحزمة السابقة إن وجدت
    if final_package_dir.exists():
        shutil.rmtree(final_package_dir)
        print("🗑️ تم حذف الحزمة السابقة")
    
    # نسخ التطبيق المبني
    print("📁 نسخ ملفات التطبيق...")
    shutil.copytree(dist_dir, final_package_dir)
    
    # إنشاء مجلدات البيانات
    data_dirs = ["data", "reports", "backups", "logs"]
    for dir_name in data_dirs:
        dir_path = final_package_dir / dir_name
        dir_path.mkdir(exist_ok=True)
        
        # إنشاء ملف README في كل مجلد
        readme_content = {
            "data": "مجلد البيانات - يحتوي على قاعدة البيانات والملفات المهمة",
            "reports": "مجلد التقارير - يحتوي على التقارير المُصدرة", 
            "backups": "مجلد النسخ الاحتياطية - يحتوي على نسخ احتياطية من البيانات",
            "logs": "مجلد السجلات - يحتوي على ملفات سجل التطبيق"
        }
        
        readme_file = dir_path / "README.txt"
        readme_file.write_text(readme_content[dir_name], encoding='utf-8')
        print(f"✅ تم إنشاء مجلد: {dir_name}")
    
    # إنشاء ملف تشغيل سريع
    batch_content = """@echo off
chcp 65001 > nul
title نظام إدارة المخازن والمستودعات

echo ========================================
echo 🏪 نظام إدارة المخازن والمستودعات
echo ========================================
echo.
echo 🚀 بدء تشغيل التطبيق...
echo.

cd /d "%~dp0"

start "" "نظام_إدارة_المخازن.exe"

echo ✅ تم تشغيل التطبيق
echo يمكنك إغلاق هذه النافذة الآن
timeout /t 3 > nul
"""
    
    batch_file = final_package_dir / "تشغيل_البرنامج.bat"
    batch_file.write_text(batch_content, encoding='utf-8')
    print("✅ تم إنشاء ملف التشغيل السريع")
    
    # نسخ ملف README
    if Path("README.md").exists():
        shutil.copy2("README.md", final_package_dir / "اقرأني.md")
        print("✅ تم نسخ ملف التعليمات")
    
    # إنشاء ملف معلومات الإصدار
    version_info = f"""نظام إدارة المخازن والمستودعات
Desktop Stores Management System

الإصدار: 1.2.0
تاريخ البناء: {time.strftime('%Y-%m-%d %H:%M:%S')}
نوع البناء: Portable Package

المحتويات:
- نظام_إدارة_المخازن.exe: الملف التنفيذي الرئيسي
- تشغيل_البرنامج.bat: ملف تشغيل سريع
- data/: مجلد البيانات
- reports/: مجلد التقارير
- backups/: مجلد النسخ الاحتياطية
- logs/: مجلد السجلات
- _internal/: ملفات النظام (لا تحذف)

بيانات الدخول الافتراضية:
اسم المستخدم: admin
كلمة المرور: admin

ملاحظات:
- يمكن نقل هذا المجلد بالكامل إلى أي جهاز آخر
- لا يحتاج إلى تثبيت Python أو أي مكتبات إضافية
- جميع المتطلبات مدمجة في التطبيق

للدعم الفني:
- راجع ملف اقرأني.md
- تحقق من ملفات السجل في مجلد logs/
"""
    
    version_file = final_package_dir / "معلومات_الإصدار.txt"
    version_file.write_text(version_info, encoding='utf-8')
    print("✅ تم إنشاء ملف معلومات الإصدار")
    
    # حساب حجم الحزمة
    total_size = sum(f.stat().st_size for f in final_package_dir.rglob('*') if f.is_file())
    size_mb = total_size / (1024 * 1024)
    
    print("\n" + "=" * 60)
    print("🎉 تم إنشاء الحزمة المحمولة بنجاح!")
    print(f"📁 المجلد: {final_package_dir}")
    print(f"📊 الحجم: {size_mb:.1f} MB")
    print(f"📄 الملف التنفيذي: نظام_إدارة_المخازن.exe")
    print("🚀 يمكنك الآن نقل هذا المجلد إلى أي جهاز آخر")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    create_final_package()