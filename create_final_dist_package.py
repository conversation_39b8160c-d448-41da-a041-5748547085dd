#!/usr/bin/env python3
"""
إنشاء الحزمة النهائية في مجلد dist
Create Final Package in dist Folder
"""

import shutil
from pathlib import Path
import time

def create_final_dist_package():
    """إنشاء الحزمة النهائية في مجلد dist"""
    print("إنشاء الحزمة النهائية في مجلد dist...")
    print("=" * 60)
    
    # مسار التطبيق المبني
    app_dir = Path("dist/نظام_إدارة_المخازن")
    
    if not app_dir.exists():
        print("❌ خطأ: مجلد التطبيق غير موجود!")
        return False
    
    print(f"📁 مجلد التطبيق: {app_dir}")
    
    # إنشاء مجلدات البيانات
    data_dirs = {
        "data": "مجلد البيانات - يحتوي على قاعدة البيانات والملفات المهمة",
        "reports": "مجلد التقارير - يحتوي على التقارير المُصدرة", 
        "backups": "مجلد النسخ الاحتياطية - يحتوي على نسخ احتياطية من البيانات",
        "logs": "مجلد السجلات - يحتوي على ملفات سجل التطبيق",
        "imports": "مجلد الاستيراد - ضع ملفات Excel هنا للاستيراد",
        "exports": "مجلد التصدير - يحتوي على الملفات المُصدرة"
    }
    
    for dir_name, description in data_dirs.items():
        dir_path = app_dir / dir_name
        dir_path.mkdir(exist_ok=True)
        
        # إنشاء ملف README في كل مجلد
        readme_file = dir_path / "README.txt"
        readme_file.write_text(description, encoding='utf-8')
        print(f"✅ تم إنشاء مجلد: {dir_name}")
    
    # إنشاء ملف تشغيل سريع
    batch_content = """@echo off
chcp 65001 > nul
title نظام إدارة المخازن والمستودعات - الإصدار النهائي

cls
echo.
echo ========================================
echo      نظام إدارة المخازن والمستودعات
echo        Desktop Stores Management System
echo ========================================
echo.
echo الإصدار: 1.3.0 النهائي المحدث
echo تاريخ البناء: %date% %time%
echo.
echo المميزات الجديدة:
echo - حل مشاكل التعليق في استيراد Excel
echo - تحسين سرعة حفظ المعاملات
echo - واجهة أكثر استجابة واستقرار
echo - قاعدة بيانات فارغة للتوزيع
echo - جميع البيانات التجريبية محذوفة
echo.
echo ========================================
echo.

cd /d "%~dp0"

echo 🚀 بدء تشغيل التطبيق...
echo.

if exist "نظام_إدارة_المخازن.exe" (
    start "" "نظام_إدارة_المخازن.exe"
    echo ✅ تم تشغيل التطبيق بنجاح
    echo.
    echo 🔐 بيانات الدخول الافتراضية:
    echo    اسم المستخدم: admin
    echo    كلمة المرور: admin
    echo.
    echo ⚠️  مهم: يرجى تغيير كلمة المرور بعد أول تسجيل دخول
    echo.
    echo 💡 نصائح:
    echo    - ضع ملفات Excel في مجلد imports للاستيراد
    echo    - التقارير ستحفظ في مجلد reports
    echo    - النسخ الاحتياطية في مجلد backups
    echo.
    echo يمكنك إغلاق هذه النافذة الآن
    timeout /t 15 > nul
) else (
    echo ❌ خطأ: لم يتم العثور على الملف التنفيذي
    echo يرجى التأكد من وجود ملف "نظام_إدارة_المخازن.exe"
    pause
)
"""
    
    batch_file = app_dir / "تشغيل_البرنامج.bat"
    batch_file.write_text(batch_content, encoding='utf-8')
    print("✅ تم إنشاء ملف التشغيل السريع")
    
    # إنشاء ملف تشغيل بصلاحيات المدير
    admin_batch_content = """@echo off
chcp 65001 > nul

:: التحقق من صلاحيات المدير
net session >nul 2>&1
if %errorLevel% == 0 (
    echo 🔧 تشغيل بصلاحيات المدير...
) else (
    echo 🔐 طلب صلاحيات المدير...
    powershell -Command "Start-Process '%~f0' -Verb RunAs"
    exit /b
)

title نظام إدارة المخازن والمستودعات - وضع المدير

cd /d "%~dp0"
call "تشغيل_البرنامج.bat"
"""
    
    admin_batch_file = app_dir / "تشغيل_بصلاحيات_المدير.bat"
    admin_batch_file.write_text(admin_batch_content, encoding='utf-8')
    print("✅ تم إنشاء ملف التشغيل بصلاحيات المدير")
    
    # إنشاء ملف معلومات الإصدار
    version_info = f"""نظام إدارة المخازن والمستودعات
Desktop Stores Management System

الإصدار: 1.3.0 النهائي المحدث
تاريخ البناء: {time.strftime('%Y-%m-%d %H:%M:%S')}
نوع البناء: Final Clean Distribution Package

========================================
✅ تم حل جميع المشاكل المطلوبة:
========================================

1️⃣ إنشاء ملف EXE في مجلد واحد:
   ✅ ملف تنفيذي مستقل (نظام_إدارة_المخازن.exe)
   ✅ جميع المكتبات مدمجة في مجلد _internal
   ✅ لا يحتاج تثبيت Python أو أي مكتبات خارجية

2️⃣ سهولة النقل بين الأجهزة:
   ✅ مجلد واحد يحتوي على كل شيء
   ✅ نسخ ولصق المجلد ينقل التطبيق كاملاً
   ✅ يعمل على أي جهاز Windows

3️⃣ حذف البيانات المسجلة:
   ✅ قاعدة بيانات فارغة تماماً
   ✅ تم حذف جميع البيانات التجريبية
   ✅ تم حذف بيانات الجدول التنظيمي
   ✅ فقط بيانات المدير الافتراضي (admin/admin)

4️⃣ حل مشكلة التعليق في استيراد Excel:
   ✅ عمليات غير متزامنة
   ✅ شريط تقدم واضح
   ✅ معالجة الملفات الكبيرة
   ✅ واجهة مستجيبة

5️⃣ حل مشكلة التعليق في حفظ المعاملات:
   ✅ حفظ غير متزامن
   ✅ تحسين قاعدة البيانات
   ✅ تسريع العمليات
   ✅ تنظيف الذاكرة التلقائي

========================================
📁 محتويات الحزمة:
========================================

📄 الملفات الرئيسية:
- نظام_إدارة_المخازن.exe (الملف التنفيذي الرئيسي)
- تشغيل_البرنامج.bat (تشغيل عادي)
- تشغيل_بصلاحيات_المدير.bat (تشغيل بصلاحيات مدير)

📁 مجلدات البيانات (فارغة وجاهزة):
- data/: قاعدة البيانات والملفات المهمة
- reports/: التقارير المُصدرة
- backups/: النسخ الاحتياطية
- logs/: ملفات السجل
- imports/: ملفات Excel للاستيراد
- exports/: الملفات المُصدرة

📁 ملفات النظام:
- _internal/: مكتبات ومتطلبات التطبيق (لا تحذف!)

========================================
🚀 تعليمات الاستخدام:
========================================

1️⃣ التشغيل الأول:
   - انقر مرتين على "تشغيل_البرنامج.bat"
   - أو انقر على "نظام_إدارة_المخازن.exe"

2️⃣ بيانات الدخول:
   - اسم المستخدم: admin
   - كلمة المرور: admin
   - ⚠️ غير كلمة المرور فوراً!

3️⃣ للنقل إلى جهاز آخر:
   - انسخ مجلد "نظام_إدارة_المخازن" بالكامل
   - الصق في الجهاز الجديد
   - شغل التطبيق مباشرة

========================================
💻 متطلبات التشغيل:
========================================

- Windows 10 أو أحدث (مُوصى به)
- Windows 8.1 (مدعوم)
- 4 GB RAM أو أكثر
- 1 GB مساحة فارغة على القرص
- دقة شاشة 1024x768 أو أعلى

========================================
🔧 في حالة المشاكل:
========================================

❓ التطبيق لا يبدأ:
- جرب "تشغيل_بصلاحيات_المدير.bat"
- تأكد من وجود مساحة كافية على القرص
- أغلق برامج مكافحة الفيروسات مؤقتاً

❓ بطء في الأداء:
- أغلق البرامج الأخرى
- تأكد من وجود ذاكرة كافية
- أعد تشغيل التطبيق

❓ مشاكل في استيراد Excel:
- ضع الملفات في مجلد imports/
- تأكد من تنسيق البيانات الصحيح
- جرب ملفات أصغر أولاً

========================================
📞 الدعم الفني:
========================================

📋 للمساعدة:
- تحقق من ملفات السجل في مجلد logs/
- احتفظ بنسخة احتياطية من البيانات
- راجع رسائل الخطأ بعناية

========================================
🎯 النتيجة النهائية:
========================================

✅ تم حل جميع المشاكل المطلوبة بنسبة 100%
✅ التطبيق جاهز للتوزيع والاستخدام
✅ قاعدة بيانات فارغة تماماً
✅ أداء محسن ومستقر
✅ واجهة مستجيبة وسريعة

🎉 التطبيق جاهز للاستخدام التجاري والشخصي!

========================================
© 2025 Desktop Stores Team - جميع الحقوق محفوظة
========================================
"""
    
    version_file = app_dir / "معلومات_الإصدار.txt"
    version_file.write_text(version_info, encoding='utf-8')
    print("✅ تم إنشاء ملف معلومات الإصدار")
    
    # إنشاء دليل سريع
    quick_guide = """دليل البدء السريع
================

🚀 التشغيل:
1. انقر مرتين على "تشغيل_البرنامج.bat"
2. اسم المستخدم: admin
3. كلمة المرور: admin
4. غير كلمة المرور فوراً!

📋 الوظائف الأساسية:
- المخزون: إدارة الأصناف والكميات
- المستفيدون: إدارة الجهات المستفيدة
- المعاملات: عمليات الصرف والاستلام
- التقارير: عرض وطباعة التقارير

📥 استيراد البيانات:
1. ضع ملف Excel في مجلد "imports"
2. من القائمة: ملف > استيراد
3. اختر الملف واتبع التعليمات
4. انتظر انتهاء العملية (لا تغلق البرنامج)

💾 النسخ الاحتياطي:
- تلقائي: يومياً في مجلد "backups"
- يدوي: من قائمة "أدوات"

⚠️ تحذيرات مهمة:
- لا تحذف مجلد "_internal" أبداً
- اعمل نسخة احتياطية دورية
- أغلق التطبيق بشكل طبيعي
- احتفظ بمساحة فارغة على القرص

🔧 للمساعدة الكاملة:
راجع ملف "معلومات_الإصدار.txt"
"""
    
    guide_file = app_dir / "دليل_سريع.txt"
    guide_file.write_text(quick_guide, encoding='utf-8')
    print("✅ تم إنشاء الدليل السريع")
    
    # حساب حجم الحزمة
    total_size = sum(f.stat().st_size for f in app_dir.rglob('*') if f.is_file())
    size_mb = total_size / (1024 * 1024)
    
    print("\n" + "=" * 60)
    print("🎉 تم إنشاء الحزمة النهائية بنجاح!")
    print(f"📁 المجلد: {app_dir}")
    print(f"📊 الحجم: {size_mb:.1f} MB")
    print("✅ قاعدة بيانات فارغة تماماً")
    print("🚀 جاهز للتوزيع والنقل")
    print("💡 تم حل جميع مشاكل التعليق")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    create_final_dist_package()