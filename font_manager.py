#!/usr/bin/env python3
"""
مدير الخطوط - ضمان استخدام Arial في جميع أنحاء التطبيق
Font Manager - Ensure Arial is used throughout the application
"""

import tkinter as tk
from tkinter import font as tkfont

# الخط الثابت للتطبيق
FIXED_FONT_FAMILY = "Arial"
FIXED_FONT_SIZE = 12

def get_app_font(size=None, weight="normal"):
    """
    الحصول على خط التطبيق الثابت
    Get the fixed application font
    """
    if size is None:
        size = FIXED_FONT_SIZE
    
    return (FIXED_FONT_FAMILY, size, weight)

def get_default_font():
    """الحصول على الخط الافتراضي"""
    return get_app_font()

def get_bold_font(size=None):
    """الحصول على خط عريض"""
    return get_app_font(size, "bold")

def get_large_font():
    """الحصول على خط كبير"""
    return get_app_font(16, "bold")

def get_small_font():
    """الحصول على خط صغير"""
    return get_app_font(10)

def setup_default_fonts():
    """إعداد الخطوط الافتراضية للتطبيق"""
    try:
        # إنشاء نافذة مؤقتة للوصول إلى إعدادات الخط
        temp_root = tk.Tk()
        temp_root.withdraw()
        
        # تعيين الخط الافتراضي
        default_font = tkfont.nametofont("TkDefaultFont")
        default_font.configure(family=FIXED_FONT_FAMILY, size=FIXED_FONT_SIZE)
        
        # تعيين خط النص
        text_font = tkfont.nametofont("TkTextFont")
        text_font.configure(family=FIXED_FONT_FAMILY, size=FIXED_FONT_SIZE)
        
        # تعيين خط القوائم
        menu_font = tkfont.nametofont("TkMenuFont")
        menu_font.configure(family=FIXED_FONT_FAMILY, size=FIXED_FONT_SIZE)
        
        # تعيين خط الأزرار
        button_font = tkfont.nametofont("TkDefaultFont")
        button_font.configure(family=FIXED_FONT_FAMILY, size=FIXED_FONT_SIZE)
        
        temp_root.destroy()
        
        print(f"[نجح] تم تعيين الخط الافتراضي: {FIXED_FONT_FAMILY}")
        return True
        
    except Exception as e:
        print(f"[تحذير] تعذر تعيين الخط الافتراضي: {e}")
        return False

def force_arial_font():
    """فرض استخدام Arial في كل مكان"""
    try:
        # إعداد الخطوط الافتراضية
        setup_default_fonts()
        
        # تعيين متغيرات البيئة للخط
        import os
        os.environ['TK_DEFAULT_FONT'] = f"{FIXED_FONT_FAMILY} {FIXED_FONT_SIZE}"
        
        return True
        
    except Exception as e:
        print(f"[تحذير] تعذر فرض استخدام Arial: {e}")
        return False

# تطبيق الخط عند استيراد الوحدة
if __name__ != "__main__":
    force_arial_font()
