#!/usr/bin/env python3
"""
نافذة تسجيل دخول جديدة ومحسنة
New and Improved Login Window
"""

import tkinter as tk
import ttkbootstrap as ttk_bs
from tkinter import messagebox
import sys
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config import UI_CONFIG, APP_CONFIG

class NewLoginWindow:
    """نافذة تسجيل دخول جديدة ومحسنة"""
    
    def __init__(self, auth_manager):
        self.auth_manager = auth_manager
        self.on_login_success = None
        self.login_window = None
        self.username_var = None
        self.password_var = None
        self.login_button = None
        
        # إنشاء النافذة
        self.create_window()
    
    def create_window(self):
        """إنشاء النافذة"""
        try:
            # إنشاء نافذة جذر مستقلة
            self.login_window = ttk_bs.Window(
                title="تسجيل الدخول - نظام إدارة المخازن",
                themename="cosmo",
                size=(500, 650),
                resizable=(False, False)
            )
            
            # تعيين الخلفية البيضاء
            self.login_window.configure(bg="white")

            # إخفاء النافذة مؤقتاً أثناء الإعداد
            self.login_window.withdraw()

            # توسيط النافذة
            self.center_window()

            # إنشاء الواجهة
            self.create_interface()

            # إعداد الأحداث
            self.setup_events()

            print("[نجح] تم إنشاء نافذة تسجيل الدخول الجديدة")
            
        except Exception as e:
            print(f"[خطأ] فشل في إنشاء نافذة تسجيل الدخول: {e}")
            raise
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        try:
            self.login_window.update_idletasks()
            
            # أبعاد النافذة
            width = 500
            height = 650
            
            # أبعاد الشاشة
            screen_width = self.login_window.winfo_screenwidth()
            screen_height = self.login_window.winfo_screenheight()
            
            # حساب الموضع
            x = (screen_width - width) // 2
            y = (screen_height - height) // 2
            
            # تعيين الموضع
            self.login_window.geometry(f"{width}x{height}+{x}+{y}")
            
            print(f"[موضع] النافذة: {width}x{height}+{x}+{y}")
            
        except Exception as e:
            print(f"[تحذير] تعذر توسيط النافذة: {e}")
    
    def create_interface(self):
        """إنشاء واجهة المستخدم"""
        try:
            # إعداد الستايل للخلفية البيضاء
            style = ttk_bs.Style()
            style.configure("White.TFrame", background="white")
            
            # إطار رئيسي مع خلفية بيضاء
            main_frame = ttk_bs.Frame(self.login_window, padding=50, style="White.TFrame")
            main_frame.pack(fill="both", expand=True)
            
            # عنوان التطبيق بالأزرق الغامق
            title_label = ttk_bs.Label(
                main_frame,
                text="نظام إدارة المخازن والمستودعات",
                font=("Arial", 24, "bold"),
                foreground="#003366"  # أزرق غامق
            )
            title_label.pack(pady=(20, 20))
            
            # عنوان فرعي
            subtitle_label = ttk_bs.Label(
                main_frame,
                text="تسجيل الدخول إلى النظام",
                font=("Arial", 16),
                bootstyle="secondary"
            )
            subtitle_label.pack(pady=(0, 40))
            
            # حقل اسم المستخدم
            username_label = ttk_bs.Label(
                main_frame, 
                text="اسم المستخدم:",
                font=("Arial", 14)
            )
            username_label.pack(anchor="w", pady=(0, 10))
            
            self.username_var = tk.StringVar(value="admin")  # قيمة افتراضية للاختبار
            username_entry = ttk_bs.Entry(
                main_frame,
                textvariable=self.username_var,
                font=("Arial", 14),
                width=30
            )
            username_entry.pack(pady=(0, 25), ipady=10)
            username_entry.focus()  # التركيز على حقل اسم المستخدم
            
            # حقل كلمة المرور
            password_label = ttk_bs.Label(
                main_frame, 
                text="كلمة المرور:",
                font=("Arial", 14)
            )
            password_label.pack(anchor="w", pady=(0, 10))
            
            self.password_var = tk.StringVar(value="admin")  # قيمة افتراضية للاختبار
            password_entry = ttk_bs.Entry(
                main_frame,
                textvariable=self.password_var,
                font=("Arial", 14),
                width=30,
                show="*"
            )
            password_entry.pack(pady=(0, 40), ipady=10)
            
            # إطار الأزرار
            buttons_frame = ttk_bs.Frame(main_frame)
            buttons_frame.pack(pady=(0, 30))
            
            # زر تسجيل الدخول
            self.login_button = ttk_bs.Button(
                buttons_frame,
                text="دخول إلى النظام",
                command=self.handle_login,
                bootstyle="success",
                width=20
            )
            self.login_button.pack(pady=(0, 15), ipady=12)
            
            # زر دخول سريع (للاختبار)
            quick_login_button = ttk_bs.Button(
                buttons_frame,
                text="دخول سريع (admin)",
                command=self.quick_login,
                bootstyle="info",
                width=22
            )
            quick_login_button.pack(pady=(0, 15), ipady=10)
            
            # رسالة المساعدة
            help_label = ttk_bs.Label(
                main_frame,
                text="المستخدم الافتراضي: admin / admin",
                font=("Arial", 12),
                bootstyle="info"
            )
            help_label.pack(pady=(20, 0))
            
            # معلومات إضافية
            info_label = ttk_bs.Label(
                main_frame,
                text="نظام شامل لإدارة المخازن والمستودعات",
                font=("Arial", 10),
                bootstyle="secondary"
            )
            info_label.pack(pady=(15, 0))
            
            # حفظ مراجع للحقول
            self.username_entry = username_entry
            self.password_entry = password_entry
            
            print("[نجح] تم إنشاء واجهة المستخدم")
            
        except Exception as e:
            print(f"[خطأ] فشل في إنشاء الواجهة: {e}")
            raise
    
    def setup_events(self):
        """إعداد الأحداث"""
        try:
            # ربط Enter بتسجيل الدخول
            self.username_entry.bind("<Return>", lambda e: self.password_entry.focus())
            self.password_entry.bind("<Return>", lambda e: self.handle_login())
            
            # ربط Escape بالإغلاق
            self.login_window.bind("<Escape>", lambda e: self.login_window.quit())
            
            # معالج إغلاق النافذة
            self.login_window.protocol("WM_DELETE_WINDOW", self.on_window_close)
            
            print("[نجح] تم إعداد الأحداث")
            
        except Exception as e:
            print(f"[خطأ] فشل في إعداد الأحداث: {e}")
    
    def quick_login(self):
        """دخول سريع بالمستخدم الافتراضي"""
        try:
            print("[دخول سريع] استخدام admin/admin")
            self.username_var.set("admin")
            self.password_var.set("admin")
            self.handle_login()
        except Exception as e:
            print(f"[خطأ] في الدخول السريع: {e}")
    
    def handle_login(self):
        """معالج تسجيل الدخول"""
        try:
            # الحصول على البيانات
            username = self.username_var.get().strip()
            password = self.password_var.get().strip()
            
            print(f"[محاولة] تسجيل دخول: '{username}'")
            
            # التحقق من صحة البيانات
            if not username or not password:
                messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم وكلمة المرور\n\nالمستخدم الافتراضي: admin\nكلمة المرور الافتراضية: admin")
                return
            
            # تعطيل زر تسجيل الدخول مؤقتاً
            self.login_button.configure(state="disabled", text="جاري التحقق...")
            self.login_window.update()
            
            # محاولة المصادقة
            success, message, user = self.auth_manager.authenticate(username, password)
            
            print(f"[نتيجة] المصادقة: نجح={success}, رسالة={message}")
            
            if success and user:
                print(f"[نجح] تسجيل الدخول: {user.username} ({user.full_name})")
                
                # عرض رسالة نجاح
                messagebox.showinfo("نجح", f"مرحباً {user.full_name}!\nتم تسجيل الدخول بنجاح")
                
                print("[تتبع] سيتم استدعاء دالة النجاح...")
                
                # استدعاء دالة النجاح
                if self.on_login_success:
                    print("[تتبع] استدعاء on_login_success...")
                    self.on_login_success(user)
                    print("[تتبع] تم استدعاء on_login_success بنجاح")
                else:
                    print("[تحذير] on_login_success غير محدد!")
                
                print("[تتبع] سيتم إغلاق نافذة تسجيل الدخول...")
                
                # إغلاق نافذة تسجيل الدخول
                self.login_window.quit()
                self.login_window.destroy()
                
                print("[تتبع] تم إغلاق نافذة تسجيل الدخول")
                
            else:
                print(f"[فشل] تسجيل الدخول: {message}")
                messagebox.showerror("فشل", message or "بيانات الدخول غير صحيحة")
                
                # مسح كلمة المرور
                self.password_var.set("")
                self.password_entry.focus()
                
                # إعادة تفعيل الزر
                self.login_button.configure(state="normal", text="تسجيل الدخول")
                
        except Exception as e:
            print(f"[خطأ] في تسجيل الدخول: {e}")
            import traceback
            traceback.print_exc()
            messagebox.showerror("خطأ", f"حدث خطأ في تسجيل الدخول:\n{str(e)}")
            
            # إعادة تفعيل الزر
            self.login_button.configure(state="normal", text="تسجيل الدخول")
    
    def on_window_close(self):
        """معالج إغلاق النافذة"""
        try:
            self.login_window.quit()
            self.login_window.destroy()
        except:
            pass
    
    def show(self):
        """عرض النافذة"""
        try:
            print("[عرض] نافذة تسجيل الدخول")
            
            # إظهار النافذة
            self.login_window.deiconify()
            
            # رفع النافذة للمقدمة
            self.login_window.lift()
            self.login_window.focus_force()
            
            # تشغيل حلقة الأحداث
            self.login_window.mainloop()
            
        except Exception as e:
            print(f"[خطأ] في عرض النافذة: {e}")
            raise
    
    def hide(self):
        """إخفاء النافذة"""
        try:
            if self.login_window:
                self.login_window.withdraw()
        except:
            pass
    
    def destroy(self):
        """تدمير النافذة"""
        try:
            if self.login_window:
                self.login_window.destroy()
                self.login_window = None
        except:
            pass

# اختبار النافذة
if __name__ == "__main__":
    # استيراد مدير المصادقة للاختبار
    sys.path.insert(0, str(Path(__file__).parent.parent))
    from auth_manager import AuthManager
    
    def test_login_success(user):
        print(f"[اختبار] نجح تسجيل الدخول: {user.username}")
    
    # إنشاء مدير المصادقة
    auth_manager = AuthManager()
    
    # إنشاء النافذة
    login_window = NewLoginWindow(auth_manager)
    login_window.on_login_success = test_login_success
    
    # عرض النافذة
    login_window.show()