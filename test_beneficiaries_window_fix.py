#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار إصلاح شاشة المستفيدين
Test Beneficiaries Window Fix
"""

import os
import sys
import tempfile
import pandas as pd

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_beneficiaries_window_import():
    """اختبار استيراد المستفيدين من الشاشة الأساسية"""
    print("🧪 اختبار استيراد المستفيدين من الشاشة الأساسية...")
    
    try:
        # إنشاء ملف اختبار
        test_data = [
            {
                'الاسم': 'مستفيد اختبار الشاشة',
                'الرقم العام': 'WINDOW_TEST001',
                'الرتبة': 'نقيب',
                'الإدارة': 'إدارة الاختبار',
                'الوحدة': 'وحدة الاختبار'
            }
        ]
        
        # إنشاء ملف مؤقت
        temp_file = tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False)
        temp_path = temp_file.name
        temp_file.close()
        
        df = pd.DataFrame(test_data)
        df.to_excel(temp_path, index=False)
        
        print(f"📁 تم إنشاء ملف الاختبار: {temp_path}")
        
        # محاكاة استيراد من الشاشة
        from ui.beneficiaries_window import BeneficiariesWindow
        from ui.main_window import MainWindow
        
        # إنشاء نافذة رئيسية وهمية
        import ttkbootstrap as ttk_bs
        root = ttk_bs.Window(themename="cosmo")
        root.withdraw()  # إخفاء النافذة الرئيسية
        
        main_window = MainWindow()
        main_window.window = root
        
        # إنشاء شاشة المستفيدين
        beneficiaries_window = BeneficiariesWindow(main_window)
        
        # محاكاة عملية الاستيراد
        print("🔄 محاكاة عملية الاستيراد...")
        
        # استدعاء دالة الاستيراد مع تمرير مسار الملف مباشرة
        from utils.excel_import_manager import ExcelImportManager
        
        def progress_callback(progress, message):
            print(f"   📊 {progress:.1f}% - {message}")
        
        def cancel_check():
            return False
        
        result = ExcelImportManager.import_beneficiaries_from_excel(
            temp_path,
            progress_callback=progress_callback,
            cancel_check=cancel_check
        )
        
        print(f"📊 نتائج الاستيراد:")
        print(f"   ✅ نجح: {result.success_count}")
        print(f"   🔄 مكرر: {result.duplicate_count}")
        print(f"   ❌ أخطاء: {result.error_count}")
        
        # تنظيف
        os.unlink(temp_path)
        root.destroy()
        
        # تنظيف البيانات التجريبية
        from database import db_manager
        db_manager.execute_query("DELETE FROM beneficiaries WHERE number LIKE 'WINDOW_TEST%'")
        
        return result.success_count > 0
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الشاشة: {e}")
        import traceback
        print(f"📋 تفاصيل الخطأ: {traceback.format_exc()}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 اختبار إصلاح شاشة المستفيدين")
    print("=" * 50)
    
    success = test_beneficiaries_window_import()
    
    print("\n" + "=" * 50)
    print("📊 نتيجة الاختبار")
    print("=" * 50)
    
    if success:
        print("🎉 الاختبار نجح!")
        print("✅ إصلاح شاشة المستفيدين يعمل بشكل صحيح")
        print("✅ يمكنك الآن استخدام زر الاستيراد في الشاشة الأساسية")
    else:
        print("❌ الاختبار فشل!")
        print("⚠️ هناك مشكلة في إصلاح الشاشة")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
