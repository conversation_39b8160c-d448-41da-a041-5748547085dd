#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لإصلاح مشاكل الاستيراد في كلا الشاشتين
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import db_manager
from models import OrganizationalChart, Beneficiary
import pandas as pd
import tempfile

def test_organizational_chart_import():
    """اختبار استيراد الجدول التنظيمي"""
    print("🧪 اختبار استيراد الجدول التنظيمي...")
    
    try:
        # إنشاء بيانات تجريبية
        test_data = {
            'اسم الصنف': ['اختبار جدول 1', 'اختبار جدول 2'],
            'رقم الصنف': ['ORG001', 'ORG002'],
            'اسم المعدة': ['معدة جدول 1', 'معدة جدول 2'],
            'الكمية': [15, 25],
            'الملاحظات': ['ملاحظة جدول 1', 'ملاحظة جدول 2']
        }
        
        df = pd.DataFrame(test_data)
        
        # حفظ الملف مؤقتاً
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as temp_file:
            temp_file_path = temp_file.name
            df.to_excel(temp_file_path, index=False, engine='openpyxl')
        
        # تنظيف البيانات التجريبية السابقة
        db_manager.execute_query("DELETE FROM organizational_chart WHERE item_code LIKE 'ORG%'")
        
        # فحص الحالة قبل الاستيراد
        before_count = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart WHERE is_active = 1")[0]
        print(f"📊 البيانات النشطة قبل الاستيراد: {before_count}")
        
        # تنفيذ الاستيراد
        from utils.organizational_chart_import import import_organizational_chart_from_excel
        
        def progress_callback(progress, message):
            print(f"   📊 {progress:.1f}% - {message}")
        
        def cancel_check():
            return False
        
        result = import_organizational_chart_from_excel(
            temp_file_path, 
            progress_callback=progress_callback,
            cancel_check=cancel_check
        )
        
        print(f"📊 نتائج الاستيراد: نجح {result.success_count}, مكرر {result.duplicate_count}, أخطاء {result.error_count}")
        
        # تطبيق الإصلاحات
        print("🔧 تطبيق إصلاحات التفعيل...")
        
        # إصلاح شامل للتفعيل
        activated_count = db_manager.execute_query("""
            UPDATE organizational_chart
            SET is_active = 1
            WHERE is_active = 0
            AND (created_at > datetime('now', '-5 minutes') OR id IN (
                SELECT id FROM organizational_chart 
                WHERE is_active = 0 
                ORDER BY id DESC 
                LIMIT ?
            ))
        """, (result.success_count,)).rowcount
        
        print(f"✅ تم تفعيل {activated_count} عنصر")
        
        # فحص النتائج
        after_count = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart WHERE is_active = 1")[0]
        items = OrganizationalChart.get_all(active_only=True)
        test_items = [item for item in items if item.item_code and item.item_code.startswith('ORG')]
        
        print(f"📊 البيانات النشطة بعد الإصلاح: {after_count}")
        print(f"🧪 العناصر التجريبية المرئية: {len(test_items)}")
        
        # تنظيف
        db_manager.execute_query("DELETE FROM organizational_chart WHERE item_code LIKE 'ORG%'")
        
        try:
            os.unlink(temp_file_path)
        except:
            pass
        
        success = (result.success_count > 0 and len(test_items) > 0)
        print(f"{'✅' if success else '❌'} اختبار الجدول التنظيمي: {'نجح' if success else 'فشل'}")
        
        return success
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الجدول التنظيمي: {e}")
        return False

def test_beneficiaries_import():
    """اختبار استيراد المستفيدين"""
    print("\n🧪 اختبار استيراد المستفيدين...")
    
    try:
        # إنشاء بيانات تجريبية
        test_data = {
            'الاسم': ['مستفيد تجريبي 1', 'مستفيد تجريبي 2'],
            'الرقم العام': ['BEN001', 'BEN002'],
            'الرتبة': ['رتبة 1', 'رتبة 2'],
            'الإدارة': ['إدارة تجريبية', 'إدارة تجريبية'],
            'الوحدة': ['وحدة تجريبية', 'وحدة تجريبية']
        }
        
        df = pd.DataFrame(test_data)
        
        # حفظ الملف مؤقتاً
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as temp_file:
            temp_file_path = temp_file.name
            df.to_excel(temp_file_path, index=False, engine='openpyxl')
        
        # تنظيف البيانات التجريبية السابقة
        db_manager.execute_query("DELETE FROM beneficiaries WHERE number LIKE 'BEN%'")
        
        # فحص الحالة قبل الاستيراد
        before_count = db_manager.fetch_one("SELECT COUNT(*) FROM beneficiaries WHERE is_active = 1")[0]
        print(f"📊 المستفيدين النشطين قبل الاستيراد: {before_count}")
        
        # تنفيذ الاستيراد
        from utils.excel_import_manager import ExcelImportManager
        
        def progress_callback(progress, message):
            print(f"   📊 {progress:.1f}% - {message}")
        
        def cancel_check():
            return False
        
        result = ExcelImportManager.import_beneficiaries_from_excel(
            temp_file_path, 
            progress_callback=progress_callback,
            cancel_check=cancel_check
        )
        
        print(f"📊 نتائج الاستيراد: نجح {result.success_count}, مكرر {result.duplicate_count}, أخطاء {result.error_count}")
        
        # فحص النتائج
        after_count = db_manager.fetch_one("SELECT COUNT(*) FROM beneficiaries WHERE is_active = 1")[0]
        beneficiaries = Beneficiary.get_all()
        test_beneficiaries = [ben for ben in beneficiaries if ben.number and ben.number.startswith('BEN')]
        
        print(f"📊 المستفيدين النشطين بعد الاستيراد: {after_count}")
        print(f"🧪 المستفيدين التجريبيين المرئيين: {len(test_beneficiaries)}")
        
        # تنظيف
        db_manager.execute_query("DELETE FROM beneficiaries WHERE number LIKE 'BEN%'")
        
        try:
            os.unlink(temp_file_path)
        except:
            pass
        
        success = (result.success_count > 0 and len(test_beneficiaries) > 0)
        print(f"{'✅' if success else '❌'} اختبار المستفيدين: {'نجح' if success else 'فشل'}")
        
        return success
        
    except Exception as e:
        print(f"❌ خطأ في اختبار المستفيدين: {e}")
        return False

def main():
    """تشغيل الاختبارات الشاملة"""
    print("🚀 بدء الاختبارات الشاملة لإصلاح مشاكل الاستيراد")
    print("=" * 70)
    
    # اختبار الجدول التنظيمي
    org_success = test_organizational_chart_import()
    
    # اختبار المستفيدين
    ben_success = test_beneficiaries_import()
    
    print("\n" + "=" * 70)
    print("📋 ملخص النتائج:")
    print(f"   🗂️ الجدول التنظيمي: {'✅ نجح' if org_success else '❌ فشل'}")
    print(f"   👥 المستفيدين: {'✅ نجح' if ben_success else '❌ فشل'}")
    
    if org_success and ben_success:
        print("\n🎉 جميع الإصلاحات نجحت!")
        print("💡 الآن يجب أن تعمل عمليات الاستيراد في كلا الشاشتين")
    elif org_success or ben_success:
        print("\n⚠️ بعض الإصلاحات نجحت والأخرى تحتاج مزيد من العمل")
    else:
        print("\n❌ جميع الإصلاحات فشلت - يحتاج إلى مزيد من التحقيق")
    
    return org_success and ben_success

if __name__ == "__main__":
    main()
