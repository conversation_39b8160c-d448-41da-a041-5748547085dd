#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
شاشة إدارة الأقسام
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from typing import List, Optional
import sys
import os
import pandas as pd

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models import Section, Department
from database import db_manager

class SectionsWindow:
    """شاشة إدارة الأقسام"""
    
    def __init__(self, parent, main_window=None):
        self.parent = parent
        self.main_window = main_window
        self.current_section = None
        self.sections_data = []
        self.departments_data = []
        
        self.setup_window()
        self.create_widgets()
        self.load_data()
    
    def setup_window(self):
        """إعداد النافذة"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("إدارة الأقسام")
        self.window.geometry("1430x700")
        self.window.resizable(True, True)
        
        # توسيط النافذة
        self.center_window()
        
        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        self.window.grab_set()
        self.window.lift()
        self.window.focus_force()
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        x = (screen_width - 1430) // 2
        y = (screen_height - 700) // 2
        self.window.geometry(f"1430x700+{x}+{y}")
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.window)
        main_frame.pack(fill=BOTH, expand=True, padx=20, pady=20)

        # العنوان
        self.create_header(main_frame)

        # نموذج الإدخال
        self.create_input_form(main_frame)

        # جدول البيانات
        self.create_data_table(main_frame)

        # شريط الحالة
        self.create_status_bar(main_frame)
    
    def create_header(self, parent):
        """إنشاء العنوان"""
        header_frame = ttk_bs.Frame(parent)
        header_frame.pack(fill=X, pady=(0, 20))

        # العنوان
        title_label = ttk_bs.Label(
            header_frame,
            text="📂 إدارة الأقسام",
            bootstyle="primary"
        )
        title_label.pack()

    def create_input_form(self, parent):
        """إنشاء نموذج الإدخال"""
        # إطار النموذج
        form_frame = ttk_bs.LabelFrame(parent, text="إضافة قسم جديد", padding=15)
        form_frame.pack(fill=X, pady=(0, 20))

        # إطار الحقول
        fields_frame = ttk_bs.Frame(form_frame)
        fields_frame.pack(fill=X)

        # متغيرات النموذج
        self.department_var = tk.StringVar()
        self.section_name_var = tk.StringVar()

        # قائمة الإدارات
        ttk_bs.Label(fields_frame, text="الإدارة:").pack(side=LEFT, padx=(0, 10))

        self.department_combo = ttk_bs.Combobox(
            fields_frame,
            textvariable=self.department_var,
            state="readonly",
            width=50
        )
        self.department_combo.pack(side=LEFT, padx=(0, 15))

        # حقل اسم القسم
        ttk_bs.Label(fields_frame, text="اسم القسم:").pack(side=LEFT, padx=(0, 10))

        name_entry = ttk_bs.Entry(
            fields_frame,
            textvariable=self.section_name_var,
            width=50
        )
        name_entry.pack(side=LEFT, padx=(0, 15))

        # زر الحفظ
        save_btn = ttk_bs.Button(
            fields_frame,
            text="💾 حفظ القسم",
            command=self.save_section,
            bootstyle="success",
            width=18
        )
        save_btn.pack(side=LEFT, padx=(0, 10))

        # زر مسح
        clear_btn = ttk_bs.Button(
            fields_frame,
            text="🗑️ مسح النموذج",
            command=self.clear_form,
            bootstyle="secondary",
            width=18
        )
        clear_btn.pack(side=LEFT, padx=(0, 10))
        
        # زر استيراد من Excel
        import_btn = ttk_bs.Button(
            fields_frame,
            text="📥 استيراد من إكسل",
            command=self.import_from_excel,
            bootstyle="info",
            width=20
        )
        import_btn.pack(side=LEFT, padx=(0, 10))
        
        # زر تحميل نموذج Excel
        template_btn = ttk_bs.Button(
            fields_frame,
            text="📄 تحميل نموذج إكسل",
            command=self.download_template,
            bootstyle="warning",
            width=22
        )
        template_btn.pack(side=LEFT)
    
    def create_data_table(self, parent):
        """إنشاء جدول البيانات"""
        # إطار الجدول
        table_frame = ttk_bs.LabelFrame(parent, text="قائمة الأقسام", padding=10)
        table_frame.pack(fill=BOTH, expand=True, pady=(0, 10))

        # إنشاء Treeview
        columns = ("id", "department", "name", "status")
        self.tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)

        # تعيين عناوين الأعمدة
        self.tree.heading("id", text="التسلسل")
        self.tree.heading("department", text="الإدارة")
        self.tree.heading("name", text="اسم القسم")
        self.tree.heading("status", text="الحالة")

        # تعيين عرض الأعمدة
        self.tree.column("id", width=100, anchor=CENTER)
        self.tree.column("department", width=200, anchor=E)
        self.tree.column("name", width=300, anchor=E)
        self.tree.column("status", width=150, anchor=CENTER)

        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient=VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)

        # تخطيط الجدول
        self.tree.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar.pack(side=RIGHT, fill=Y)

        # ربط الأحداث
        self.tree.bind("<<TreeviewSelect>>", self.on_item_select)
        self.tree.bind("<Button-3>", self.show_context_menu)  # النقر بالزر الأيمن
    
    def create_status_bar(self, parent):
        """إنشاء شريط الحالة"""
        self.status_bar = ttk_bs.Label(
            parent,
            text="جاهز",
            relief=SUNKEN,
            anchor=W
        )
        self.status_bar.pack(fill=X, side=BOTTOM)
    
    def load_data(self):
        """تحميل البيانات مرتبة حسب التسلسل"""
        try:
            self.sections_data = Section.get_all(active_only=False)
            self.departments_data = Department.get_all(active_only=False)
            # ترتيب البيانات حسب ID من الأصغر إلى الأكبر
            self.sections_data.sort(key=lambda x: x.id or 0)
            self.departments_data.sort(key=lambda x: x.id or 0)
            self.populate_table()
            self.populate_departments_combo()
            self.update_status(f"تم تحميل {len(self.sections_data)} قسم (مرتب حسب التسلسل)")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل البيانات: {e}")
            self.update_status("خطأ في تحميل البيانات")

    def populate_table(self):
        """ملء الجدول بالبيانات"""
        # مسح البيانات الموجودة
        for item in self.tree.get_children():
            self.tree.delete(item)

        # إضافة البيانات الجديدة
        for section in self.sections_data:
            # البحث عن اسم الإدارة
            department_name = ""
            if section.department_id:
                dept = next((d for d in self.departments_data if d.id == section.department_id), None)
                if dept:
                    department_name = dept.name

            status = "نشط" if section.is_active else "غير نشط"
            self.tree.insert("", "end", values=(
                section.id,
                department_name,
                section.name,
                status
            ))

    def populate_departments_combo(self):
        """ملء قائمة الإدارات المنسدلة"""
        dept_names = ["-- اختر الإدارة --"] + [dept.name for dept in self.departments_data if dept.is_active]
        self.department_combo['values'] = dept_names
        self.department_combo.set("-- اختر الإدارة --")
    
    def on_item_select(self, event):
        """معالج اختيار عنصر من الجدول"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            section_id = item['values'][0]
            self.current_section = next(
                (s for s in self.sections_data if s.id == section_id), None
            )

    def show_context_menu(self, event):
        """عرض القائمة المنبثقة"""
        # تحديد العنصر المحدد
        item = self.tree.identify_row(event.y)
        if item:
            self.tree.selection_set(item)
            self.on_item_select(event)

            # إنشاء القائمة المنبثقة
            context_menu = tk.Menu(self.window, tearoff=0)
            context_menu.add_command(label="✏️ تعديل القسم", command=self.edit_section)
            context_menu.add_command(label="🗑️ حذف القسم", command=self.delete_section)

            # عرض القائمة
            try:
                context_menu.tk_popup(event.x_root, event.y_root)
            finally:
                context_menu.grab_release()

    def save_section(self):
        """حفظ قسم جديد"""
        department_name = self.department_var.get()
        section_name = self.section_name_var.get().strip()

        if department_name == "-- اختر الإدارة --":
            messagebox.showerror("خطأ", "يرجى اختيار الإدارة")
            return

        if not section_name:
            messagebox.showerror("خطأ", "يرجى إدخال اسم القسم")
            return

        try:
            # البحث عن معرف الإدارة
            department_id = None
            for dept in self.departments_data:
                if dept.name == department_name:
                    department_id = dept.id
                    break

            # إنشاء قسم جديد
            section = Section(name=section_name, department_id=department_id, is_active=True)

            if section.save():
                self.show_success_message("تم إضافة القسم بنجاح")
                self.clear_form()
                self.refresh_data()
            else:
                messagebox.showerror("خطأ", "فشل في إضافة القسم")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ: {e}")

    def clear_form(self):
        """مسح النموذج"""
        self.department_var.set("-- اختر الإدارة --")
        self.section_name_var.set("")
    
    def edit_section(self):
        """تعديل القسم المحدد"""
        if not self.current_section:
            messagebox.showwarning("تحذير", "يرجى اختيار قسم للتعديل")
            return

        # نافذة تعديل بسيطة
        dialog = SectionEditDialog(self.window, "تعديل القسم", self.current_section, self.departments_data)
        if dialog.result:
            self.current_section.name = dialog.result['name']
            self.current_section.department_id = dialog.result['department_id']
            self.current_section.is_active = dialog.result['is_active']

            if self.current_section.save():
                self.show_success_message("تم تحديث القسم بنجاح")
                self.refresh_data()
            else:
                messagebox.showerror("خطأ", "فشل في تحديث القسم")
    
    def delete_section(self):
        """حذف القسم المحدد"""
        if not self.current_section:
            messagebox.showwarning("تحذير", "يرجى اختيار قسم للحذف")
            return

        # التحقق من وجود بيانات مرتبطة
        if not self.check_section_can_be_deleted():
            return

        if messagebox.askyesno("تأكيد الحذف",
                              f"هل تريد حذف القسم '{self.current_section.name}' نهائياً؟\n"
                              "هذا الإجراء لا يمكن التراجع عنه وسيتم حذف القسم من قاعدة البيانات."):
            if self.current_section.delete():
                self.show_success_message("تم حذف القسم نهائياً بنجاح")
                self.refresh_data()
            else:
                messagebox.showerror("خطأ", "فشل في حذف القسم")

    def check_section_can_be_deleted(self):
        """التحقق من إمكانية حذف القسم"""
        try:
            section_id = self.current_section.id
            print(f"فحص إمكانية حذف القسم ID: {section_id}")

            # التحقق من وجود مستفيدين مرتبطين بالقسم
            try:
                beneficiaries_count = db_manager.fetch_one(
                    "SELECT COUNT(*) as count FROM beneficiaries WHERE section_id = ? AND is_active = 1",
                    (section_id,)
                )
                count = beneficiaries_count['count'] if beneficiaries_count else 0
                print(f"عدد المستفيدين المرتبطين: {count}")

                if count > 0:
                    messagebox.showerror(
                        "لا يمكن الحذف",
                        f"لا يمكن حذف القسم '{self.current_section.name}'\n"
                        f"يوجد {count} مستفيد مرتبط بهذا القسم\n\n"
                        "يرجى نقل أو حذف المستفيدين أولاً"
                    )
                    return False
            except Exception as e:
                print(f"خطأ في فحص المستفيدين: {e}")

            print("يمكن حذف القسم")
            return True

        except Exception as e:
            print(f"خطأ عام في التحقق من الحذف: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ أثناء التحقق من إمكانية الحذف: {e}")
            return False
    
    def refresh_data(self):
        """تحديث البيانات"""
        self.load_data()
    
    def update_status(self, message):
        """تحديث شريط الحالة"""
        self.status_bar.config(text=message)

    def download_template(self):
        """تحميل نموذج Excel للأقسام"""
        try:
            print("[تتبع] بداية تنفيذ download_template")
            
            # التحقق من وجود المكتبات المطلوبة
            try:
                import pandas as pd
                import openpyxl
                print("[تتبع] تم استيراد pandas و openpyxl بنجاح")
            except ImportError as e:
                error_msg = f"المكتبات المطلوبة غير متوفرة: {e}\n\nيرجى تثبيت:\npip install pandas openpyxl"
                print(f"[خطأ] {error_msg}")
                messagebox.showerror("مكتبات مفقودة", error_msg)
                return
            
            # اختيار مكان الحفظ
            print("[تتبع] فتح نافذة اختيار مكان الحفظ")
            file_path = filedialog.asksaveasfilename(
                title="حفظ نموذج الأقسام",
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                initialfile="نموذج_الأقسام.xlsx"
            )
            
            if not file_path:
                print("[تتبع] تم إلغاء العملية من قبل المستخدم")
                return
            
            print(f"[تتبع] مسار الحفظ المحدد: {file_path}")
            
            # الحصول على الإدارات الموجودة في النظام
            existing_departments = []
            try:
                print("[تتبع] محاولة الحصول على الإدارات من قاعدة البيانات")
                departments = Department.get_all(active_only=True)
                existing_departments = [dept.name for dept in departments]
                print(f"[تتبع] تم العثور على {len(existing_departments)} إدارة")
            except Exception as e:
                print(f"[تحذير] فشل في الحصول على الإدارات: {e}")
                # في حالة عدم وجود إدارات، استخدام أمثلة افتراضية
                existing_departments = [
                    'الإدارة المالية',
                    'الإدارة الإدارية', 
                    'الإدارة التقنية',
                    'الإدارة التجارية'
                ]
                print("[تتبع] استخدام الإدارات الافتراضية")
            
            # إنشاء بيانات النموذج مع أمثلة متنوعة
            print("[تتبع] إنشاء بيانات النموذج")
            template_data = {
                'اسم القسم': [
                    'قسم المحاسبة',
                    'قسم الموارد البشرية',
                    'قسم تقنية المعلومات',
                    'قسم المشتريات',
                    'قسم المبيعات',
                    'قسم التسويق',
                    'قسم خدمة العملاء',
                    'قسم الجودة',
                    'قسم الأمن والسلامة',
                    'قسم التدريب والتطوير'
                ],
                'الإدارة': [
                    existing_departments[0] if len(existing_departments) > 0 else 'الإدارة المالية',
                    existing_departments[1] if len(existing_departments) > 1 else 'الإدارة الإدارية',
                    existing_departments[2] if len(existing_departments) > 2 else 'الإدارة التقنية',
                    existing_departments[0] if len(existing_departments) > 0 else 'الإدارة المالية',
                    existing_departments[3] if len(existing_departments) > 3 else 'الإدارة التجارية',
                    existing_departments[3] if len(existing_departments) > 3 else 'الإدارة التجارية',
                    existing_departments[1] if len(existing_departments) > 1 else 'الإدارة الإدارية',
                    existing_departments[2] if len(existing_departments) > 2 else 'الإدارة التقنية',
                    existing_departments[1] if len(existing_departments) > 1 else 'الإدارة الإدارية',
                    existing_departments[1] if len(existing_departments) > 1 else 'الإدارة الإدارية'
                ],
                'ملاحظات': [
                    'قسم مسؤول عن الحسابات والمالية العامة',
                    'قسم مسؤول عن شؤون الموظفين والتوظيف',
                    'قسم مسؤول عن الأنظمة والبرمجيات والدعم التقني',
                    'قسم مسؤول عن المشتريات والتوريد والمخازن',
                    'قسم مسؤول عن المبيعات المباشرة والعلاقات التجارية',
                    'قسم مسؤول عن التسويق والإعلان والعلاقات العامة',
                    'قسم مسؤول عن دعم العملاء وحل المشاكل',
                    'قسم مسؤول عن ضمان الجودة ومراقبة المعايير',
                    'قسم مسؤول عن الأمن والسلامة المهنية',
                    'قسم مسؤول عن التدريب والتطوير المهني'
                ]
            }
            
            # إنشاء DataFrame وحفظه
            print("[تتبع] إنشاء DataFrame")
            df = pd.DataFrame(template_data)
            print(f"[تتبع] حجم DataFrame: {df.shape}")
            
            print("[تتبع] حفظ الملف")
            df.to_excel(file_path, index=False, engine='openpyxl')
            
            # التحقق من إنشاء الملف
            from pathlib import Path
            if Path(file_path).exists():
                file_size = Path(file_path).stat().st_size
                print(f"[نجح] تم إنشاء الملف بحجم {file_size} بايت")
            else:
                raise Exception("فشل في إنشاء الملف")
            
            # رسالة نجاح مفصلة
            success_message = (
                f"✅ تم إنشاء نموذج الأقسام بنجاح!\n\n"
                f"📁 مكان الحفظ:\n{file_path}\n\n"
                f"📊 محتويات النموذج:\n"
                f"• {len(template_data['اسم القسم'])} قسم كمثال\n"
                f"• {len(existing_departments)} إدارة من النظام\n"
                f"• أعمدة: اسم القسم، الإدارة، ملاحظات\n\n"
                f"💡 تعليمات:\n"
                f"1. افتح الملف في Excel\n"
                f"2. عدل البيانات حسب احتياجاتك\n"
                f"3. احفظ الملف\n"
                f"4. استخدم زر 'استيراد من Excel' لرفع البيانات"
            )
            
            print("[نجح] عرض رسالة النجاح")
            messagebox.showinfo("تم إنشاء النموذج بنجاح", success_message)
            
            # تحديث شريط الحالة
            self.update_status("تم إنشاء نموذج Excel بنجاح")
            
        except PermissionError as e:
            error_msg = f"ليس لديك صلاحية للكتابة في هذا المكان\n\nتفاصيل الخطأ: {e}\n\nحلول مقترحة:\n• اختر مجلد آخر\n• تأكد من عدم فتح ملف بنفس الاسم\n• شغل البرنامج كمدير"
            print(f"[خطأ] {error_msg}")
            messagebox.showerror("خطأ في الصلاحيات", error_msg)
            self.update_status("فشل في إنشاء النموذج - خطأ في الصلاحيات")
            
        except FileNotFoundError as e:
            error_msg = f"المسار المحدد غير موجود\n\nتفاصيل الخطأ: {e}\n\nحلول مقترحة:\n• تأكد من صحة المسار\n• اختر مجلد موجود"
            print(f"[خطأ] {error_msg}")
            messagebox.showerror("خطأ في المسار", error_msg)
            self.update_status("فشل في إنشاء النموذج - مسار غير صحيح")
            
        except Exception as e:
            error_msg = f"فشل في إنشاء النموذج: {e}\n\nتأكد من:\n• وجود مساحة كافية على القرص\n• صلاحيات الكتابة في المجلد المحدد\n• عدم فتح ملف بنفس الاسم\n• تثبيت المكتبات المطلوبة"
            print(f"[خطأ] {error_msg}")
            import traceback
            print(f"[تفاصيل الخطأ] {traceback.format_exc()}")
            messagebox.showerror("خطأ", error_msg)
            self.update_status("فشل في إنشاء النموذج")

    def import_from_excel(self):
        """استيراد الأقسام من ملف Excel"""
        try:
            # اختيار الملف
            file_path = filedialog.askopenfilename(
                title="اختيار ملف Excel للأقسام",
                filetypes=[("Excel files", "*.xlsx *.xls"), ("All files", "*.*")]
            )
            
            if not file_path:
                return
            
            # رسالة تأكيد
            confirm_result = messagebox.askyesnocancel(
                "تأكيد الاستيراد",
                f"سيتم استيراد الأقسام من:\n{file_path}\n\n"
                f"• الأقسام الموجودة سيتم تحديثها\n"
                f"• الأقسام الجديدة سيتم إضافتها\n"
                f"• سيتم التحقق من صحة أسماء الإدارات\n\n"
                f"هل تريد المتابعة؟\n\n"
                f"نعم = متابعة الاستيراد\n"
                f"لا = إلغاء العملية\n"
                f"إلغاء = اختيار ملف آخر"
            )
            
            if confirm_result is None:  # إلغاء - اختيار ملف آخر
                return self.import_from_excel()
            elif not confirm_result:  # لا - إلغاء العملية
                return
            
            # قراءة الملف
            df = pd.read_excel(file_path)
            
            # التحقق من الأعمدة المطلوبة
            required_columns = ['اسم القسم', 'الإدارة']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                messagebox.showerror("خطأ", f"الأعمدة المطلوبة مفقودة: {', '.join(missing_columns)}")
                return
            
            # عرض معلومات الملف
            available_columns = list(df.columns)
            print(f"الأعمدة المتاحة: {', '.join(available_columns)}")
            print(f"عدد الصفوف: {len(df)}")
            
            # إنشاء نافذة تقدم للملفات الكبيرة
            progress_window = None
            progress_var = None
            progress_label = None
            
            if len(df) > 20:  # إظهار شريط التقدم للملفات الكبيرة
                progress_window = tk.Toplevel(self.window)
                progress_window.title("جاري استيراد الأقسام...")
                progress_window.geometry("400x120")
                progress_window.resizable(False, False)
                progress_window.transient(self.window)
                progress_window.grab_set()
                
                # توسيط النافذة
                progress_window.update_idletasks()
                x = (progress_window.winfo_screenwidth() // 2) - (400 // 2)
                y = (progress_window.winfo_screenheight() // 2) - (120 // 2)
                progress_window.geometry(f"400x120+{x}+{y}")
                
                # عناصر واجهة شريط التقدم
                tk.Label(progress_window, text="جاري استيراد الأقسام...", font=("Arial", 12)).pack(pady=10)
                
                progress_var = tk.DoubleVar()
                progress_bar = ttk.Progressbar(progress_window, variable=progress_var, maximum=100, length=350)
                progress_bar.pack(pady=5)
                
                progress_label = tk.Label(progress_window, text="", font=("Arial", 10))
                progress_label.pack(pady=5)
            
            # تحديث بيانات الإدارات
            self.departments_data = Department.get_all(active_only=False)
            
            # إحصائيات الاستيراد
            imported_count = 0
            updated_count = 0
            error_count = 0
            invalid_department_count = 0
            total_rows = len(df)
            invalid_departments = []
            
            for row_index, (_, row) in enumerate(df.iterrows()):
                try:
                    # تحديث شريط التقدم
                    if progress_window:
                        progress_percent = (row_index / len(df)) * 100
                        progress_var.set(progress_percent)
                        progress_label.config(text=f"معالجة الصف {row_index + 1} من {len(df)}")
                        progress_window.update()
                    
                    # التحقق من وجود اسم القسم
                    if pd.isna(row['اسم القسم']) or not str(row['اسم القسم']).strip():
                        continue
                    
                    section_name = str(row['اسم القسم']).strip()
                    department_name = str(row['الإدارة']).strip() if pd.notna(row['الإدارة']) else ""
                    
                    if not department_name:
                        error_count += 1
                        print(f"اسم الإدارة مفقود للقسم: {section_name}")
                        continue
                    
                    # البحث عن الإدارة
                    department = None
                    for dept in self.departments_data:
                        if dept.name == department_name:
                            department = dept
                            break
                    
                    if not department:
                        invalid_department_count += 1
                        if department_name not in invalid_departments:
                            invalid_departments.append(department_name)
                        print(f"إدارة غير موجودة: {department_name}")
                        continue
                    
                    # البحث عن القسم الموجود
                    existing_section = None
                    try:
                        existing_row = db_manager.fetch_one(
                            "SELECT * FROM sections WHERE name = ?",
                            (section_name,)
                        )
                        if existing_row:
                            existing_section = Section.from_row(existing_row)
                    except Exception as e:
                        print(f"خطأ في البحث عن القسم الموجود: {e}")
                    
                    # إنشاء أو تحديث القسم
                    if existing_section:
                        # تحديث القسم الموجود
                        section = existing_section
                        section.name = section_name
                        section.department_id = department.id
                        section.is_active = True  # إعادة تفعيل إذا كان غير نشط
                        is_update = True
                    else:
                        # إنشاء قسم جديد
                        section = Section(
                            name=section_name,
                            department_id=department.id,
                            is_active=True
                        )
                        is_update = False
                    
                    # حفظ القسم
                    if section.save():
                        if is_update:
                            updated_count += 1
                        else:
                            imported_count += 1
                    else:
                        error_count += 1
                        print(f"فشل في حفظ القسم: {section_name}")
                
                except Exception as e:
                    error_count += 1
                    print(f"خطأ في معالجة الصف: {e}")
                    continue
            
            # إغلاق نافذة التقدم
            if progress_window:
                progress_var.set(100)
                progress_label.config(text="تم الانتهاء من المعالجة...")
                progress_window.update()
                progress_window.after(500, progress_window.destroy)
            
            # إعداد رسالة النتائج
            success_message = []
            if imported_count > 0:
                success_message.append(f"✅ تم استيراد {imported_count} قسم جديد")
            if updated_count > 0:
                success_message.append(f"🔄 تم تحديث {updated_count} قسم موجود")
            
            # إضافة معلومات الأخطاء
            if invalid_department_count > 0:
                success_message.append(f"⚠️ تم تجاهل {invalid_department_count} قسم بسبب إدارات غير موجودة")
                if len(invalid_departments) <= 3:
                    success_message.append("الإدارات غير الموجودة:")
                    for dept in invalid_departments:
                        success_message.append(f"  • {dept}")
                else:
                    success_message.append(f"الإدارات غير الموجودة (أول 3 من {len(invalid_departments)}):")
                    for dept in invalid_departments[:3]:
                        success_message.append(f"  • {dept}")
                    success_message.append(f"  ... و {len(invalid_departments) - 3} إدارات أخرى")
            
            if error_count > 0:
                success_message.append(f"❌ فشل في معالجة {error_count} قسم")
            
            # إضافة الإجمالي
            processed_count = imported_count + updated_count
            success_message.append(f"\n📊 الإجمالي: تم معالجة {processed_count} من أصل {total_rows} صف")
            
            if processed_count > 0:
                # عرض رسالة النجاح مع خيار الاستيراد مرة أخرى
                success_message.append(f"\n🔄 هل تريد استيراد ملف آخر؟")
                
                result = messagebox.askyesno(
                    "تم الاستيراد بنجاح", 
                    "\n".join(success_message),
                    icon='info'
                )
                
                # تحديث البيانات في الشاشة
                self.refresh_data()
                
                # إذا اختار المستخدم "نعم"، فتح نافذة استيراد جديدة
                if result:
                    self.import_from_excel()
            else:
                # في حالة عدم معالجة أي أقسام
                result = messagebox.askyesnocancel(
                    "تنبيه", 
                    f"لم يتم معالجة أي أقسام بنجاح من أصل {total_rows} صف\n"
                    f"يرجى التحقق من:\n"
                    f"• صحة أسماء الإدارات (يجب أن تطابق الأسماء في شاشة إدارة الإدارات)\n"
                    f"• تنسيق البيانات في الملف\n\n"
                    f"هل تريد المحاولة مرة أخرى؟\n\n"
                    f"نعم = اختيار ملف آخر\n"
                    f"لا = إغلاق\n"
                    f"إلغاء = مراجعة البيانات"
                )
                
                if result is True:  # نعم - اختيار ملف آخر
                    self.import_from_excel()
                elif result is None:  # إلغاء - مراجعة البيانات
                    self.refresh_data()
        
        except Exception as e:
            # إغلاق نافذة التقدم في حالة الخطأ
            try:
                if 'progress_window' in locals() and progress_window:
                    progress_window.destroy()
            except:
                pass
            
            # في حالة حدوث خطأ، عرض رسالة مع خيار المحاولة مرة أخرى
            error_message = f"فشل في استيراد الأقسام: {e}\n\nهل تريد المحاولة مرة أخرى؟"
            
            result = messagebox.askyesno(
                "خطأ في الاستيراد", 
                error_message,
                icon='error'
            )
            
            if result:
                self.import_from_excel()

    def show_success_message(self, message="تم بنجاح"):
        """عرض رسالة النجاح تختفي خلال 3 ثوان أو بالضغط"""
        # إنشاء نافذة رسالة مخصصة
        success_window = tk.Toplevel(self.window)
        success_window.title("✅ تم بنجاح")
        success_window.geometry("400x200")
        success_window.resizable(False, False)
        success_window.transient(self.window)
        success_window.grab_set()
        success_window.configure(bg='#d4edda')

        # توسيط النافذة
        success_window.update_idletasks()
        x = (success_window.winfo_screenwidth() - 400) // 2
        y = (success_window.winfo_screenheight() - 200) // 2
        success_window.geometry(f"400x200+{x}+{y}")

        # ربط الضغط في أي مكان لإغلاق النافذة
        def close_on_click(event=None):
            success_window.destroy()

        success_window.bind("<Button-1>", close_on_click)
        success_window.bind("<Key>", close_on_click)
        success_window.focus_set()

        # محتوى الرسالة
        frame = ttk_bs.Frame(success_window)
        frame.pack(fill=tk.BOTH, expand=True, padx=30, pady=30)
        frame.bind("<Button-1>", close_on_click)

        # أيقونة النجاح
        success_icon = ttk_bs.Label(
            frame,
            text="✅",
            bootstyle="success"
        )
        success_icon.pack(pady=10)
        success_icon.bind("<Button-1>", close_on_click)

        # رسالة النجاح
        success_label = ttk_bs.Label(
            frame,
            text=message,
            bootstyle="success"
        )
        success_label.pack(pady=10)
        success_label.bind("<Button-1>", close_on_click)

        # رسالة إرشادية
        info_label = ttk_bs.Label(
            frame,
            text="اضغط في أي مكان للإغلاق",
            bootstyle="secondary"
        )
        info_label.pack(pady=5)
        info_label.bind("<Button-1>", close_on_click)

        # إغلاق تلقائي بعد 3 ثوان
        success_window.after(3000, close_on_click)


class SectionEditDialog:
    """نافذة حوار تعديل القسم"""

    def __init__(self, parent, title, section, departments_data):
        self.result = None
        self.section = section
        self.departments_data = departments_data

        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("500x250")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # توسيط النافذة
        self.center_window()

        # إعداد المحتوى
        self.setup_dialog()

        # انتظار إغلاق النافذة
        self.dialog.wait_window()

    def center_window(self):
        """توسيط النافذة"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() - 500) // 2
        y = (self.dialog.winfo_screenheight() - 250) // 2
        self.dialog.geometry(f"500x250+{x}+{y}")

    def setup_dialog(self):
        """إعداد محتوى النافذة"""
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.dialog)
        main_frame.pack(fill=BOTH, expand=True, padx=20, pady=20)

        # متغيرات النموذج
        self.department_var = tk.StringVar()
        self.name_var = tk.StringVar(value=self.section.name)
        self.is_active_var = tk.BooleanVar(value=self.section.is_active)

        # قائمة الإدارات
        ttk_bs.Label(main_frame, text="الإدارة:").pack(anchor=E, pady=(0, 5))
        dept_names = ["-- اختر الإدارة --"] + [dept.name for dept in self.departments_data if dept.is_active]
        self.dept_combo = ttk_bs.Combobox(main_frame, textvariable=self.department_var, values=dept_names, state="readonly", width=50)
        self.dept_combo.pack(fill=X, pady=(0, 15))

        # تعيين الإدارة الحالية
        if self.section.department_id:
            dept = next((d for d in self.departments_data if d.id == self.section.department_id), None)
            if dept:
                self.department_var.set(dept.name)
            else:
                self.department_var.set("-- اختر الإدارة --")
        else:
            self.department_var.set("-- اختر الإدارة --")

        # اسم القسم
        ttk_bs.Label(main_frame, text="اسم القسم:").pack(anchor=E, pady=(0, 5))
        ttk_bs.Entry(main_frame, textvariable=self.name_var, width=50).pack(fill=X, pady=(0, 15))

        # خانة اختيار الحالة
        ttk_bs.Checkbutton(
            main_frame,
            text="القسم نشط",
            variable=self.is_active_var,
            bootstyle="success"
        ).pack(anchor=E, pady=(0, 20))

        # الأزرار
        buttons_frame = ttk_bs.Frame(main_frame)
        buttons_frame.pack()

        ttk_bs.Button(
            buttons_frame,
            text="💾 حفظ",
            command=self.save_data,
            bootstyle="success",
            width=15
        ).pack(side=LEFT, padx=10)

        ttk_bs.Button(
            buttons_frame,
            text="❌ إلغاء",
            command=self.cancel,
            bootstyle="secondary",
            width=15
        ).pack(side=LEFT, padx=10)

    def save_data(self):
        """حفظ البيانات"""
        if not self.name_var.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال اسم القسم")
            return

        if self.department_var.get() == "-- اختر الإدارة --":
            messagebox.showerror("خطأ", "يرجى اختيار الإدارة")
            return

        # البحث عن معرف الإدارة
        department_id = None
        dept_name = self.department_var.get()
        for dept in self.departments_data:
            if dept.name == dept_name:
                department_id = dept.id
                break

        self.result = {
            'name': self.name_var.get().strip(),
            'department_id': department_id,
            'is_active': self.is_active_var.get()
        }

        self.dialog.destroy()

    def cancel(self):
        """إلغاء العملية"""
        self.dialog.destroy()
