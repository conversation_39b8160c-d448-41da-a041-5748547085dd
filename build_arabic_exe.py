#!/usr/bin/env python3
"""
بناء ملف تنفيذي باسم عربي مع جميع المكتبات في مجلد واحد
Build Arabic Named Executable with All Libraries in One Directory
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path
import time

def clean_previous_builds():
    """تنظيف البناءات السابقة"""
    print("🧹 تنظيف البناءات السابقة...")
    
    # مجلدات البناء المؤقتة
    temp_dirs = ['build', 'dist', '__pycache__']
    
    for temp_dir in temp_dirs:
        if Path(temp_dir).exists():
            try:
                shutil.rmtree(temp_dir)
                print(f"✅ تم حذف مجلد: {temp_dir}")
            except Exception as e:
                print(f"⚠️ تعذر حذف {temp_dir}: {e}")
    
    # ملفات spec
    spec_files = list(Path('.').glob('*.spec'))
    for spec_file in spec_files:
        try:
            spec_file.unlink()
            print(f"✅ تم حذف ملف: {spec_file}")
        except Exception as e:
            print(f"⚠️ تعذر حذف {spec_file}: {e}")

def check_requirements():
    """فحص المتطلبات"""
    print("🔍 فحص المتطلبات...")
    
    # فحص PyInstaller
    try:
        import PyInstaller
        print(f"✅ PyInstaller متوفر - الإصدار: {PyInstaller.__version__}")
    except ImportError:
        print("❌ PyInstaller غير مثبت!")
        print("قم بتثبيته باستخدام: pip install pyinstaller")
        return False
    
    # فحص الملف الرئيسي
    if not Path('run_app.py').exists():
        print("❌ ملف run_app.py غير موجود!")
        return False
    
    # فحص الأيقونة
    icon_path = Path('assets/app_icon.ico')
    if not icon_path.exists():
        print(f"⚠️ ملف الأيقونة غير موجود: {icon_path}")
        print("سيتم البناء بدون أيقونة")
    else:
        print(f"✅ ملف الأيقونة موجود: {icon_path}")
    
    return True

def build_executable():
    """بناء الملف التنفيذي"""
    print("🔨 بناء الملف التنفيذي...")
    print("=" * 60)
    
    # اسم التطبيق بالعربية
    app_name = "نظام_إدارة_المخازن_والمستودعات"
    
    # إعداد أوامر PyInstaller
    pyinstaller_args = [
        '--name=' + app_name,
        '--onedir',  # مجلد واحد يحتوي على جميع المكتبات
        '--windowed',  # بدون نافذة console
        '--noconfirm',  # عدم طلب تأكيد
        '--clean',  # تنظيف البناء السابق
        '--distpath=dist',  # مجلد الإخراج
        '--workpath=build',  # مجلد العمل المؤقت
    ]
    
    # إضافة الأيقونة إذا كانت موجودة
    icon_path = Path('assets/app_icon.ico')
    if icon_path.exists():
        pyinstaller_args.append(f'--icon={icon_path}')
    
    # إضافة البيانات والملفات المطلوبة
    data_additions = [
        '--add-data=assets;assets',
        '--add-data=ui;ui',
        '--add-data=utils;utils',
        '--add-data=config.py;.',
        '--add-data=models.py;.',
        '--add-data=database.py;.',
        '--add-data=permissions_manager.py;.',
        '--add-data=activity_monitor.py;.',
    ]
    
    # فحص وإضافة الملفات الموجودة فقط
    for data_arg in data_additions:
        source_path = data_arg.split(';')[0].replace('--add-data=', '')
        if Path(source_path).exists():
            pyinstaller_args.append(data_arg)
            print(f"✅ سيتم تضمين: {source_path}")
        else:
            print(f"⚠️ ملف غير موجود: {source_path}")
    
    # إضافة مكتبات مخفية مطلوبة
    hidden_imports = [
        '--hidden-import=tkinter',
        '--hidden-import=tkinter.ttk',
        '--hidden-import=ttkbootstrap',
        '--hidden-import=PIL',
        '--hidden-import=PIL.Image',
        '--hidden-import=PIL.ImageTk',
        '--hidden-import=pandas',
        '--hidden-import=sqlite3',
        '--hidden-import=bcrypt',
        '--hidden-import=reportlab',
        '--hidden-import=matplotlib',
        '--hidden-import=openpyxl',
        '--hidden-import=xlsxwriter',
    ]
    
    pyinstaller_args.extend(hidden_imports)
    
    # إضافة الملف الرئيسي
    pyinstaller_args.append('run_app.py')
    
    print("🚀 بدء عملية البناء...")
    print(f"📝 الأوامر: {' '.join(pyinstaller_args)}")
    print("⏳ هذا قد يستغرق عدة دقائق...")
    
    try:
        # تشغيل PyInstaller
        result = subprocess.run(['pyinstaller'] + pyinstaller_args, 
                              capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✅ تم بناء الملف التنفيذي بنجاح!")
            return True
        else:
            print("❌ فشل في بناء الملف التنفيذي!")
            print("خطأ:", result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ خطأ في عملية البناء: {e}")
        return False

def create_data_directories(app_dir):
    """إنشاء مجلدات البيانات"""
    print("📁 إنشاء مجلدات البيانات...")
    
    data_dirs = {
        "data": "مجلد البيانات - يحتوي على قاعدة البيانات والملفات المهمة",
        "reports": "مجلد التقارير - يحتوي على التقارير المُصدرة", 
        "backups": "مجلد النسخ الاحتياطية - يحتوي على نسخ احتياطية من البيانات",
        "logs": "مجلد السجلات - يحتوي على ملفات سجل التطبيق",
        "imports": "مجلد الاستيراد - ضع ملفات Excel هنا للاستيراد",
        "exports": "مجلد التصدير - يحتوي على الملفات المُصدرة"
    }
    
    for dir_name, description in data_dirs.items():
        dir_path = app_dir / dir_name
        dir_path.mkdir(exist_ok=True)
        
        # إنشاء ملف README في كل مجلد
        readme_file = dir_path / "README.txt"
        readme_file.write_text(description, encoding='utf-8')
        print(f"✅ تم إنشاء مجلد: {dir_name}")

def create_batch_files(app_dir, app_name):
    """إنشاء ملفات التشغيل السريع"""
    print("📄 إنشاء ملفات التشغيل...")
    
    # ملف التشغيل العادي
    batch_content = f"""@echo off
chcp 65001 > nul
title نظام إدارة المخازن والمستودعات - الإصدار العربي

cls
echo.
echo ========================================
echo      نظام إدارة المخازن والمستودعات
echo        Desktop Stores Management System
echo ========================================
echo.
echo الإصدار: 2.0.0 العربي الكامل
echo تاريخ البناء: %date% %time%
echo.
echo المميزات:
echo - اسم عربي كامل للتطبيق
echo - جميع المكتبات في مجلد واحد
echo - سهولة النقل بين الأجهزة
echo - أداء محسن ومستقر
echo.
echo ========================================
echo.

cd /d "%~dp0"

echo 🚀 بدء تشغيل التطبيق...
echo.

if exist "{app_name}.exe" (
    start "" "{app_name}.exe"
    echo ✅ تم تشغيل التطبيق بنجاح
    echo.
    echo 🔐 بيانات الدخول الافتراضية:
    echo    اسم المستخدم: admin
    echo    كلمة المرور: admin
    echo.
    echo ⚠️  مهم: يرجى تغيير كلمة المرور بعد أول تسجيل دخول
    echo.
    echo 💡 نصائح:
    echo    - ضع ملفات Excel في مجلد imports للاستيراد
    echo    - التقارير ستحفظ في مجلد reports
    echo    - النسخ الاحتياطية في مجلد backups
    echo.
    echo يمكنك إغلاق هذه النافذة الآن
    timeout /t 10 > nul
) else (
    echo ❌ خطأ: لم يتم العثور على الملف التنفيذي
    echo يرجى التأكد من وجود ملف "{app_name}.exe"
    pause
)
"""
    
    batch_file = app_dir / "تشغيل_البرنامج.bat"
    batch_file.write_text(batch_content, encoding='utf-8')
    print("✅ تم إنشاء ملف التشغيل العادي")
    
    # ملف التشغيل بصلاحيات المدير
    admin_batch_content = f"""@echo off
chcp 65001 > nul

:: التحقق من صلاحيات المدير
net session >nul 2>&1
if %errorLevel% == 0 (
    echo 🔧 تشغيل بصلاحيات المدير...
) else (
    echo 🔐 طلب صلاحيات المدير...
    powershell -Command "Start-Process '%~f0' -Verb RunAs"
    exit /b
)

title نظام إدارة المخازن والمستودعات - وضع المدير

cd /d "%~dp0"
call "تشغيل_البرنامج.bat"
"""
    
    admin_batch_file = app_dir / "تشغيل_بصلاحيات_المدير.bat"
    admin_batch_file.write_text(admin_batch_content, encoding='utf-8')
    print("✅ تم إنشاء ملف التشغيل بصلاحيات المدير")

def create_documentation(app_dir, app_name):
    """إنشاء ملفات التوثيق"""
    print("📚 إنشاء ملفات التوثيق...")
    
    # ملف معلومات الإصدار
    version_info = f"""نظام إدارة المخازن والمستودعات
Desktop Stores Management System

الإصدار: 2.0.0 العربي الكامل
تاريخ البناء: {time.strftime('%Y-%m-%d %H:%M:%S')}
نوع البناء: Arabic Named Executable with All Libraries

========================================
✅ تم تحقيق جميع المتطلبات:
========================================

1️⃣ اسم التطبيق بالعربية:
   ✅ اسم الملف التنفيذي: {app_name}.exe
   ✅ عنوان النافذة بالعربية
   ✅ جميع النصوص والقوائم بالعربية

2️⃣ جميع المكتبات في مجلد واحد:
   ✅ مجلد _internal يحتوي على جميع المكتبات
   ✅ لا يحتاج تثبيت Python أو أي مكتبات خارجية
   ✅ يعمل على أي جهاز Windows مباشرة

3️⃣ سهولة النقل والتوزيع:
   ✅ مجلد واحد يحتوي على كل شيء
   ✅ نسخ ولصق المجلد ينقل التطبيق كاملاً
   ✅ لا يحتاج تثبيت أو إعداد إضافي

========================================
📁 محتويات الحزمة:
========================================

📄 الملفات الرئيسية:
- {app_name}.exe (الملف التنفيذي الرئيسي)
- تشغيل_البرنامج.bat (تشغيل عادي)
- تشغيل_بصلاحيات_المدير.bat (تشغيل بصلاحيات مدير)

📁 مجلدات البيانات:
- data/: قاعدة البيانات والملفات المهمة
- reports/: التقارير المُصدرة
- backups/: النسخ الاحتياطية
- logs/: ملفات السجل
- imports/: ملفات Excel للاستيراد
- exports/: الملفات المُصدرة

📁 ملفات النظام:
- _internal/: جميع المكتبات والمتطلبات (لا تحذف!)

========================================
🚀 تعليمات الاستخدام:
========================================

1️⃣ التشغيل الأول:
   - انقر مرتين على "تشغيل_البرنامج.bat"
   - أو انقر على "{app_name}.exe"

2️⃣ بيانات الدخول:
   - اسم المستخدم: admin
   - كلمة المرور: admin
   - ⚠️ غير كلمة المرور فوراً!

3️⃣ للنقل إلى جهاز آخر:
   - انسخ المجلد بالكامل
   - الصق في الجهاز الجديد
   - شغل التطبيق مباشرة

========================================
💻 متطلبات التشغيل:
========================================

- Windows 10 أو أحدث (مُوصى به)
- Windows 8.1 (مدعوم)
- 4 GB RAM أو أكثر
- 1 GB مساحة فارغة على القرص
- دقة شاشة 1024x768 أو أعلى

========================================
🔧 في حالة المشاكل:
========================================

❓ التطبيق لا يبدأ:
- جرب "تشغيل_بصلاحيات_المدير.bat"
- تأكد من وجود مساحة كافية على القرص
- أغلق برامج مكافحة الفيروسات مؤقتاً

❓ بطء في الأداء:
- أغلق البرامج الأخرى
- تأكد من وجود ذاكرة كافية
- أعد تشغيل التطبيق

❓ مشاكل في استيراد Excel:
- ضع الملفات في مجلد imports/
- تأكد من تنسيق البيانات الصحيح
- جرب ملفات أصغر أولاً

========================================
🎯 النتيجة النهائية:
========================================

✅ تطبيق باسم عربي كامل
✅ جميع المكتبات مدمجة في مجلد واحد
✅ سهولة النقل والتوزيع
✅ يعمل على أي جهاز Windows
✅ لا يحتاج تثبيت أي برامج إضافية

🎉 التطبيق جاهز للاستخدام والتوزيع!

========================================
© 2025 Desktop Stores Team - جميع الحقوق محفوظة
========================================
"""
    
    version_file = app_dir / "معلومات_الإصدار.txt"
    version_file.write_text(version_info, encoding='utf-8')
    print("✅ تم إنشاء ملف معلومات الإصدار")
    
    # دليل سريع
    quick_guide = """دليل البدء السريع
================

🚀 التشغيل:
1. انقر مرتين على "تشغيل_البرنامج.bat"
2. اسم المستخدم: admin
3. كلمة المرور: admin
4. غير كلمة المرور فوراً!

📋 الوظائف الأساسية:
- المخزون: إدارة الأصناف والكميات
- المستفيدون: إدارة الجهات المستفيدة
- المعاملات: عمليات الصرف والاستلام
- التقارير: عرض وطباعة التقارير

📥 استيراد البيانات:
1. ضع ملف Excel في مجلد "imports"
2. من القائمة: ملف > استيراد
3. اختر الملف واتبع التعليمات

💾 النسخ الاحتياطي:
- تلقائي: يومياً في مجلد "backups"
- يدوي: من قائمة "أدوات"

⚠️ تحذيرات مهمة:
- لا تحذف مجلد "_internal" أبداً
- اعمل نسخة احتياطية دورية
- أغلق التطبيق بشكل طبيعي

للمساعدة الكاملة: راجع "معلومات_الإصدار.txt"
"""
    
    guide_file = app_dir / "دليل_سريع.txt"
    guide_file.write_text(quick_guide, encoding='utf-8')
    print("✅ تم إنشاء الدليل السريع")

def finalize_package():
    """إنهاء الحزمة وعرض النتائج"""
    app_name = "نظام_إدارة_المخازن_والمستودعات"
    app_dir = Path("dist") / app_name
    
    if not app_dir.exists():
        print("❌ مجلد التطبيق غير موجود!")
        return False
    
    # إنشاء مجلدات البيانات
    create_data_directories(app_dir)
    
    # إنشاء ملفات التشغيل
    create_batch_files(app_dir, app_name)
    
    # إنشاء التوثيق
    create_documentation(app_dir, app_name)
    
    # حساب حجم الحزمة
    total_size = sum(f.stat().st_size for f in app_dir.rglob('*') if f.is_file())
    size_mb = total_size / (1024 * 1024)
    
    print("\n" + "=" * 60)
    print("🎉 تم إنشاء التطبيق العربي بنجاح!")
    print("=" * 60)
    print(f"📁 مجلد التطبيق: {app_dir}")
    print(f"📄 الملف التنفيذي: {app_name}.exe")
    print(f"📊 الحجم الإجمالي: {size_mb:.1f} MB")
    print("✅ اسم عربي كامل للتطبيق")
    print("✅ جميع المكتبات في مجلد _internal")
    print("✅ جاهز للنقل والتوزيع")
    print("✅ يعمل على أي جهاز Windows")
    print("=" * 60)
    
    return True

def main():
    """الدالة الرئيسية"""
    print("🏗️ بناء نظام إدارة المخازن - الإصدار العربي")
    print("=" * 60)
    
    # تنظيف البناءات السابقة
    clean_previous_builds()
    
    # فحص المتطلبات
    if not check_requirements():
        print("❌ فشل في فحص المتطلبات!")
        return False
    
    # بناء الملف التنفيذي
    if not build_executable():
        print("❌ فشل في بناء الملف التنفيذي!")
        return False
    
    # إنهاء الحزمة
    if not finalize_package():
        print("❌ فشل في إنهاء الحزمة!")
        return False
    
    print("\n🎊 تم الانتهاء بنجاح!")
    print("يمكنك الآن العثور على التطبيق في مجلد dist/")
    
    return True

if __name__ == "__main__":
    main()#!/usr/bin/env python3
"""
بناء ملف تنفيذي باسم عربي مع جميع المكتبات في مجلد واحد
Build Arabic Named Executable with All Libraries in One Directory
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path
import time

def clean_previous_builds():
    """تنظيف البناءات السابقة"""
    print("🧹 تنظيف البناءات السابقة...")
    
    # مجلدات البناء المؤقتة
    temp_dirs = ['build', 'dist', '__pycache__']
    
    for temp_dir in temp_dirs:
        if Path(temp_dir).exists():
            try:
                shutil.rmtree(temp_dir)
                print(f"✅ تم حذف مجلد: {temp_dir}")
            except Exception as e:
                print(f"⚠️ تعذر حذف {temp_dir}: {e}")
    
    # ملفات spec
    spec_files = list(Path('.').glob('*.spec'))
    for spec_file in spec_files:
        try:
            spec_file.unlink()
            print(f"✅ تم حذف ملف: {spec_file}")
        except Exception as e:
            print(f"⚠️ تعذر حذف {spec_file}: {e}")

def check_requirements():
    """فحص المتطلبات"""
    print("🔍 فحص المتطلبات...")
    
    # فحص PyInstaller
    try:
        import PyInstaller
        print(f"✅ PyInstaller متوفر - الإصدار: {PyInstaller.__version__}")
    except ImportError:
        print("❌ PyInstaller غير مثبت!")
        print("قم بتثبيته باستخدام: pip install pyinstaller")
        return False
    
    # فحص الملف الرئيسي
    if not Path('run_app.py').exists():
        print("❌ ملف run_app.py غير موجود!")
        return False
    
    # فحص الأيقونة
    icon_path = Path('assets/app_icon.ico')
    if not icon_path.exists():
        print(f"⚠️ ملف الأيقونة غير موجود: {icon_path}")
        print("سيتم البناء بدون أيقونة")
    else:
        print(f"✅ ملف الأيقونة موجود: {icon_path}")
    
    return True

def build_executable():
    """بناء الملف التنفيذي"""
    print("🔨 بناء الملف التنفيذي...")
    print("=" * 60)
    
    # اسم التطبيق بالعربية
    app_name = "نظام_إدارة_المخازن_والمستودعات"
    
    # إعداد أوامر PyInstaller
    pyinstaller_args = [
        '--name=' + app_name,
        '--onedir',  # مجلد واحد يحتوي على جميع المكتبات
        '--windowed',  # بدون نافذة console
        '--noconfirm',  # عدم طلب تأكيد
        '--clean',  # تنظيف البناء السابق
        '--distpath=dist',  # مجلد الإخراج
        '--workpath=build',  # مجلد العمل المؤقت
    ]
    
    # إضافة الأيقونة إذا كانت موجودة
    icon_path = Path('assets/app_icon.ico')
    if icon_path.exists():
        pyinstaller_args.append(f'--icon={icon_path}')
    
    # إضافة البيانات والملفات المطلوبة
    data_additions = [
        '--add-data=assets;assets',
        '--add-data=ui;ui',
        '--add-data=utils;utils',
        '--add-data=config.py;.',
        '--add-data=models.py;.',
        '--add-data=database.py;.',
        '--add-data=permissions_manager.py;.',
        '--add-data=activity_monitor.py;.',
    ]
    
    # فحص وإضافة الملفات الموجودة فقط
    for data_arg in data_additions:
        source_path = data_arg.split(';')[0].replace('--add-data=', '')
        if Path(source_path).exists():
            pyinstaller_args.append(data_arg)
            print(f"✅ سيتم تضمين: {source_path}")
        else:
            print(f"⚠️ ملف غير موجود: {source_path}")
    
    # إضافة مكتبات مخفية مطلوبة
    hidden_imports = [
        '--hidden-import=tkinter',
        '--hidden-import=tkinter.ttk',
        '--hidden-import=ttkbootstrap',
        '--hidden-import=PIL',
        '--hidden-import=PIL.Image',
        '--hidden-import=PIL.ImageTk',
        '--hidden-import=pandas',
        '--hidden-import=sqlite3',
        '--hidden-import=bcrypt',
        '--hidden-import=reportlab',
        '--hidden-import=matplotlib',
        '--hidden-import=openpyxl',
        '--hidden-import=xlsxwriter',
    ]
    
    pyinstaller_args.extend(hidden_imports)
    
    # إضافة الملف الرئيسي
    pyinstaller_args.append('run_app.py')
    
    print("🚀 بدء عملية البناء...")
    print(f"📝 الأوامر: {' '.join(pyinstaller_args)}")
    print("⏳ هذا قد يستغرق عدة دقائق...")
    
    try:
        # تشغيل PyInstaller
        result = subprocess.run(['pyinstaller'] + pyinstaller_args, 
                              capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✅ تم بناء الملف التنفيذي بنجاح!")
            return True
        else:
            print("❌ فشل في بناء الملف التنفيذي!")
            print("خطأ:", result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ خطأ في عملية البناء: {e}")
        return False

def create_data_directories(app_dir):
    """إنشاء مجلدات البيانات"""
    print("📁 إنشاء مجلدات البيانات...")
    
    data_dirs = {
        "data": "مجلد البيانات - يحتوي على قاعدة البيانات والملفات المهمة",
        "reports": "مجلد التقارير - يحتوي على التقارير المُصدرة", 
        "backups": "مجلد النسخ الاحتياطية - يحتوي على نسخ احتياطية من البيانات",
        "logs": "مجلد السجلات - يحتوي على ملفات سجل التطبيق",
        "imports": "مجلد الاستيراد - ضع ملفات Excel هنا للاستيراد",
        "exports": "مجلد التصدير - يحتوي على الملفات المُصدرة"
    }
    
    for dir_name, description in data_dirs.items():
        dir_path = app_dir / dir_name
        dir_path.mkdir(exist_ok=True)
        
        # إنشاء ملف README في كل مجلد
        readme_file = dir_path / "README.txt"
        readme_file.write_text(description, encoding='utf-8')
        print(f"✅ تم إنشاء مجلد: {dir_name}")

def create_batch_files(app_dir, app_name):
    """إنشاء ملفات التشغيل السريع"""
    print("📄 إنشاء ملفات التشغيل...")
    
    # ملف التشغيل العادي
    batch_content = f"""@echo off
chcp 65001 > nul
title نظام إدارة المخازن والمستودعات - الإصدار العربي

cls
echo.
echo ========================================
echo      نظام إدارة المخازن والمستودعات
echo        Desktop Stores Management System
echo ========================================
echo.
echo الإصدار: 2.0.0 العربي الكامل
echo تاريخ البناء: %date% %time%
echo.
echo المميزات:
echo - اسم عربي كامل للتطبيق
echo - جميع المكتبات في مجلد واحد
echo - سهولة النقل بين الأجهزة
echo - أداء محسن ومستقر
echo.
echo ========================================
echo.

cd /d "%~dp0"

echo 🚀 بدء تشغيل التطبيق...
echo.

if exist "{app_name}.exe" (
    start "" "{app_name}.exe"
    echo ✅ تم تشغيل التطبيق بنجاح
    echo.
    echo 🔐 بيانات الدخول الافتراضية:
    echo    اسم المستخدم: admin
    echo    كلمة المرور: admin
    echo.
    echo ⚠️  مهم: يرجى تغيير كلمة المرور بعد أول تسجيل دخول
    echo.
    echo 💡 نصائح:
    echo    - ضع ملفات Excel في مجلد imports للاستيراد
    echo    - التقارير ستحفظ في مجلد reports
    echo    - النسخ الاحتياطية في مجلد backups
    echo.
    echo يمكنك إغلاق هذه النافذة الآن
    timeout /t 10 > nul
) else (
    echo ❌ خطأ: لم يتم العثور على الملف التنفيذي
    echo يرجى التأكد من وجود ملف "{app_name}.exe"
    pause
)
"""
    
    batch_file = app_dir / "تشغيل_البرنامج.bat"
    batch_file.write_text(batch_content, encoding='utf-8')
    print("✅ تم إنشاء ملف التشغيل العادي")
    
    # ملف التشغيل بصلاحيات المدير
    admin_batch_content = f"""@echo off
chcp 65001 > nul

:: التحقق من صلاحيات المدير
net session >nul 2>&1
if %errorLevel% == 0 (
    echo 🔧 تشغيل بصلاحيات المدير...
) else (
    echo 🔐 طلب صلاحيات المدير...
    powershell -Command "Start-Process '%~f0' -Verb RunAs"
    exit /b
)

title نظام إدارة المخازن والمستودعات - وضع المدير

cd /d "%~dp0"
call "تشغيل_البرنامج.bat"
"""
    
    admin_batch_file = app_dir / "تشغيل_بصلاحيات_المدير.bat"
    admin_batch_file.write_text(admin_batch_content, encoding='utf-8')
    print("✅ تم إنشاء ملف التشغيل بصلاحيات المدير")

def create_documentation(app_dir, app_name):
    """إنشاء ملفات التوثيق"""
    print("📚 إنشاء ملفات التوثيق...")
    
    # ملف معلومات الإصدار
    version_info = f"""نظام إدارة المخازن والمستودعات
Desktop Stores Management System

الإصدار: 2.0.0 العربي الكامل
تاريخ البناء: {time.strftime('%Y-%m-%d %H:%M:%S')}
نوع البناء: Arabic Named Executable with All Libraries

========================================
✅ تم تحقيق جميع المتطلبات:
========================================

1️⃣ اسم التطبيق بالعربية:
   ✅ اسم الملف التنفيذي: {app_name}.exe
   ✅ عنوان النافذة بالعربية
   ✅ جميع النصوص والقوائم بالعربية

2️⃣ جميع المكتبات في مجلد واحد:
   ✅ مجلد _internal يحتوي على جميع المكتبات
   ✅ لا يحتاج تثبيت Python أو أي مكتبات خارجية
   ✅ يعمل على أي جهاز Windows مباشرة

3️⃣ سهولة النقل والتوزيع:
   ✅ مجلد واحد يحتوي على كل شيء
   ✅ نسخ ولصق المجلد ينقل التطبيق كاملاً
   ✅ لا يحتاج تثبيت أو إعداد إضافي

========================================
📁 محتويات الحزمة:
========================================

📄 الملفات الرئيسية:
- {app_name}.exe (الملف التنفيذي الرئيسي)
- تشغيل_البرنامج.bat (تشغيل عادي)
- تشغيل_بصلاحيات_المدير.bat (تشغيل بصلاحيات مدير)

📁 مجلدات البيانات:
- data/: قاعدة البيانات والملفات المهمة
- reports/: التقارير المُصدرة
- backups/: النسخ الاحتياطية
- logs/: ملفات السجل
- imports/: ملفات Excel للاستيراد
- exports/: الملفات المُصدرة

📁 ملفات النظام:
- _internal/: جميع المكتبات والمتطلبات (لا تحذف!)

========================================
🚀 تعليمات الاستخدام:
========================================

1️⃣ التشغيل الأول:
   - انقر مرتين على "تشغيل_البرنامج.bat"
   - أو انقر على "{app_name}.exe"

2️⃣ بيانات الدخول:
   - اسم المستخدم: admin
   - كلمة المرور: admin
   - ⚠️ غير كلمة المرور فوراً!

3️⃣ للنقل إلى جهاز آخر:
   - انسخ المجلد بالكامل
   - الصق في الجهاز الجديد
   - شغل التطبيق مباشرة

========================================
💻 متطلبات التشغيل:
========================================

- Windows 10 أو أحدث (مُوصى به)
- Windows 8.1 (مدعوم)
- 4 GB RAM أو أكثر
- 1 GB مساحة فارغة على القرص
- دقة شاشة 1024x768 أو أعلى

========================================
🔧 في حالة المشاكل:
========================================

❓ التطبيق لا يبدأ:
- جرب "تشغيل_بصلاحيات_المدير.bat"
- تأكد من وجود مساحة كافية على القرص
- أغلق برامج مكافحة الفيروسات مؤقتاً

❓ بطء في الأداء:
- أغلق البرامج الأخرى
- تأكد من وجود ذاكرة كافية
- أعد تشغيل التطبيق

❓ مشاكل في استيراد Excel:
- ضع الملفات في مجلد imports/
- تأكد من تنسيق البيانات الصحيح
- جرب ملفات أصغر أولاً

========================================
🎯 النتيجة النهائية:
========================================

✅ تطبيق باسم عربي كامل
✅ جميع المكتبات مدمجة في مجلد واحد
✅ سهولة النقل والتوزيع
✅ يعمل على أي جهاز Windows
✅ لا يحتاج تثبيت أي برامج إضافية

🎉 التطبيق جاهز للاستخدام والتوزيع!

========================================
© 2025 Desktop Stores Team - جميع الحقوق محفوظة
========================================
"""
    
    version_file = app_dir / "معلومات_الإصدار.txt"
    version_file.write_text(version_info, encoding='utf-8')
    print("✅ تم إنشاء ملف معلومات الإصدار")
    
    # دليل سريع
    quick_guide = """دليل البدء السريع
================

🚀 التشغيل:
1. انقر مرتين على "تشغيل_البرنامج.bat"
2. اسم المستخدم: admin
3. كلمة المرور: admin
4. غير كلمة المرور فوراً!

📋 الوظائف الأساسية:
- المخزون: إدارة الأصناف والكميات
- المستفيدون: إدارة الجهات المستفيدة
- المعاملات: عمليات الصرف والاستلام
- التقارير: عرض وطباعة التقارير

📥 استيراد البيانات:
1. ضع ملف Excel في مجلد "imports"
2. من القائمة: ملف > استيراد
3. اختر الملف واتبع التعليمات

💾 النسخ الاحتياطي:
- تلقائي: يومياً في مجلد "backups"
- يدوي: من قائمة "أدوات"

⚠️ تحذيرات مهمة:
- لا تحذف مجلد "_internal" أبداً
- اعمل نسخة احتياطية دورية
- أغلق التطبيق بشكل طبيعي

للمساعدة الكاملة: راجع "معلومات_الإصدار.txt"
"""
    
    guide_file = app_dir / "دليل_سريع.txt"
    guide_file.write_text(quick_guide, encoding='utf-8')
    print("✅ تم إنشاء الدليل السريع")

def finalize_package():
    """إنهاء الحزمة وعرض النتائج"""
    app_name = "نظام_إدارة_المخازن_والمستودعات"
    app_dir = Path("dist") / app_name
    
    if not app_dir.exists():
        print("❌ مجلد التطبيق غير موجود!")
        return False
    
    # إنشاء مجلدات البيانات
    create_data_directories(app_dir)
    
    # إنشاء ملفات التشغيل
    create_batch_files(app_dir, app_name)
    
    # إنشاء التوثيق
    create_documentation(app_dir, app_name)
    
    # حساب حجم الحزمة
    total_size = sum(f.stat().st_size for f in app_dir.rglob('*') if f.is_file())
    size_mb = total_size / (1024 * 1024)
    
    print("\n" + "=" * 60)
    print("🎉 تم إنشاء التطبيق العربي بنجاح!")
    print("=" * 60)
    print(f"📁 مجلد التطبيق: {app_dir}")
    print(f"📄 الملف التنفيذي: {app_name}.exe")
    print(f"📊 الحجم الإجمالي: {size_mb:.1f} MB")
    print("✅ اسم عربي كامل للتطبيق")
    print("✅ جميع المكتبات في مجلد _internal")
    print("✅ جاهز للنقل والتوزيع")
    print("✅ يعمل على أي جهاز Windows")
    print("=" * 60)
    
    return True

def main():
    """الدالة الرئيسية"""
    print("🏗️ بناء نظام إدارة المخازن - الإصدار العربي")
    print("=" * 60)
    
    # تنظيف البناءات السابقة
    clean_previous_builds()
    
    # فحص المتطلبات
    if not check_requirements():
        print("❌ فشل في فحص المتطلبات!")
        return False
    
    # بناء الملف التنفيذي
    if not build_executable():
        print("❌ فشل في بناء الملف التنفيذي!")
        return False
    
    # إنهاء الحزمة
    if not finalize_package():
        print("❌ فشل في إنهاء الحزمة!")
        return False
    
    print("\n🎊 تم الانتهاء بنجاح!")
    print("يمكنك الآن العثور على التطبيق في مجلد dist/")
    
    return True

if __name__ == "__main__":
    main()