#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from database import db_manager

def check_database_duplicates():
    """فحص التكرار في قاعدة البيانات"""
    
    # فحص البيانات في جدول inventory_movements_new
    query = """
    SELECT id, movement_date, item_number, movement_type, quantity, user_id
    FROM inventory_movements_new 
    WHERE is_active = 1 
    ORDER BY movement_date DESC, id DESC
    LIMIT 20
    """

    try:
        movements = db_manager.fetch_all(query)
        print("=== آخر 20 حركة في قاعدة البيانات ===")
        for i, movement in enumerate(movements, 1):
            print(f"{i}. ID: {movement['id']}, التاريخ: {movement['movement_date']}, الصنف: {movement['item_number']}, النوع: {movement['movement_type']}, الكمية: {movement['quantity']}")
        
        # فحص التكرار
        print("\n=== فحص التكرار ===")
        duplicate_query = """
        SELECT item_number, movement_type, quantity, movement_date, COUNT(*) as count
        FROM inventory_movements_new 
        WHERE is_active = 1 
        GROUP BY item_number, movement_type, quantity, movement_date
        HAVING COUNT(*) > 1
        """
        
        duplicates = db_manager.fetch_all(duplicate_query)
        if duplicates:
            print("تم العثور على حركات مكررة:")
            for dup in duplicates:
                print(f"الصنف: {dup['item_number']}, النوع: {dup['movement_type']}, الكمية: {dup['quantity']}, التاريخ: {dup['movement_date']}, العدد: {dup['count']}")
        else:
            print("لا توجد حركات مكررة في قاعدة البيانات")
            
        # فحص الحركات الأخيرة للصنف 10
        print("\n=== حركات الصنف 10 ===")
        item_10_query = """
        SELECT id, movement_date, movement_type, quantity, user_id
        FROM inventory_movements_new 
        WHERE is_active = 1 AND item_number = 10
        ORDER BY movement_date DESC, id DESC
        """
        
        item_10_movements = db_manager.fetch_all(item_10_query)
        for i, movement in enumerate(item_10_movements, 1):
            print(f"{i}. ID: {movement['id']}, التاريخ: {movement['movement_date']}, النوع: {movement['movement_type']}, الكمية: {movement['quantity']}")
            
    except Exception as e:
        print(f"خطأ: {e}")

if __name__ == "__main__":
    check_database_duplicates()
