#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار وظائف الباركود في شاشة عملية الصرف الجديدة
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import db_manager

def test_barcode_functionality():
    """اختبار وظائف الباركود"""
    print("🧪 بدء اختبار وظائف الباركود...")
    
    try:
        # 1. التحقق من وجود حقل الباركود في جدول added_items
        print("\n1️⃣ التحقق من هيكل جدول added_items...")
        
        # الحصول على معلومات الجدول
        table_info = db_manager.execute_query("PRAGMA table_info(added_items)")
        columns = [col[1] for col in table_info]
        
        print(f"📋 أعمدة الجدول: {columns}")
        
        if 'barcode' in columns:
            print("✅ حقل الباركود موجود في الجدول")
        else:
            print("❌ حقل الباركود غير موجود في الجدول")
            return False
        
        # 2. إضافة بيانات تجريبية للاختبار
        print("\n2️⃣ إضافة بيانات تجريبية...")
        
        # التحقق من وجود أصناف للاختبار
        existing_items = db_manager.fetch_all(
            "SELECT COUNT(*) FROM added_items WHERE is_active = 1"
        )

        if existing_items and existing_items[0][0] > 0:
            print(f"📦 يوجد {existing_items[0][0]} صنف في النظام")
            
            # إضافة باركود لبعض الأصناف الموجودة
            items_to_update = db_manager.fetch_all(
                "SELECT id, item_number, item_name FROM added_items WHERE is_active = 1 LIMIT 3"
            )
            
            for i, item in enumerate(items_to_update):
                barcode = f"BC{item[1]}{str(i+1).zfill(3)}"  # إنشاء باركود تجريبي
                
                db_manager.execute_query(
                    "UPDATE added_items SET barcode = ? WHERE id = ?",
                    (barcode, item[0])
                )
                
                print(f"🏷️ تم إضافة باركود {barcode} للصنف {item[1]} - {item[2]}")
        
        # 3. اختبار البحث بالباركود
        print("\n3️⃣ اختبار البحث بالباركود...")
        
        items_with_barcode = db_manager.fetch_all(
            "SELECT item_number, item_name, barcode FROM added_items WHERE barcode IS NOT NULL AND barcode != ''"
        )
        
        if items_with_barcode:
            print(f"✅ تم العثور على {len(items_with_barcode)} صنف مع باركود:")
            for item in items_with_barcode:
                print(f"   📦 {item[0]} - {item[1]} - باركود: {item[2]}")
                
                # اختبار البحث
                search_result = db_manager.fetch_all(
                    "SELECT * FROM added_items WHERE barcode = ? AND is_active = 1",
                    (item[2],)
                )
                
                if search_result:
                    print(f"   ✅ البحث بالباركود {item[2]} نجح")
                else:
                    print(f"   ❌ البحث بالباركود {item[2]} فشل")
        else:
            print("⚠️ لا توجد أصناف مع باركود للاختبار")
        
        # 4. اختبار البحث برقم الصنف كباركود
        print("\n4️⃣ اختبار البحث برقم الصنف...")
        
        sample_item = db_manager.fetch_all(
            "SELECT item_number, item_name FROM added_items WHERE is_active = 1 LIMIT 1"
        )
        
        if sample_item:
            item_number = sample_item[0][0]
            search_result = db_manager.fetch_all(
                "SELECT * FROM added_items WHERE item_number = ? AND is_active = 1",
                (item_number,)
            )
            
            if search_result:
                print(f"✅ البحث برقم الصنف {item_number} نجح")
            else:
                print(f"❌ البحث برقم الصنف {item_number} فشل")
        
        print("\n🎉 انتهى اختبار وظائف الباركود بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار وظائف الباركود: {e}")
        import traceback
        traceback.print_exc()
        return False

def add_sample_barcodes():
    """إضافة باركودات تجريبية للأصناف الموجودة"""
    print("🏷️ إضافة باركودات تجريبية...")
    
    try:
        # الحصول على الأصناف الموجودة
        items = db_manager.fetch_all(
            "SELECT id, item_number, item_name FROM added_items WHERE is_active = 1 AND (barcode IS NULL OR barcode = '') LIMIT 10"
        )
        
        if not items:
            print("ℹ️ جميع الأصناف لديها باركود بالفعل أو لا توجد أصناف")
            return
        
        for i, item in enumerate(items):
            # إنشاء باركود فريد
            barcode = f"BC{item[1]}{str(i+1).zfill(4)}"
            
            # تحديث الصنف بالباركود
            db_manager.execute_query(
                "UPDATE added_items SET barcode = ? WHERE id = ?",
                (barcode, item[0])
            )
            
            print(f"✅ تم إضافة باركود {barcode} للصنف {item[1]} - {item[2]}")
        
        print(f"🎉 تم إضافة باركودات لـ {len(items)} صنف")
        
    except Exception as e:
        print(f"❌ خطأ في إضافة الباركودات: {e}")

if __name__ == "__main__":
    print("=" * 60)
    print("🧪 اختبار وظائف الباركود في نظام إدارة المخازن")
    print("=" * 60)
    
    # إضافة باركودات تجريبية أولاً
    add_sample_barcodes()
    
    print("\n" + "=" * 60)
    
    # تشغيل الاختبار
    success = test_barcode_functionality()
    
    if success:
        print("\n✅ جميع الاختبارات نجحت!")
        print("\n📋 الميزات الجديدة:")
        print("   🏷️ حقل الباركود في شاشة عملية الصرف")
        print("   🔍 البحث بالباركود أو رقم الصنف")
        print("   ⚠️ رسائل خطأ للأصناف غير الموجودة")
        print("   ⏰ إخفاء الرسائل تلقائياً بعد 3 ثوانٍ")
        print("   🖱️ إخفاء الرسائل عند النقر")
    else:
        print("\n❌ فشل في بعض الاختبارات!")
    
    print("\n" + "=" * 60)
