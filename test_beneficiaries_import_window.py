#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
شاشة اختبار استيراد المستفيدين
Test Window for Beneficiaries Import
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
import pandas as pd
import os
import sys
from datetime import datetime
import tempfile

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import db_manager
from models import Beneficiary, Department, Unit
from utils.excel_import_manager import ExcelImportManager
from utils.threading_manager import run_in_background

class TestBeneficiariesImportWindow:
    """شاشة اختبار استيراد المستفيدين"""
    
    def __init__(self):
        self.window = ttk_bs.Toplevel()
        self.window.title("شاشة اختبار استيراد المستفيدين")
        self.window.geometry("800x600")
        self.window.resizable(True, True)
        
        # متغيرات لحفظ البيانات
        self.test_results = []
        
        self.setup_ui()
        self.load_current_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        # إطار العنوان
        title_frame = ttk_bs.Frame(self.window)
        title_frame.pack(fill=X, padx=10, pady=5)
        
        ttk_bs.Label(
            title_frame,
            text="🧪 شاشة اختبار استيراد المستفيدين",
            font=("Arial", 16, "bold"),
            bootstyle="primary"
        ).pack()
        
        # إطار الأزرار
        buttons_frame = ttk_bs.Frame(self.window)
        buttons_frame.pack(fill=X, padx=10, pady=5)
        
        # زر إنشاء ملف اختبار
        ttk_bs.Button(
            buttons_frame,
            text="📝 إنشاء ملف Excel للاختبار",
            command=self.create_test_excel,
            bootstyle="success",
            width=25
        ).pack(side=LEFT, padx=5)
        
        # زر استيراد من Excel
        ttk_bs.Button(
            buttons_frame,
            text="📥 استيراد من Excel",
            command=self.import_from_excel,
            bootstyle="primary",
            width=25
        ).pack(side=LEFT, padx=5)
        
        # زر تحديث البيانات
        ttk_bs.Button(
            buttons_frame,
            text="🔄 تحديث البيانات",
            command=self.load_current_data,
            bootstyle="info",
            width=25
        ).pack(side=LEFT, padx=5)
        
        # زر تنظيف البيانات التجريبية
        ttk_bs.Button(
            buttons_frame,
            text="🧹 تنظيف البيانات التجريبية",
            command=self.clean_test_data,
            bootstyle="warning",
            width=25
        ).pack(side=LEFT, padx=5)
        
        # إطار المعلومات
        info_frame = ttk_bs.LabelFrame(self.window, text="معلومات الاختبار", padding=10)
        info_frame.pack(fill=X, padx=10, pady=5)
        
        self.info_text = tk.Text(info_frame, height=8, wrap=tk.WORD, font=("Arial", 10))
        scrollbar_info = ttk_bs.Scrollbar(info_frame, orient=VERTICAL, command=self.info_text.yview)
        self.info_text.configure(yscrollcommand=scrollbar_info.set)
        
        self.info_text.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar_info.pack(side=RIGHT, fill=Y)
        
        # إطار البيانات الحالية
        data_frame = ttk_bs.LabelFrame(self.window, text="البيانات الحالية", padding=10)
        data_frame.pack(fill=BOTH, expand=True, padx=10, pady=5)
        
        # جدول البيانات
        columns = ("الرقم", "الاسم", "الرقم العام", "الرتبة", "الإدارة", "الوحدة", "الحالة")
        self.tree = ttk_bs.Treeview(data_frame, columns=columns, show="headings", height=15)
        
        # تعيين عناوين الأعمدة
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=100, anchor=CENTER)
        
        # شريط التمرير للجدول
        scrollbar_tree = ttk_bs.Scrollbar(data_frame, orient=VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar_tree.set)
        
        self.tree.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar_tree.pack(side=RIGHT, fill=Y)
        
        # إضافة رسالة ترحيب
        self.log_message("🚀 مرحباً بك في شاشة اختبار استيراد المستفيدين")
        self.log_message("📋 يمكنك إنشاء ملف Excel للاختبار ثم استيراده")
        
    def log_message(self, message):
        """إضافة رسالة إلى سجل المعلومات"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        full_message = f"[{timestamp}] {message}\n"
        
        self.info_text.insert(tk.END, full_message)
        self.info_text.see(tk.END)
        self.window.update()
        
        # طباعة في وحدة التحكم أيضاً
        print(full_message.strip())
    
    def create_test_excel(self):
        """إنشاء ملف Excel للاختبار"""
        try:
            self.log_message("📝 بدء إنشاء ملف Excel للاختبار...")
            
            # التأكد من وجود إدارة ووحدة للاختبار
            self.ensure_test_departments()
            
            # بيانات تجريبية
            test_data = [
                {
                    'الاسم': 'أحمد محمد علي',
                    'الرقم العام': 'TEST001',
                    'الرتبة': 'نقيب',
                    'الإدارة': 'إدارة الاختبار',
                    'الوحدة': 'وحدة الاختبار'
                },
                {
                    'الاسم': 'محمد أحمد حسن',
                    'الرقم العام': 'TEST002',
                    'الرتبة': 'ملازم',
                    'الإدارة': 'إدارة الاختبار',
                    'الوحدة': 'وحدة الاختبار'
                },
                {
                    'الاسم': 'علي حسن محمد',
                    'الرقم العام': 'TEST003',
                    'الرتبة': 'رقيب',
                    'الإدارة': 'إدارة الاختبار',
                    'الوحدة': 'وحدة الاختبار'
                }
            ]
            
            # اختيار مكان حفظ الملف
            file_path = filedialog.asksaveasfilename(
                title="حفظ ملف Excel للاختبار",
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                initialname="test_beneficiaries.xlsx"
            )
            
            if not file_path:
                self.log_message("❌ تم إلغاء إنشاء الملف")
                return
            
            # إنشاء DataFrame وحفظه
            df = pd.DataFrame(test_data)
            df.to_excel(file_path, index=False)
            
            self.log_message(f"✅ تم إنشاء ملف Excel بنجاح: {file_path}")
            self.log_message(f"📊 الملف يحتوي على {len(test_data)} مستفيد للاختبار")
            
            messagebox.showinfo("نجح", f"تم إنشاء ملف Excel للاختبار بنجاح!\n\nالمسار: {file_path}")
            
        except Exception as e:
            error_msg = f"فشل في إنشاء ملف Excel: {e}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("خطأ", error_msg)
    
    def ensure_test_departments(self):
        """التأكد من وجود إدارة ووحدة للاختبار"""
        try:
            # البحث عن إدارة الاختبار
            dept = db_manager.fetch_one("SELECT id FROM departments WHERE name = 'إدارة الاختبار'")
            if not dept:
                # إنشاء إدارة الاختبار
                db_manager.execute_query(
                    "INSERT INTO departments (name, is_active) VALUES (?, ?)",
                    ("إدارة الاختبار", 1)
                )
                self.log_message("✅ تم إنشاء إدارة الاختبار")
            
            # البحث عن وحدة الاختبار
            unit = db_manager.fetch_one("SELECT id FROM units WHERE name = 'وحدة الاختبار'")
            if not unit:
                # إنشاء وحدة الاختبار
                db_manager.execute_query(
                    "INSERT INTO units (name, is_active) VALUES (?, ?)",
                    ("وحدة الاختبار", 1)
                )
                self.log_message("✅ تم إنشاء وحدة الاختبار")
                
        except Exception as e:
            self.log_message(f"⚠️ تحذير: فشل في إنشاء إدارة/وحدة الاختبار: {e}")
    
    def import_from_excel(self):
        """استيراد البيانات من Excel"""
        try:
            self.log_message("📥 بدء عملية استيراد المستفيدين...")
            
            # اختيار ملف Excel
            file_path = filedialog.askopenfilename(
                title="اختر ملف Excel للاستيراد",
                filetypes=[("Excel files", "*.xlsx *.xls"), ("All files", "*.*")]
            )
            
            if not file_path:
                self.log_message("❌ لم يتم اختيار ملف")
                return
            
            self.log_message(f"📁 تم اختيار الملف: {file_path}")
            
            def progress_callback(progress, message):
                self.log_message(f"📊 {progress:.1f}% - {message}")
            
            def cancel_check():
                return False
            
            def on_success(result):
                self.log_message("🎉 تم الانتهاء من الاستيراد!")
                self.log_message(f"✅ نجح: {result.success_count}")
                self.log_message(f"🔄 مكرر: {result.duplicate_count}")
                self.log_message(f"❌ أخطاء: {result.error_count}")
                
                # تحديث البيانات
                self.load_current_data()
                
                # عرض رسالة النجاح
                messagebox.showinfo("نجح الاستيراد", 
                                  f"تم استيراد {result.success_count} مستفيد بنجاح!\n"
                                  f"مكرر: {result.duplicate_count}\n"
                                  f"أخطاء: {result.error_count}")
            
            def on_error(error_msg, traceback_info):
                self.log_message(f"❌ خطأ في الاستيراد: {error_msg}")
                if traceback_info:
                    self.log_message(f"📋 تفاصيل الخطأ: {traceback_info}")
                messagebox.showerror("خطأ في الاستيراد", error_msg)
            
            # تشغيل الاستيراد
            run_in_background(
                parent_window=self.window,
                target_function=ExcelImportManager.import_beneficiaries_from_excel,
                args=(file_path,),
                progress_title="استيراد المستفيدين من Excel",
                progress_message="جاري قراءة ومعالجة ملف Excel...",
                success_callback=on_success,
                error_callback=on_error,
                show_progress=True,
                can_cancel=True,
                progress_callback=progress_callback,
                cancel_check=cancel_check
            )
            
        except Exception as e:
            error_msg = f"فشل في بدء عملية الاستيراد: {e}"
            self.log_message(f"❌ {error_msg}")
            import traceback
            self.log_message(f"📋 تفاصيل الخطأ: {traceback.format_exc()}")
            messagebox.showerror("خطأ", error_msg)
    
    def load_current_data(self):
        """تحميل البيانات الحالية"""
        try:
            self.log_message("🔄 تحديث البيانات...")
            
            # مسح الجدول
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # جلب البيانات من قاعدة البيانات
            beneficiaries = db_manager.fetch_all("""
                SELECT b.id, b.name, b.number, b.rank, d.name as dept_name, u.name as unit_name, b.is_active
                FROM beneficiaries b
                LEFT JOIN departments d ON b.department_id = d.id
                LEFT JOIN units u ON b.unit_id = u.id
                ORDER BY b.id DESC
            """)
            
            # إضافة البيانات للجدول
            for ben in beneficiaries:
                status = "نشط" if ben[6] else "غير نشط"
                self.tree.insert("", "end", values=(
                    ben[0],  # الرقم
                    ben[1],  # الاسم
                    ben[2],  # الرقم العام
                    ben[3] or "",  # الرتبة
                    ben[4] or "",  # الإدارة
                    ben[5] or "",  # الوحدة
                    status   # الحالة
                ))
            
            total_count = len(beneficiaries)
            active_count = sum(1 for ben in beneficiaries if ben[6])
            
            self.log_message(f"📊 تم تحميل {total_count} مستفيد (نشط: {active_count})")
            
        except Exception as e:
            error_msg = f"فشل في تحميل البيانات: {e}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("خطأ", error_msg)
    
    def clean_test_data(self):
        """تنظيف البيانات التجريبية"""
        try:
            if messagebox.askyesno("تأكيد التنظيف", 
                                 "هل تريد حذف جميع البيانات التجريبية؟\n"
                                 "(البيانات التي تبدأ بـ TEST)"):
                
                self.log_message("🧹 بدء تنظيف البيانات التجريبية...")
                
                # حذف المستفيدين التجريبيين
                result = db_manager.execute_query("DELETE FROM beneficiaries WHERE number LIKE 'TEST%'")
                deleted_count = result.rowcount if result else 0
                
                self.log_message(f"✅ تم حذف {deleted_count} مستفيد تجريبي")
                
                # تحديث البيانات
                self.load_current_data()
                
                messagebox.showinfo("تم التنظيف", f"تم حذف {deleted_count} مستفيد تجريبي")
                
        except Exception as e:
            error_msg = f"فشل في تنظيف البيانات: {e}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("خطأ", error_msg)

def main():
    """تشغيل شاشة الاختبار"""
    try:
        # إنشاء نافذة رئيسية مخفية
        root = ttk_bs.Window(themename="cosmo")
        root.withdraw()
        
        # إنشاء شاشة الاختبار
        test_window = TestBeneficiariesImportWindow()
        
        # تشغيل التطبيق
        root.mainloop()
        
    except Exception as e:
        print(f"خطأ في تشغيل شاشة الاختبار: {e}")
        import traceback
        print(f"تفاصيل الخطأ: {traceback.format_exc()}")

if __name__ == "__main__":
    main()
