#!/usr/bin/env python3
"""
العمليات غير المتزامنة - حل مشاكل التعليق
Async Operations - Fix Freezing Issues
"""

import threading
import queue
import time
import tkinter as tk
from tkinter import ttk
import pandas as pd
from concurrent.futures import ThreadPoolExecutor
import gc

class AsyncOperationManager:
    """مدير العمليات غير المتزامنة"""
    
    def __init__(self):
        self.thread_pool = ThreadPoolExecutor(max_workers=3)
        self.active_operations = {}
        self.operation_counter = 0
    
    def run_async_operation(self, operation_func, *args, 
                          progress_callback=None, 
                          success_callback=None, 
                          error_callback=None, 
                          **kwargs):
        """تشغيل عملية غير متزامنة"""
        
        operation_id = self.operation_counter
        self.operation_counter += 1
        
        def worker():
            try:
                if progress_callback:
                    progress_callback(0, "بدء العملية...")
                
                result = operation_func(*args, **kwargs)
                
                if progress_callback:
                    progress_callback(100, "تم الانتهاء")
                
                if success_callback:
                    # تشغيل callback في الخيط الرئيسي
                    tk._default_root.after(0, lambda: success_callback(result))
                
                # إزالة العملية من القائمة النشطة
                if operation_id in self.active_operations:
                    del self.active_operations[operation_id]
                    
            except Exception as e:
                if error_callback:
                    tk._default_root.after(0, lambda: error_callback(str(e)))
                else:
                    print(f"خطأ في العملية {operation_id}: {e}")
                
                if operation_id in self.active_operations:
                    del self.active_operations[operation_id]
        
        # تشغيل العملية في خيط منفصل
        future = self.thread_pool.submit(worker)
        self.active_operations[operation_id] = future
        
        return operation_id
    
    def cancel_operation(self, operation_id):
        """إلغاء عملية"""
        if operation_id in self.active_operations:
            future = self.active_operations[operation_id]
            future.cancel()
            del self.active_operations[operation_id]
            return True
        return False
    
    def cancel_all_operations(self):
        """إلغاء جميع العمليات"""
        for operation_id in list(self.active_operations.keys()):
            self.cancel_operation(operation_id)

class ProgressDialog:
    """نافذة عرض التقدم"""
    
    def __init__(self, parent, title="جاري المعالجة..."):
        self.parent = parent
        self.window = tk.Toplevel(parent)
        self.window.title(title)
        self.window.geometry("400x150")
        self.window.resizable(False, False)
        self.window.transient(parent)
        self.window.grab_set()
        
        # توسيط النافذة
        self.center_window()
        
        # إنشاء العناصر
        self.create_widgets()
        
        # متغيرات التحكم
        self.cancelled = False
        
    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (400 // 2)
        y = (self.window.winfo_screenheight() // 2) - (150 // 2)
        self.window.geometry(f"400x150+{x}+{y}")
    
    def create_widgets(self):
        """إنشاء عناصر النافذة"""
        # إطار رئيسي
        main_frame = ttk.Frame(self.window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # تسمية الحالة
        self.status_label = ttk.Label(main_frame, text="جاري المعالجة...")
        self.status_label.pack(pady=(0, 10))
        
        # شريط التقدم
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            main_frame, 
            variable=self.progress_var,
            maximum=100,
            length=350
        )
        self.progress_bar.pack(pady=(0, 10))
        
        # تسمية النسبة المئوية
        self.percent_label = ttk.Label(main_frame, text="0%")
        self.percent_label.pack(pady=(0, 10))
        
        # زر الإلغاء
        self.cancel_button = ttk.Button(
            main_frame, 
            text="إلغاء", 
            command=self.cancel_operation
        )
        self.cancel_button.pack()
    
    def update_progress(self, progress, status=""):
        """تحديث التقدم"""
        if not self.cancelled:
            self.progress_var.set(progress)
            self.percent_label.config(text=f"{progress:.0f}%")
            
            if status:
                self.status_label.config(text=status)
            
            self.window.update()
    
    def cancel_operation(self):
        """إلغاء العملية"""
        self.cancelled = True
        self.close()
    
    def close(self):
        """إغلاق النافذة"""
        try:
            self.window.destroy()
        except:
            pass

class AsyncExcelImporter:
    """مستورد Excel غير متزامن"""
    
    @staticmethod
    def import_excel_file(file_path, progress_callback=None):
        """استيراد ملف Excel بشكل غير متزامن"""
        try:
            if progress_callback:
                progress_callback(10, "قراءة ملف Excel...")
            
            # قراءة الملف
            df = pd.read_excel(file_path)
            total_rows = len(df)
            
            if progress_callback:
                progress_callback(30, f"تم قراءة {total_rows} صف")
            
            # معالجة البيانات في أجزاء صغيرة
            chunk_size = 100
            processed_data = []
            
            for i in range(0, total_rows, chunk_size):
                chunk = df.iloc[i:i+chunk_size]
                
                # معالجة كل صف في الجزء
                for _, row in chunk.iterrows():
                    processed_row = {}
                    for col in df.columns:
                        processed_row[col] = str(row[col]) if pd.notna(row[col]) else ""
                    processed_data.append(processed_row)
                
                # تحديث التقدم
                progress = 30 + (i / total_rows) * 60
                if progress_callback:
                    progress_callback(progress, f"معالجة البيانات... {i+len(chunk)}/{total_rows}")
                
                # إعطاء فرصة للخيط الرئيسي
                time.sleep(0.01)
            
            if progress_callback:
                progress_callback(100, "تم الانتهاء من الاستيراد")
            
            return processed_data
            
        except Exception as e:
            raise Exception(f"خطأ في استيراد ملف Excel: {e}")

class AsyncDatabaseOperations:
    """عمليات قاعدة البيانات غير المتزامنة"""
    
    @staticmethod
    def batch_insert_items(db_manager, items_data, progress_callback=None):
        """إدراج عدة أصناف بشكل غير متزامن"""
        try:
            total_items = len(items_data)
            
            if progress_callback:
                progress_callback(0, f"بدء إدراج {total_items} صنف...")
            
            # تجميع العمليات في دفعات
            batch_size = 50
            inserted_count = 0
            
            for i in range(0, total_items, batch_size):
                batch = items_data[i:i+batch_size]
                
                # بدء معاملة قاعدة البيانات
                db_manager.begin_transaction()
                
                try:
                    for item_data in batch:
                        # إدراج الصنف
                        db_manager.execute_query("""
                            INSERT INTO added_items 
                            (item_number, item_name, unit, quantity, notes, created_at)
                            VALUES (?, ?, ?, ?, ?, datetime('now'))
                        """, (
                            item_data.get('item_number', ''),
                            item_data.get('item_name', ''),
                            item_data.get('unit', 'قطعة'),
                            float(item_data.get('quantity', 0)),
                            item_data.get('notes', ''),
                        ))
                        
                        inserted_count += 1
                    
                    # تأكيد المعاملة
                    db_manager.commit_transaction()
                    
                    # تحديث التقدم
                    progress = (inserted_count / total_items) * 100
                    if progress_callback:
                        progress_callback(progress, f"تم إدراج {inserted_count}/{total_items} صنف")
                    
                    # إعطاء فرصة للخيط الرئيسي
                    time.sleep(0.01)
                    
                except Exception as e:
                    # إلغاء المعاملة في حالة الخطأ
                    db_manager.rollback_transaction()
                    raise e
            
            if progress_callback:
                progress_callback(100, f"تم إدراج {inserted_count} صنف بنجاح")
            
            return inserted_count
            
        except Exception as e:
            raise Exception(f"خطأ في إدراج الأصناف: {e}")
    
    @staticmethod
    def save_transaction_async(db_manager, transaction_data, progress_callback=None):
        """حفظ معاملة بشكل غير متزامن"""
        try:
            if progress_callback:
                progress_callback(10, "بدء حفظ المعاملة...")
            
            # بدء معاملة قاعدة البيانات
            db_manager.begin_transaction()
            
            try:
                # إدراج المعاملة الرئيسية
                if progress_callback:
                    progress_callback(30, "حفظ بيانات المعاملة...")
                
                transaction_id = db_manager.execute_query("""
                    INSERT INTO transactions 
                    (transaction_number, transaction_date, beneficiary_id, receiver_id, notes, created_at)
                    VALUES (?, ?, ?, ?, ?, datetime('now'))
                """, (
                    transaction_data['transaction_number'],
                    transaction_data['transaction_date'],
                    transaction_data['beneficiary_id'],
                    transaction_data.get('receiver_id'),
                    transaction_data.get('notes', ''),
                ), fetch_id=True)
                
                # إدراج أصناف المعاملة
                if progress_callback:
                    progress_callback(60, "حفظ أصناف المعاملة...")
                
                items = transaction_data.get('items', [])
                for i, item in enumerate(items):
                    # إدراج صنف المعاملة
                    db_manager.execute_query("""
                        INSERT INTO transaction_items 
                        (transaction_id, item_id, quantity, notes)
                        VALUES (?, ?, ?, ?)
                    """, (
                        transaction_id,
                        item['item_id'],
                        item['quantity'],
                        item.get('notes', '')
                    ))
                    
                    # تحديث المخزون
                    db_manager.execute_query("""
                        INSERT INTO inventory_movements 
                        (item_id, movement_type, quantity, transaction_id, notes, created_at)
                        VALUES (?, 'out', ?, ?, ?, datetime('now'))
                    """, (
                        item['item_id'],
                        item['quantity'],
                        transaction_id,
                        f"صرف - معاملة رقم {transaction_data['transaction_number']}"
                    ))
                    
                    # تحديث التقدم
                    progress = 60 + ((i + 1) / len(items)) * 30
                    if progress_callback:
                        progress_callback(progress, f"حفظ الصنف {i+1}/{len(items)}")
                
                # تأكيد المعاملة
                db_manager.commit_transaction()
                
                if progress_callback:
                    progress_callback(100, "تم حفظ المعاملة بنجاح")
                
                return transaction_id
                
            except Exception as e:
                # إلغاء المعاملة في حالة الخطأ
                db_manager.rollback_transaction()
                raise e
                
        except Exception as e:
            raise Exception(f"خطأ في حفظ المعاملة: {e}")

# إنشاء مدير العمليات العام
async_manager = AsyncOperationManager()

def cleanup_async_operations():
    """تنظيف العمليات غير المتزامنة"""
    global async_manager
    if async_manager:
        async_manager.cancel_all_operations()
        async_manager = None
    
    # تنظيف الذاكرة
    gc.collect()