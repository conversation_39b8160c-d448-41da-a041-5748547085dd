#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح مشكلة اختفاء شاشة إدارة الأصناف تلقائياً
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import time
import threading
from unittest.mock import Mock, patch

def test_auto_refresh_behavior():
    """اختبار سلوك التحديث التلقائي"""
    print("🧪 اختبار سلوك التحديث التلقائي...")
    
    try:
        # محاكاة النافذة الرئيسية
        class MockMainWindow:
            def __init__(self):
                self.current_main_content = None
                self.main_frame = Mock()
                self.main_frame.winfo_children.return_value = [Mock()]  # محاكاة وجود محتوى
                self.dashboard_shown = False
                
            def show_dashboard(self):
                """محاكاة عرض لوحة التحكم"""
                self.dashboard_shown = True
                self.current_main_content = 'dashboard'
                print("📊 تم عرض لوحة التحكم")
                
            def refresh_dashboard_data(self):
                """محاكاة تحديث بيانات لوحة التحكم"""
                print("🔄 تحديث بيانات لوحة التحكم...")
                
                # تطبيق المنطق المُصحح
                if (hasattr(self, 'main_frame') and self.main_frame.winfo_children() and 
                    hasattr(self, 'current_main_content') and self.current_main_content == 'dashboard'):
                    self.show_dashboard()
                    print("✅ تم تحديث لوحة التحكم (كانت مفتوحة)")
                else:
                    print("⏭️ تم تخطي تحديث لوحة التحكم (شاشة أخرى مفتوحة)")
        
        # اختبار 1: عندما تكون لوحة التحكم مفتوحة
        print("\n📋 اختبار 1: عندما تكون لوحة التحكم مفتوحة")
        main_window = MockMainWindow()
        main_window.current_main_content = 'dashboard'
        main_window.dashboard_shown = False
        
        main_window.refresh_dashboard_data()
        
        if main_window.dashboard_shown:
            print("✅ تم تحديث لوحة التحكم بشكل صحيح")
        else:
            print("❌ لم يتم تحديث لوحة التحكم")
            return False
        
        # اختبار 2: عندما تكون شاشة إدارة الأصناف مفتوحة
        print("\n📋 اختبار 2: عندما تكون شاشة إدارة الأصناف مفتوحة")
        main_window = MockMainWindow()
        main_window.current_main_content = 'inventory'
        main_window.dashboard_shown = False
        
        main_window.refresh_dashboard_data()
        
        if not main_window.dashboard_shown:
            print("✅ لم يتم تحديث لوحة التحكم (شاشة الأصناف محمية)")
        else:
            print("❌ تم تحديث لوحة التحكم خطأً (يجب عدم التحديث)")
            return False
        
        # اختبار 3: عندما تكون شاشة أخرى مفتوحة
        print("\n📋 اختبار 3: عندما تكون شاشة أخرى مفتوحة")
        main_window = MockMainWindow()
        main_window.current_main_content = 'beneficiaries'
        main_window.dashboard_shown = False
        
        main_window.refresh_dashboard_data()
        
        if not main_window.dashboard_shown:
            print("✅ لم يتم تحديث لوحة التحكم (شاشة أخرى محمية)")
        else:
            print("❌ تم تحديث لوحة التحكم خطأً (يجب عدم التحديث)")
            return False
        
        # اختبار 4: عندما لا يوجد محتوى
        print("\n📋 اختبار 4: عندما لا يوجد محتوى")
        main_window = MockMainWindow()
        main_window.current_main_content = None
        main_window.main_frame.winfo_children.return_value = []  # لا يوجد محتوى
        main_window.dashboard_shown = False
        
        main_window.refresh_dashboard_data()
        
        if not main_window.dashboard_shown:
            print("✅ لم يتم تحديث لوحة التحكم (لا يوجد محتوى)")
        else:
            print("❌ تم تحديث لوحة التحكم خطأً")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التحديث التلقائي: {e}")
        return False

def test_inventory_window_protection():
    """اختبار حماية شاشة إدارة الأصناف من الإغلاق التلقائي"""
    print("\n🧪 اختبار حماية شاشة إدارة الأصناف...")
    
    try:
        # محاكاة سيناريو واقعي
        class MockMainWindow:
            def __init__(self):
                self.current_main_content = None
                self.main_frame = Mock()
                self.main_frame.winfo_children.return_value = [Mock()]
                self.auto_refresh_count = 0
                
            def show_dashboard(self):
                """محاكاة عرض لوحة التحكم"""
                print("📊 تم عرض لوحة التحكم - إغلاق الشاشة الحالية!")
                
            def refresh_dashboard_data(self):
                """محاكاة تحديث بيانات لوحة التحكم"""
                self.auto_refresh_count += 1
                print(f"🔄 تحديث تلقائي #{self.auto_refresh_count}")
                
                # تطبيق المنطق المُصحح
                if (hasattr(self, 'main_frame') and self.main_frame.winfo_children() and 
                    hasattr(self, 'current_main_content') and self.current_main_content == 'dashboard'):
                    self.show_dashboard()
                else:
                    print(f"🛡️ شاشة '{self.current_main_content}' محمية من الإغلاق التلقائي")
        
        # محاكاة فتح شاشة إدارة الأصناف
        main_window = MockMainWindow()
        main_window.current_main_content = 'inventory'
        print("📦 تم فتح شاشة إدارة الأصناف")
        
        # محاكاة عدة دورات تحديث تلقائي
        print("\n⏰ محاكاة التحديث التلقائي كل 5 دقائق...")
        for i in range(3):
            time.sleep(0.1)  # محاكاة مرور الوقت
            main_window.refresh_dashboard_data()
        
        # التحقق من أن الشاشة لا تزال مفتوحة
        if main_window.current_main_content == 'inventory':
            print("✅ شاشة إدارة الأصناف لا تزال مفتوحة بعد التحديثات التلقائية")
            return True
        else:
            print("❌ شاشة إدارة الأصناف تم إغلاقها")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار حماية الشاشة: {e}")
        return False

def test_code_changes_verification():
    """التحقق من التغييرات في الكود"""
    print("\n🧪 التحقق من التغييرات في الكود...")
    
    try:
        # قراءة ملف main_window.py
        with open('ui/main_window.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن السطر المُصحح
        expected_line = "hasattr(self, 'current_main_content') and self.current_main_content == 'dashboard'"
        
        if expected_line in content:
            print("✅ تم العثور على التعديل المطلوب في الكود")
            
            # التحقق من عدم وجود السطر القديم
            old_line = "if hasattr(self, 'main_frame') and self.main_frame.winfo_children():\n                self.show_dashboard()"
            
            if old_line not in content:
                print("✅ تم إزالة السطر القديم بنجاح")
                return True
            else:
                print("⚠️ السطر القديم لا يزال موجود")
                return False
        else:
            print("❌ لم يتم العثور على التعديل المطلوب")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في التحقق من التغييرات: {e}")
        return False

def main():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء اختبارات إصلاح مشكلة اختفاء شاشة إدارة الأصناف")
    print("=" * 70)
    
    tests = [
        ("اختبار سلوك التحديث التلقائي", test_auto_refresh_behavior),
        ("اختبار حماية شاشة إدارة الأصناف", test_inventory_window_protection),
        ("التحقق من التغييرات في الكود", test_code_changes_verification),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                print(f"✅ {test_name}: نجح")
                passed += 1
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {e}")
    
    print("\n" + "="*70)
    print(f"📊 النتائج النهائية: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت!")
        print("✅ إصلاح مشكلة اختفاء شاشة إدارة الأصناف يعمل بشكل صحيح")
        print("\n📋 ملخص الإصلاح:")
        print("   • تم تعديل دالة refresh_dashboard_data() لتتحقق من الشاشة الحالية")
        print("   • شاشة إدارة الأصناف محمية من الإغلاق التلقائي")
        print("   • التحديث التلقائي يعمل فقط عندما تكون لوحة التحكم مفتوحة")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت")
        return False

if __name__ == "__main__":
    main()
