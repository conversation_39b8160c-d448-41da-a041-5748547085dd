#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import sys
import os

# إضافة مسار المشروع
sys.path.append('.')

try:
    from database import db_manager
    
    print('🔄 إعادة تعيين معرفات الأصناف بالقوة لتبدأ من 1...')
    print('=' * 60)

    # إنشاء جدول جديد بنفس البنية
    try:
        print('📋 إنشاء جدول جديد للأصناف...')
        
        # إنشاء جدول جديد
        db_manager.execute_query('''
            CREATE TABLE IF NOT EXISTS added_items_new (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                item_number TEXT NOT NULL,
                item_name TEXT NOT NULL,
                custody_type TEXT,
                classification TEXT,
                unit TEXT,
                current_quantity INTEGER DEFAULT 0,
                data_entry_user TEXT,
                entry_date TEXT,
                notes TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                dispensed_quantity INTEGER DEFAULT 0,
                entered_quantity INTEGER DEFAULT 0,
                barcode TEXT
            )
        ''')
        
        print('✅ تم إنشاء الجدول الجديد')
        
    except Exception as e:
        print(f'❌ خطأ في إنشاء الجدول الجديد: {e}')
        exit(1)

    # نسخ البيانات إلى الجدول الجديد مرتبة حسب رقم الصنف
    try:
        print('\n📦 نسخ البيانات إلى الجدول الجديد...')
        
        # الحصول على البيانات مرتبة حسب رقم الصنف
        items = db_manager.fetch_all('''
            SELECT item_number, item_name, custody_type, classification, unit, 
                   current_quantity, data_entry_user, entry_date, notes, 
                   created_at, updated_at, dispensed_quantity, entered_quantity, barcode
            FROM added_items
            WHERE is_active = 1
            ORDER BY CAST(item_number AS INTEGER)
        ''')
        
        # إدراج البيانات في الجدول الجديد
        for item in items:
            db_manager.execute_query('''
                INSERT INTO added_items_new 
                (item_number, item_name, custody_type, classification, unit, 
                 current_quantity, data_entry_user, entry_date, notes, 
                 is_active, created_at, updated_at, dispensed_quantity, 
                 entered_quantity, barcode)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 1, ?, ?, ?, ?, ?)
            ''', [
                item["item_number"], item["item_name"], item["custody_type"],
                item["classification"], item["unit"], item["current_quantity"],
                item["data_entry_user"], item["entry_date"], item["notes"],
                item["created_at"], item["updated_at"], item["dispensed_quantity"],
                item["entered_quantity"], item["barcode"]
            ])
        
        print(f'✅ تم نسخ {len(items)} صنف إلى الجدول الجديد')
        
    except Exception as e:
        print(f'❌ خطأ في نسخ البيانات: {e}')
        exit(1)

    # حذف الجدول القديم وإعادة تسمية الجديد
    try:
        print('\n🔄 استبدال الجدول القديم بالجديد...')
        
        # حذف الجدول القديم
        db_manager.execute_query('DROP TABLE added_items')
        
        # إعادة تسمية الجدول الجديد
        db_manager.execute_query('ALTER TABLE added_items_new RENAME TO added_items')
        
        # إعادة تعيين التسلسل
        db_manager.execute_query("UPDATE sqlite_sequence SET seq = (SELECT MAX(id) FROM added_items) WHERE name = 'added_items'")
        
        print('✅ تم استبدال الجدول بنجاح')
        
    except Exception as e:
        print(f'❌ خطأ في استبدال الجدول: {e}')
        exit(1)

    # إنشاء جدول جديد لحركات المخزون
    try:
        print('\n📦 إعادة تنظيم حركات المخزون...')
        
        # إنشاء جدول جديد لحركات المخزون
        db_manager.execute_query('''
            CREATE TABLE IF NOT EXISTS inventory_movements_new_temp (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                item_number TEXT NOT NULL,
                movement_type TEXT NOT NULL,
                quantity REAL NOT NULL,
                organization_type TEXT,
                organization_name TEXT,
                notes TEXT,
                user_id INTEGER,
                movement_date DATETIME,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # نسخ حركات المخزون
        movements = db_manager.fetch_all('''
            SELECT item_number, movement_type, quantity, organization_type,
                   organization_name, notes, user_id, movement_date,
                   created_at, updated_at
            FROM inventory_movements_new
            WHERE is_active = 1
            ORDER BY created_at
        ''')
        
        for movement in movements:
            db_manager.execute_query('''
                INSERT INTO inventory_movements_new_temp 
                (item_number, movement_type, quantity, organization_type,
                 organization_name, notes, user_id, movement_date, is_active,
                 created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1, ?, ?)
            ''', [
                movement["item_number"], movement["movement_type"], movement["quantity"],
                movement["organization_type"], movement["organization_name"], 
                movement["notes"], movement["user_id"], movement["movement_date"],
                movement["created_at"], movement["updated_at"]
            ])
        
        # استبدال جدول حركات المخزون
        db_manager.execute_query('DROP TABLE inventory_movements_new')
        db_manager.execute_query('ALTER TABLE inventory_movements_new_temp RENAME TO inventory_movements_new')
        
        print(f'✅ تم إعادة تنظيم {len(movements)} حركة مخزون')
        
    except Exception as e:
        print(f'❌ خطأ في إعادة تنظيم حركات المخزون: {e}')

    # التحقق من النتائج النهائية
    try:
        print('\n📊 التحقق من النتائج النهائية:')
        print('=' * 40)
        
        # فحص الأصناف الجديدة
        new_items_info = db_manager.fetch_one('''
            SELECT MIN(id) as min_id, MAX(id) as max_id, COUNT(*) as count
            FROM added_items
            WHERE is_active = 1
        ''')
        
        print(f'📦 إجمالي الأصناف: {new_items_info["count"]}')
        print(f'📊 أصغر معرف: {new_items_info["min_id"]}')
        print(f'📊 أكبر معرف: {new_items_info["max_id"]}')
        
        # فحص حركات المخزون
        movements_count = db_manager.fetch_one('''
            SELECT COUNT(*) as count
            FROM inventory_movements_new
            WHERE is_active = 1
        ''')
        
        print(f'📦 إجمالي حركات المخزون: {movements_count["count"]}')
        
        # عرض أول 10 أصناف
        first_items = db_manager.fetch_all('''
            SELECT id, item_number, item_name
            FROM added_items
            WHERE is_active = 1
            ORDER BY id
            LIMIT 10
        ''')
        
        print('\n📋 أول 10 أصناف بالمعرفات الجديدة:')
        for item in first_items:
            print(f'  🆔 {item["id"]} | 📦 {item["item_number"]} | 📝 {item["item_name"]}')
        
        # عرض آخر 5 أصناف
        last_items = db_manager.fetch_all('''
            SELECT id, item_number, item_name
            FROM added_items
            WHERE is_active = 1
            ORDER BY id DESC
            LIMIT 5
        ''')
        
        print('\n📋 آخر 5 أصناف:')
        for item in last_items:
            print(f'  🆔 {item["id"]} | 📦 {item["item_number"]} | 📝 {item["item_name"]}')
        
        # فحص التسلسل الجديد
        new_sequence = db_manager.fetch_one('''
            SELECT seq
            FROM sqlite_sequence
            WHERE name = 'added_items'
        ''')
        
        if new_sequence:
            print(f'\n📊 آخر معرف في التسلسل الجديد: {new_sequence["seq"]}')
        
        if new_items_info["min_id"] == 1:
            print('\n🎉 ممتاز! تم إعادة تعيين معرفات الأصناف بنجاح!')
            print('✅ المعرفات تبدأ الآن من 1 وتتسلسل بدون فجوات')
        else:
            print(f'\n⚠️ المعرفات تبدأ من {new_items_info["min_id"]} بدلاً من 1')
        
    except Exception as e:
        print(f'❌ خطأ في التحقق من النتائج: {e}')

except Exception as e:
    print(f'❌ خطأ عام: {e}')
    import traceback
    traceback.print_exc()
