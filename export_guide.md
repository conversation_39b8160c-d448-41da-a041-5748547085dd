# دليل استخدام ميزة التصدير في شاشة عمليات الصرف

## 🎯 نظرة عامة
تم تفعيل زر التصدير في شاشة عمليات الصرف بميزات متقدمة لتصدير البيانات إلى ملفات Excel.

## 📋 الميزات المتاحة

### 1. 📊 التصدير المفصل (مع الأصناف)
- يصدر جميع عمليات الصرف مع تفاصيل كل صنف
- يشمل معلومات العملية والمستفيد والأصناف
- مناسب للتقارير التفصيلية والمراجعة الشاملة

**البيانات المُصدرة:**
- رقم العملية وتاريخها
- بيانات المستفيد (الاسم والرقم)
- تفاصيل كل صنف (الكود، الاسم، الكمية، السعر)
- إجماليات العملية
- مدخل البيانات وتواريخ الإنشاء

### 2. 📋 التصدير المبسط (العمليات فقط)
- يصدر معلومات العمليات الأساسية فقط
- لا يشمل تفاصيل الأصناف
- مناسب للتقارير الإجمالية والإحصائيات

**البيانات المُصدرة:**
- رقم العملية وتاريخها
- بيانات المستفيد
- عدد الأصناف وإجمالي الكمية
- إجمالي القيمة
- حالة العملية ومدخل البيانات

### 3. 🔍 تصدير البيانات المفلترة
- يصدر النتائج المفلترة فقط
- يعمل مع فلاتر البحث والتاريخ
- يتيح اختيار نوع التصدير (مفصل أو مبسط)

## 🚀 كيفية الاستخدام

### الطريقة الأولى: من قائمة التصدير
1. افتح شاشة عمليات الصرف
2. انقر على زر "📤 تصدير" 
3. اختر نوع التصدير من القائمة:
   - 📊 تصدير مفصل (مع الأصناف)
   - 📋 تصدير مبسط (العمليات فقط)
   - 🔍 تصدير البيانات المفلترة فقط

### الطريقة الثانية: اختصار لوحة المفاتيح
- اضغط `Ctrl + E` للتصدير السريع (مبسط)

### الطريقة الثالثة: تصدير البيانات المفلترة
1. استخدم فلاتر البحث لتحديد العمليات المطلوبة
2. انقر على "🔍 تصدير البيانات المفلترة فقط"
3. اختر نوع التصدير في النافذة المنبثقة

## ⚙️ الميزات التقنية

### شريط التقدم
- يعرض تقدم عملية التصدير
- يوضح العملية الحالية قيد المعالجة
- يمكن متابعة العملية بصرياً

### تنسيق الملفات
- ملفات Excel (.xlsx) عالية الجودة
- تنسيق تلقائي لعرض الأعمدة
- أسماء أعمدة باللغة العربية
- ترتيب منطقي للبيانات

### معالجة الأخطاء
- معالجة شاملة للأخطاء المحتملة
- رسائل واضحة في حالة الفشل
- حماية من فقدان البيانات

## 📁 مكان الحفظ
- يمكن اختيار مجلد الحفظ المطلوب
- اسم الملف يتضمن التاريخ والوقت تلقائياً
- صيغة الاسم: `عمليات_الصرف_YYYYMMDD_HHMMSS.xlsx`

## 🔧 متطلبات النظام
- Python 3.8+
- pandas
- openpyxl
- tkinter (مدمج مع Python)

## 📞 الدعم الفني
في حالة مواجهة أي مشاكل:
1. تأكد من تثبيت المكتبات المطلوبة
2. تحقق من صلاحيات الكتابة في مجلد الحفظ
3. راجع ملف السجل للأخطاء التفصيلية

---
**تم تطوير هذه الميزة بواسطة نظام إدارة المخازن والمستودعات**