"""
نافذة تقرير البحث عن عمليات الصرف
Transactions Search Report Window
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, date
from database import db_manager
import csv
import os

class TransactionsReportWindow:
    def __init__(self, parent):
        self.parent = parent
        self.window = tk.Toplevel(parent)
        self.window.title("تقرير البحث عن عمليات الصرف")
        self.window.geometry("1200x800")
        self.window.resizable(True, True)
        
        # متغيرات البحث
        self.beneficiary_var = tk.StringVar()
        self.receiver_var = tk.StringVar()
        self.item_number_var = tk.StringVar()
        self.item_name_var = tk.StringVar()
        self.date_from_var = tk.StringVar()
        self.date_to_var = tk.StringVar()
        self.transaction_number_var = tk.StringVar()
        
        self.setup_ui()
        self.load_initial_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # تكوين الشبكة
        self.window.columnconfigure(0, weight=1)
        self.window.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # العنوان
        title_label = ttk.Label(main_frame, text="تقرير البحث عن عمليات الصرف", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, pady=(0, 20))
        
        # إطار البحث
        search_frame = ttk.LabelFrame(main_frame, text="معايير البحث", padding="10")
        search_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        search_frame.columnconfigure(1, weight=1)
        search_frame.columnconfigure(3, weight=1)
        search_frame.columnconfigure(5, weight=1)
        
        # الصف الأول
        ttk.Label(search_frame, text="المستفيد:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        beneficiary_entry = ttk.Entry(search_frame, textvariable=self.beneficiary_var, width=20)
        beneficiary_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 20))
        
        ttk.Label(search_frame, text="المندوب المستلم:").grid(row=0, column=2, sticky=tk.W, padx=(0, 10))
        receiver_entry = ttk.Entry(search_frame, textvariable=self.receiver_var, width=20)
        receiver_entry.grid(row=0, column=3, sticky=(tk.W, tk.E), padx=(0, 20))
        
        ttk.Label(search_frame, text="رقم السند:").grid(row=0, column=4, sticky=tk.W, padx=(0, 10))
        transaction_entry = ttk.Entry(search_frame, textvariable=self.transaction_number_var, width=15)
        transaction_entry.grid(row=0, column=5, sticky=(tk.W, tk.E))
        
        # الصف الثاني
        ttk.Label(search_frame, text="رقم الصنف:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(10, 0))
        item_number_entry = ttk.Entry(search_frame, textvariable=self.item_number_var, width=20)
        item_number_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(0, 20), pady=(10, 0))
        
        ttk.Label(search_frame, text="اسم الصنف:").grid(row=1, column=2, sticky=tk.W, padx=(0, 10), pady=(10, 0))
        item_name_entry = ttk.Entry(search_frame, textvariable=self.item_name_var, width=20)
        item_name_entry.grid(row=1, column=3, sticky=(tk.W, tk.E), padx=(0, 20), pady=(10, 0))
        
        # الصف الثالث - التواريخ
        ttk.Label(search_frame, text="من تاريخ:").grid(row=2, column=0, sticky=tk.W, padx=(0, 10), pady=(10, 0))
        date_from_entry = ttk.Entry(search_frame, textvariable=self.date_from_var, width=15)
        date_from_entry.grid(row=2, column=1, sticky=tk.W, padx=(0, 20), pady=(10, 0))
        ttk.Label(search_frame, text="(YYYY-MM-DD)", font=('Arial', 8)).grid(row=3, column=1, sticky=tk.W, padx=(0, 20))
        
        ttk.Label(search_frame, text="إلى تاريخ:").grid(row=2, column=2, sticky=tk.W, padx=(0, 10), pady=(10, 0))
        date_to_entry = ttk.Entry(search_frame, textvariable=self.date_to_var, width=15)
        date_to_entry.grid(row=2, column=3, sticky=tk.W, padx=(0, 20), pady=(10, 0))
        ttk.Label(search_frame, text="(YYYY-MM-DD)", font=('Arial', 8)).grid(row=3, column=3, sticky=tk.W, padx=(0, 20))
        
        # أزرار البحث
        buttons_frame = ttk.Frame(search_frame)
        buttons_frame.grid(row=2, column=4, columnspan=2, sticky=tk.E, pady=(10, 0))
        
        ttk.Button(buttons_frame, text="بحث", command=self.search_transactions).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(buttons_frame, text="مسح", command=self.clear_search).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(buttons_frame, text="تصدير Excel", command=self.export_to_excel).pack(side=tk.LEFT, padx=(0, 5))
        
        # إطار النتائج
        results_frame = ttk.LabelFrame(main_frame, text="نتائج البحث", padding="10")
        results_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        results_frame.columnconfigure(0, weight=1)
        results_frame.rowconfigure(0, weight=1)
        
        # جدول النتائج
        columns = ("رقم السند", "تاريخ السند", "المستفيد", "المندوب المستلم", 
                  "رقم الصنف", "اسم الصنف", "الكمية", "الوحدة", "ملاحظات")
        self.tree = ttk.Treeview(results_frame, columns=columns, show="headings", height=20)
        
        # تعريف الأعمدة
        self.tree.heading("رقم السند", text="رقم السند")
        self.tree.heading("تاريخ السند", text="تاريخ السند")
        self.tree.heading("المستفيد", text="المستفيد")
        self.tree.heading("المندوب المستلم", text="المندوب المستلم")
        self.tree.heading("رقم الصنف", text="رقم الصنف")
        self.tree.heading("اسم الصنف", text="اسم الصنف")
        self.tree.heading("الكمية", text="الكمية")
        self.tree.heading("الوحدة", text="الوحدة")
        self.tree.heading("ملاحظات", text="ملاحظات")
        
        # تحديد عرض الأعمدة
        self.tree.column("رقم السند", width=100, anchor=tk.CENTER)
        self.tree.column("تاريخ السند", width=100, anchor=tk.CENTER)
        self.tree.column("المستفيد", width=150, anchor=tk.W)
        self.tree.column("المندوب المستلم", width=150, anchor=tk.W)
        self.tree.column("رقم الصنف", width=100, anchor=tk.CENTER)
        self.tree.column("اسم الصنف", width=200, anchor=tk.W)
        self.tree.column("الكمية", width=80, anchor=tk.CENTER)
        self.tree.column("الوحدة", width=80, anchor=tk.CENTER)
        self.tree.column("ملاحظات", width=150, anchor=tk.W)
        
        # شريط التمرير
        scrollbar_v = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.tree.yview)
        scrollbar_h = ttk.Scrollbar(results_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        self.tree.configure(yscrollcommand=scrollbar_v.set, xscrollcommand=scrollbar_h.set)
        
        # وضع الجدول وأشرطة التمرير
        self.tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar_v.grid(row=0, column=1, sticky=(tk.N, tk.S))
        scrollbar_h.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        # إحصائيات
        stats_frame = ttk.Frame(main_frame)
        stats_frame.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(10, 0))
        
        self.stats_label = ttk.Label(stats_frame, text="عدد النتائج: 0", font=('Arial', 10, 'bold'))
        self.stats_label.pack(side=tk.LEFT)
        
    def load_initial_data(self):
        """تحميل البيانات الأولية"""
        self.search_transactions()
        
    def clear_search(self):
        """مسح معايير البحث"""
        self.beneficiary_var.set("")
        self.receiver_var.set("")
        self.item_number_var.set("")
        self.item_name_var.set("")
        self.date_from_var.set("")
        self.date_to_var.set("")
        self.transaction_number_var.set("")
        
    def search_transactions(self):
        """البحث عن عمليات الصرف"""
        try:
            # مسح النتائج الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # بناء الاستعلام
            query = """
            SELECT DISTINCT
                t.transaction_number,
                t.transaction_date,
                b.name as beneficiary_name,
                r.name as receiver_name,
                COALESCE(i.number, i.id) as item_number,
                i.name as item_name,
                ti.quantity,
                COALESCE(u.symbol, u.name) as unit_symbol,
                ti.notes
            FROM transactions t
            LEFT JOIN beneficiaries b ON t.beneficiary_id = b.id
            LEFT JOIN receivers r ON t.receiver_id = r.id
            LEFT JOIN transaction_items ti ON t.id = ti.transaction_id
            LEFT JOIN items i ON ti.item_id = i.id
            LEFT JOIN units u ON i.unit_id = u.id
            WHERE 1=1
            """
            
            params = []
            
            # إضافة شروط البحث
            if self.beneficiary_var.get().strip():
                query += " AND b.name LIKE ?"
                params.append(f"%{self.beneficiary_var.get().strip()}%")
            
            if self.receiver_var.get().strip():
                query += " AND r.name LIKE ?"
                params.append(f"%{self.receiver_var.get().strip()}%")
            
            if self.transaction_number_var.get().strip():
                query += " AND t.transaction_number LIKE ?"
                params.append(f"%{self.transaction_number_var.get().strip()}%")
            
            if self.item_number_var.get().strip():
                query += " AND (i.number LIKE ? OR CAST(i.id AS TEXT) LIKE ?)"
                search_term = f"%{self.item_number_var.get().strip()}%"
                params.extend([search_term, search_term])
            
            if self.item_name_var.get().strip():
                query += " AND i.name LIKE ?"
                params.append(f"%{self.item_name_var.get().strip()}%")
            
            if self.date_from_var.get().strip():
                query += " AND t.transaction_date >= ?"
                params.append(self.date_from_var.get().strip())
            
            if self.date_to_var.get().strip():
                query += " AND t.transaction_date <= ?"
                params.append(self.date_to_var.get().strip())
            
            query += " ORDER BY t.transaction_date DESC, t.transaction_number"
            
            # تنفيذ الاستعلام
            results = db_manager.fetch_all(query, tuple(params) if params else None)
            
            # إضافة النتائج للجدول
            for row in results:
                self.tree.insert("", tk.END, values=(
                    row[0] or "",  # transaction_number
                    row[1] or "",  # transaction_date
                    row[2] or "",  # beneficiary_name
                    row[3] or "",  # receiver_name
                    row[4] or "",  # item_number
                    row[5] or "",  # item_name
                    row[6] or "",  # quantity
                    row[7] or "",  # unit_symbol
                    row[8] or ""   # notes
                ))
            
            # تحديث الإحصائيات
            count = len(results)
            self.stats_label.config(text=f"عدد النتائج: {count}")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في البحث: {str(e)}")
    
    def export_to_excel(self):
        """تصدير النتائج إلى ملف Excel"""
        try:
            # الحصول على البيانات من الجدول
            data = []
            headers = ["رقم السند", "تاريخ السند", "المستفيد", "المندوب المستلم", 
                      "رقم الصنف", "اسم الصنف", "الكمية", "الوحدة", "ملاحظات"]
            data.append(headers)
            
            for item in self.tree.get_children():
                values = self.tree.item(item)['values']
                data.append(values)
            
            if len(data) <= 1:
                messagebox.showwarning("تحذير", "لا توجد بيانات للتصدير")
                return
            
            # إنشاء اسم الملف
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"transactions_report_{timestamp}.csv"
            filepath = os.path.join(os.path.expanduser("~"), "Desktop", filename)
            
            # كتابة البيانات إلى ملف CSV
            with open(filepath, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerows(data)
            
            messagebox.showinfo("نجح", f"تم تصدير التقرير بنجاح إلى:\n{filepath}")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصدير التقرير: {str(e)}")

def main():
    """اختبار النافذة"""
    root = tk.Tk()
    root.withdraw()  # إخفاء النافذة الرئيسية
    
    app = TransactionsReportWindow(root)
    root.mainloop()

if __name__ == "__main__":
    main()