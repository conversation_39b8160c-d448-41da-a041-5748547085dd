#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح البيانات غير النشطة في الجدول التنظيمي
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import db_manager
from models import OrganizationalChart

def fix_inactive_organizational_chart_data():
    """إصلاح البيانات غير النشطة في الجدول التنظيمي"""
    print("🔧 بدء إصلاح البيانات غير النشطة في الجدول التنظيمي...")
    
    try:
        # فحص الحالة الحالية
        total_result = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart")
        total_count = total_result[0] if total_result else 0
        
        active_result = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart WHERE is_active = 1")
        active_count = active_result[0] if active_result else 0
        
        inactive_result = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart WHERE is_active = 0")
        inactive_count = inactive_result[0] if inactive_result else 0
        
        print(f"📊 الحالة الحالية:")
        print(f"   📦 إجمالي البيانات: {total_count}")
        print(f"   ✅ البيانات النشطة: {active_count}")
        print(f"   ❌ البيانات غير النشطة: {inactive_count}")
        
        if inactive_count == 0:
            print("✅ جميع البيانات نشطة بالفعل - لا حاجة للإصلاح")
            return True
        
        # عرض عينة من البيانات غير النشطة
        sample_inactive = db_manager.fetch_all("""
            SELECT id, sequence_number, item_name, item_code, created_at 
            FROM organizational_chart 
            WHERE is_active = 0
            ORDER BY created_at DESC
            LIMIT 10
        """)
        
        if sample_inactive:
            print(f"\n📋 عينة من البيانات غير النشطة (أول 10):")
            for item in sample_inactive:
                print(f"   • ID: {item['id']}, التسلسل: {item['sequence_number']}, الاسم: {item['item_name'][:40]}...")
        
        # تأكيد من المستخدم
        print(f"\n⚠️ سيتم تفعيل {inactive_count} عنصر غير نشط")
        confirm = input("هل تريد المتابعة؟ (y/n): ").lower().strip()
        
        if confirm not in ['y', 'yes', 'نعم', 'ن']:
            print("❌ تم إلغاء العملية")
            return False
        
        # تفعيل جميع البيانات غير النشطة
        print(f"\n🔄 تفعيل {inactive_count} عنصر...")
        
        result = db_manager.execute_query("""
            UPDATE organizational_chart 
            SET is_active = 1 
            WHERE is_active = 0
        """)
        
        affected_rows = result.rowcount if result else 0
        
        print(f"✅ تم تفعيل {affected_rows} عنصر")
        
        # فحص النتيجة
        final_active_result = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart WHERE is_active = 1")
        final_active_count = final_active_result[0] if final_active_result else 0
        
        final_inactive_result = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart WHERE is_active = 0")
        final_inactive_count = final_inactive_result[0] if final_inactive_result else 0
        
        print(f"\n📊 النتيجة النهائية:")
        print(f"   📦 إجمالي البيانات: {total_count}")
        print(f"   ✅ البيانات النشطة: {final_active_count}")
        print(f"   ❌ البيانات غير النشطة: {final_inactive_count}")
        
        if final_inactive_count == 0:
            print("🎉 تم إصلاح جميع البيانات بنجاح!")
            
            # اختبار النموذج
            print("\n🔍 اختبار عرض البيانات من النموذج...")
            items = OrganizationalChart.get_all(active_only=True)
            print(f"✅ يمكن عرض {len(items)} عنصر من النموذج")
            
            return True
        else:
            print(f"⚠️ لا تزال هناك {final_inactive_count} عنصر غير نشط")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في إصلاح البيانات: {e}")
        import traceback
        print(f"تفاصيل الخطأ: {traceback.format_exc()}")
        return False

def main():
    """تشغيل الإصلاح"""
    print("🚀 بدء إصلاح البيانات غير النشطة")
    print("=" * 60)
    
    success = fix_inactive_organizational_chart_data()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ تم الإصلاح بنجاح")
        print("💡 الآن يجب أن تظهر جميع البيانات في شاشة الجدول التنظيمي")
    else:
        print("❌ فشل في الإصلاح")
    
    return success

if __name__ == "__main__":
    main()
