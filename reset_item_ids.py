#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import sys
import os

# إضافة مسار المشروع
sys.path.append('.')

try:
    from database import db_manager
    
    print('🔄 إعادة تعيين معرفات الأصناف لتبدأ من 1...')
    print('=' * 60)

    # إنشاء نسخة احتياطية من البيانات
    try:
        print('💾 إنشاء نسخة احتياطية من البيانات...')
        
        # إنشاء جدول مؤقت للأصناف
        db_manager.execute_query('''
            CREATE TEMPORARY TABLE items_backup AS
            SELECT * FROM added_items
            WHERE is_active = 1
            ORDER BY id
        ''')
        
        # إنشاء جدول مؤقت لحركات المخزون
        db_manager.execute_query('''
            CREATE TEMPORARY TABLE movements_backup AS
            SELECT * FROM inventory_movements_new
            WHERE is_active = 1
        ''')
        
        print('✅ تم إنشاء النسخة الاحتياطية')
        
    except Exception as e:
        print(f'❌ خطأ في إنشاء النسخة الاحتياطية: {e}')
        exit(1)

    # حذف البيانات الحالية
    try:
        print('\n🗑️ حذف البيانات الحالية...')
        
        db_manager.execute_query('DELETE FROM added_items WHERE is_active = 1')
        db_manager.execute_query('DELETE FROM inventory_movements_new WHERE is_active = 1')
        
        # إعادة تعيين التسلسل
        db_manager.execute_query("DELETE FROM sqlite_sequence WHERE name = 'added_items'")
        
        print('✅ تم حذف البيانات الحالية')
        
    except Exception as e:
        print(f'❌ خطأ في حذف البيانات: {e}')
        exit(1)

    # إعادة إدراج الأصناف بمعرفات جديدة
    try:
        print('\n📦 إعادة إدراج الأصناف بمعرفات جديدة...')
        
        # الحصول على الأصناف من النسخة الاحتياطية
        backup_items = db_manager.fetch_all('''
            SELECT item_number, item_name, custody_type, classification, unit, 
                   current_quantity, data_entry_user, entry_date, notes, 
                   created_at, updated_at, dispensed_quantity, entered_quantity, barcode
            FROM items_backup
            ORDER BY CAST(item_number AS INTEGER)
        ''')
        
        # إنشاء خريطة لربط أرقام الأصناف بالمعرفات الجديدة
        item_number_to_new_id = {}
        
        for item in backup_items:
            # إدراج الصنف بمعرف جديد
            cursor = db_manager.execute_query('''
                INSERT INTO added_items 
                (item_number, item_name, custody_type, classification, unit, 
                 current_quantity, data_entry_user, entry_date, notes, 
                 is_active, created_at, updated_at, dispensed_quantity, 
                 entered_quantity, barcode)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 1, ?, ?, ?, ?, ?)
            ''', [
                item["item_number"], item["item_name"], item["custody_type"],
                item["classification"], item["unit"], item["current_quantity"],
                item["data_entry_user"], item["entry_date"], item["notes"],
                item["created_at"], item["updated_at"], item["dispensed_quantity"],
                item["entered_quantity"], item["barcode"]
            ])
            
            # حفظ المعرف الجديد
            new_id = cursor.lastrowid
            item_number_to_new_id[item["item_number"]] = new_id
        
        print(f'✅ تم إعادة إدراج {len(backup_items)} صنف بمعرفات جديدة')
        
    except Exception as e:
        print(f'❌ خطأ في إعادة إدراج الأصناف: {e}')
        exit(1)

    # إعادة إدراج حركات المخزون
    try:
        print('\n📦 إعادة إدراج حركات المخزون...')
        
        # الحصول على حركات المخزون من النسخة الاحتياطية
        backup_movements = db_manager.fetch_all('''
            SELECT item_number, movement_type, quantity, organization_type,
                   organization_name, notes, user_id, movement_date,
                   created_at, updated_at
            FROM movements_backup
            ORDER BY created_at
        ''')
        
        for movement in backup_movements:
            db_manager.execute_query('''
                INSERT INTO inventory_movements_new 
                (item_number, movement_type, quantity, organization_type,
                 organization_name, notes, user_id, movement_date, is_active,
                 created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1, ?, ?)
            ''', [
                movement["item_number"], movement["movement_type"], movement["quantity"],
                movement["organization_type"], movement["organization_name"], 
                movement["notes"], movement["user_id"], movement["movement_date"],
                movement["created_at"], movement["updated_at"]
            ])
        
        print(f'✅ تم إعادة إدراج {len(backup_movements)} حركة مخزون')
        
    except Exception as e:
        print(f'❌ خطأ في إعادة إدراج حركات المخزون: {e}')

    # التحقق من النتائج
    try:
        print('\n📊 التحقق من النتائج:')
        print('=' * 40)
        
        # فحص الأصناف الجديدة
        new_items_info = db_manager.fetch_one('''
            SELECT MIN(id) as min_id, MAX(id) as max_id, COUNT(*) as count
            FROM added_items
            WHERE is_active = 1
        ''')
        
        print(f'📦 إجمالي الأصناف: {new_items_info["count"]}')
        print(f'📊 أصغر معرف: {new_items_info["min_id"]}')
        print(f'📊 أكبر معرف: {new_items_info["max_id"]}')
        
        # فحص حركات المخزون
        movements_count = db_manager.fetch_one('''
            SELECT COUNT(*) as count
            FROM inventory_movements_new
            WHERE is_active = 1
        ''')
        
        print(f'📦 إجمالي حركات المخزون: {movements_count["count"]}')
        
        # عرض أول 10 أصناف
        first_items = db_manager.fetch_all('''
            SELECT id, item_number, item_name
            FROM added_items
            WHERE is_active = 1
            ORDER BY id
            LIMIT 10
        ''')
        
        print('\n📋 أول 10 أصناف بالمعرفات الجديدة:')
        for item in first_items:
            print(f'  🆔 {item["id"]} | 📦 {item["item_number"]} | 📝 {item["item_name"]}')
        
        # فحص التسلسل الجديد
        new_sequence = db_manager.fetch_one('''
            SELECT seq
            FROM sqlite_sequence
            WHERE name = 'added_items'
        ''')
        
        if new_sequence:
            print(f'\n📊 آخر معرف في التسلسل الجديد: {new_sequence["seq"]}')
        
        print('\n🎉 تم إعادة تعيين معرفات الأصناف بنجاح!')
        print('✅ المعرفات تبدأ الآن من 1 وتتسلسل بدون فجوات')
        
    except Exception as e:
        print(f'❌ خطأ في التحقق من النتائج: {e}')

except Exception as e:
    print(f'❌ خطأ عام: {e}')
    import traceback
    traceback.print_exc()
