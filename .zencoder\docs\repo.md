# Desktop Stores Management System Information

## Summary
A comprehensive desktop application for inventory and warehouse management, providing tools for tracking inventory, managing operations, and generating reports. The application is built with Python and uses a tkinter/ttkbootstrap GUI framework with SQLite database for data storage.

## Structure
- **ui/**: Contains all user interface components and windows
- **utils/**: Utility modules for various functionalities (PDF generation, Excel handling, etc.)
- **assets/**: Application resources and icons
- **data/**: Database and application data storage
- **backups/**: Automatic backup storage
- **reports/**: Generated reports storage
- **logs/**: Application log files

## Language & Runtime
**Language**: Python
**Version**: 3.13.3
**Build System**: cx_Freeze, PyInstaller
**Package Manager**: pip

## Dependencies
**Main Dependencies**:
- ttkbootstrap (1.10.1): Enhanced tkinter UI framework
- Pillow (≥9.0.0): Image processing
- pandas (≥1.5.0): Data manipulation
- sqlite3: Database management
- reportlab (≥3.6.0): PDF generation
- matplotlib (≥3.5.0): Data visualization
- bcrypt (≥4.0.1): Password hashing

**Development Dependencies**:
- cx-<PERSON>ze (≥6.15.6): For creating standalone executables
- pyinstaller (≥5.13.0): Alternative executable builder

## Build & Installation
```bash
# Install dependencies
pip install -r requirements.txt

# Run the application
python main.py

# Create executable with cx_Freeze
python setup.py build

# Create executable with PyInstaller
python create_final_dist_package.py
```

## Main Components
**Entry Point**: main.py
**Database**: SQLite (stores_management.db)
**Configuration**: config.py, settings.json
**Data Models**: models.py
**Database Management**: database.py
**Authentication**: auth_manager.py

## UI Components
The application has a comprehensive UI with multiple windows for different functionalities:
- **main_window.py**: Main application interface
- **login_window.py**: User authentication
- **inventory_window.py**: Inventory management
- **transactions_window.py**: Transaction processing
- **reports_window.py**: Report generation
- **settings_window.py**: Application settings

## Features
- Inventory tracking and management
- Transaction processing (receiving/issuing items)
- User management with role-based permissions
- Organizational structure management
- Comprehensive reporting system
- Data import/export (Excel, PDF)
- Automatic backups
- Multi-language support (Arabic/English)

## Testing
**Test Files**:
- test_final_app.py: Tests the final executable package
- test_fixes.py: Tests specific bug fixes

**Testing Approach**:
- Manual testing through UI
- Functional testing of core components
- Package verification tests

## Deployment
**Distribution Methods**:
- Standalone executable (.exe) for Windows
- Portable package (all files in a single directory)
- No installation required - can be run directly

**Packaging Scripts**:
- create_final_dist_package.py
- create_portable_package.py
- create_ultimate_package.py

## Notes
- The application uses SQLite for data storage with automatic backups
- Supports Arabic and English interfaces
- Designed for Windows environments
- Includes comprehensive user management and permissions system
- No Docker configuration is present - designed as a traditional desktop application