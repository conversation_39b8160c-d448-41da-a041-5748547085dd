#!/usr/bin/env python3
"""
شاشة الإعدادات
Settings Window
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
import json
import os
from pathlib import Path

from config import APP_CONFIG, UI_CONFIG, get_message

class SettingsWindow:
    """شاشة الإعدادات"""
    
    def __init__(self, parent, main_window):
        self.parent = parent
        self.main_window = main_window
        self.settings_window = None
        self.settings_data = {}
        
        # تحميل الإعدادات الحالية
        self.load_current_settings()
        
        self.setup_window()
    
    def setup_window(self):
        """إعداد النافذة"""
        self.settings_window = tk.Toplevel(self.parent)
        self.settings_window.title("⚙️ إعدادات النظام")
        self.settings_window.geometry("800x600")
        self.settings_window.resizable(True, True)

        # توسيط النافذة
        self.center_window()

        # تسجيل النافذة في مراقبة النشاط (معطل)
        # try:
        #     from activity_monitor import register_window_for_activity_monitoring
        #     register_window_for_activity_monitoring(self.settings_window)
        # except Exception as e:
        #     print(f"تحذير: فشل في تسجيل النافذة في مراقبة النشاط: {e}")

        # إعداد المحتوى
        self.setup_content()

        # جعل النافذة في المقدمة
        self.settings_window.lift()
        self.settings_window.focus_force()
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.settings_window.update_idletasks()
        
        screen_width = self.settings_window.winfo_screenwidth()
        screen_height = self.settings_window.winfo_screenheight()
        
        window_width = 800
        window_height = 600
        
        x = max(0, (screen_width - window_width) // 2)
        y = max(0, (screen_height - window_height) // 2)
        
        self.settings_window.geometry(f"{window_width}x{window_height}+{x}+{y}")
    
    def load_current_settings(self):
        """تحميل الإعدادات الحالية"""
        self.settings_data = {
            # إعدادات التطبيق
            'app_name': APP_CONFIG.get('app_name', ''),
            'theme': APP_CONFIG.get('theme', 'cosmo'),
            'language': APP_CONFIG.get('language', 'ar'),
            'auto_backup': APP_CONFIG.get('auto_backup', True),
            'backup_interval_hours': APP_CONFIG.get('backup_interval_hours', 24),
            'session_timeout_minutes': APP_CONFIG.get('session_timeout_minutes', 60),
            'max_login_attempts': APP_CONFIG.get('max_login_attempts', 3),
            'password_min_length': APP_CONFIG.get('password_min_length', 6),
            'low_stock_threshold': APP_CONFIG.get('low_stock_threshold', 5),
            'items_per_page': APP_CONFIG.get('items_per_page', 50),
            'date_format': APP_CONFIG.get('date_format', '%Y-%m-%d'),
            'currency': APP_CONFIG.get('currency', 'ريال سعودي'),
            
            # إعدادات واجهة المستخدم
            'font_family': UI_CONFIG.get('font_family', 'Arial'),
            'font_size': UI_CONFIG.get('font_size_normal', 11),
            'button_width': UI_CONFIG.get('button_width', 15),
            'entry_width': UI_CONFIG.get('entry_width', 30),
        }
    
    def setup_content(self):
        """إعداد محتوى النافذة"""
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.settings_window)
        main_frame.pack(fill=BOTH, expand=True, padx=10, pady=10)
        
        # شريط العنوان
        self.create_header(main_frame)
        
        # دفتر التبويبات
        self.create_tabs(main_frame)
        
        # أزرار الحفظ والإلغاء
        self.create_buttons(main_frame)
    
    def create_header(self, parent):
        """إنشاء شريط العنوان"""
        header_frame = ttk_bs.Frame(parent)
        header_frame.pack(fill=X, pady=(0, 20))
        
        # العنوان
        title_label = ttk_bs.Label(
            header_frame,
            text="⚙️ إعدادات النظام",
            bootstyle="primary"
        )
        title_label.pack(side=LEFT)
        
        # معلومات الإصدار
        version_label = ttk_bs.Label(
            header_frame,
            text=f"الإصدار {APP_CONFIG['app_version']}",
            bootstyle="secondary"
        )
        version_label.pack(side=RIGHT)
    
    def create_tabs(self, parent):
        """إنشاء دفتر التبويبات"""
        # إنشاء دفتر التبويبات
        self.notebook = ttk_bs.Notebook(parent, bootstyle="primary")
        self.notebook.pack(fill=BOTH, expand=True, pady=(0, 20))
        
        # تبويب الإعدادات العامة
        self.create_general_tab()
        
        # تبويب إعدادات الواجهة
        self.create_ui_tab()
        
        # تبويب إعدادات الأمان
        self.create_security_tab()
        
        # تبويب إعدادات النسخ الاحتياطية
        self.create_backup_tab()
        
        # تبويب إعدادات المخزون
        self.create_inventory_tab()
    
    def create_general_tab(self):
        """إنشاء تبويب الإعدادات العامة"""
        general_frame = ttk_bs.Frame(self.notebook)
        self.notebook.add(general_frame, text="🏠 عام")
        
        # إطار المحتوى مع شريط تمرير
        canvas = tk.Canvas(general_frame)
        scrollbar = ttk.Scrollbar(general_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk_bs.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # اسم التطبيق
        ttk_bs.Label(scrollable_frame, text="اسم التطبيق:").pack(anchor=W, pady=(10, 5))
        self.app_name_var = tk.StringVar(value=self.settings_data['app_name'])
        ttk_bs.Entry(scrollable_frame, textvariable=self.app_name_var, width=50).pack(fill=X, padx=10, pady=(0, 15))
        
        # السمة
        ttk_bs.Label(scrollable_frame, text="سمة التطبيق:").pack(anchor=W, pady=(0, 5))
        self.theme_var = tk.StringVar(value=self.settings_data['theme'])
        theme_combo = ttk_bs.Combobox(
            scrollable_frame,
            textvariable=self.theme_var,
            values=["cosmo", "flatly", "journal", "litera", "lumen", "minty", "pulse", "sandstone", "united", "yeti"],
            state="readonly",
            width=47
        )
        theme_combo.pack(fill=X, padx=10, pady=(0, 15))
        
        # اللغة
        ttk_bs.Label(scrollable_frame, text="اللغة:").pack(anchor=W, pady=(0, 5))
        self.language_var = tk.StringVar(value=self.settings_data['language'])
        language_combo = ttk_bs.Combobox(
            scrollable_frame,
            textvariable=self.language_var,
            values=["ar", "en"],
            state="readonly",
            width=47
        )
        language_combo.pack(fill=X, padx=10, pady=(0, 15))
        
        # تنسيق التاريخ
        ttk_bs.Label(scrollable_frame, text="تنسيق التاريخ:").pack(anchor=W, pady=(0, 5))
        self.date_format_var = tk.StringVar(value=self.settings_data['date_format'])
        date_format_combo = ttk_bs.Combobox(
            scrollable_frame,
            textvariable=self.date_format_var,
            values=["%Y-%m-%d", "%d/%m/%Y", "%m/%d/%Y", "%d-%m-%Y"],
            state="readonly",
            width=47
        )
        date_format_combo.pack(fill=X, padx=10, pady=(0, 15))
        
        # العملة
        ttk_bs.Label(scrollable_frame, text="العملة:").pack(anchor=W, pady=(0, 5))
        self.currency_var = tk.StringVar(value=self.settings_data['currency'])
        ttk_bs.Entry(scrollable_frame, textvariable=self.currency_var, width=50).pack(fill=X, padx=10, pady=(0, 15))
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
    
    def create_ui_tab(self):
        """إنشاء تبويب إعدادات الواجهة"""
        ui_frame = ttk_bs.Frame(self.notebook)
        self.notebook.add(ui_frame, text="🎨 الواجهة")
        
        # إطار المحتوى
        content_frame = ttk_bs.Frame(ui_frame)
        content_frame.pack(fill=BOTH, expand=True, padx=20, pady=20)
        
        # خط النص
        ttk_bs.Label(content_frame, text="خط النص:").pack(anchor=W, pady=(0, 5))
        self.font_family_var = tk.StringVar(value=self.settings_data['font_family'])
        font_combo = ttk_bs.Combobox(
            content_frame,
            textvariable=self.font_family_var,
            values=["Arial", "Tahoma", "Segoe UI", "Calibri", "Times New Roman"],
            state="readonly",
            width=47
        )
        font_combo.pack(fill=X, pady=(0, 15))
        
        # حجم الخط
        ttk_bs.Label(content_frame, text="حجم الخط:").pack(anchor=W, pady=(0, 5))
        self.font_size_var = tk.IntVar(value=self.settings_data['font_size'])
        font_size_spin = ttk_bs.Spinbox(
            content_frame,
            from_=8,
            to=20,
            textvariable=self.font_size_var,
            width=47
        )
        font_size_spin.pack(fill=X, pady=(0, 15))
        
        # عرض الأزرار
        ttk_bs.Label(content_frame, text="عرض الأزرار:").pack(anchor=W, pady=(0, 5))
        self.button_width_var = tk.IntVar(value=self.settings_data['button_width'])
        button_width_spin = ttk_bs.Spinbox(
            content_frame,
            from_=10,
            to=30,
            textvariable=self.button_width_var,
            width=47
        )
        button_width_spin.pack(fill=X, pady=(0, 15))
        
        # عرض حقول الإدخال
        ttk_bs.Label(content_frame, text="عرض حقول الإدخال:").pack(anchor=W, pady=(0, 5))
        self.entry_width_var = tk.IntVar(value=self.settings_data['entry_width'])
        entry_width_spin = ttk_bs.Spinbox(
            content_frame,
            from_=20,
            to=50,
            textvariable=self.entry_width_var,
            width=47
        )
        entry_width_spin.pack(fill=X, pady=(0, 15))
    
    def create_security_tab(self):
        """إنشاء تبويب إعدادات الأمان"""
        security_frame = ttk_bs.Frame(self.notebook)
        self.notebook.add(security_frame, text="🔒 الأمان")
        
        # إطار المحتوى
        content_frame = ttk_bs.Frame(security_frame)
        content_frame.pack(fill=BOTH, expand=True, padx=20, pady=20)
        
        # مهلة الجلسة
        ttk_bs.Label(content_frame, text="مهلة الجلسة (بالدقائق):").pack(anchor=W, pady=(0, 5))
        self.session_timeout_var = tk.IntVar(value=self.settings_data['session_timeout_minutes'])
        session_spin = ttk_bs.Spinbox(
            content_frame,
            from_=15,
            to=480,
            textvariable=self.session_timeout_var,
            width=47
        )
        session_spin.pack(fill=X, pady=(0, 15))
        
        # عدد محاولات تسجيل الدخول
        ttk_bs.Label(content_frame, text="عدد محاولات تسجيل الدخول المسموحة:").pack(anchor=W, pady=(0, 5))
        self.max_login_var = tk.IntVar(value=self.settings_data['max_login_attempts'])
        login_spin = ttk_bs.Spinbox(
            content_frame,
            from_=1,
            to=10,
            textvariable=self.max_login_var,
            width=47
        )
        login_spin.pack(fill=X, pady=(0, 15))
        
        # الحد الأدنى لطول كلمة المرور
        ttk_bs.Label(content_frame, text="الحد الأدنى لطول كلمة المرور:").pack(anchor=W, pady=(0, 5))
        self.password_length_var = tk.IntVar(value=self.settings_data['password_min_length'])
        password_spin = ttk_bs.Spinbox(
            content_frame,
            from_=4,
            to=20,
            textvariable=self.password_length_var,
            width=47
        )
        password_spin.pack(fill=X, pady=(0, 15))
    
    def create_backup_tab(self):
        """إنشاء تبويب إعدادات النسخ الاحتياطية"""
        backup_frame = ttk_bs.Frame(self.notebook)
        self.notebook.add(backup_frame, text="💾 النسخ الاحتياطية")
        
        # إطار المحتوى
        content_frame = ttk_bs.Frame(backup_frame)
        content_frame.pack(fill=BOTH, expand=True, padx=20, pady=20)
        
        # تفعيل النسخ الاحتياطية التلقائية
        self.auto_backup_var = tk.BooleanVar(value=self.settings_data['auto_backup'])
        auto_backup_check = ttk_bs.Checkbutton(
            content_frame,
            text="تفعيل النسخ الاحتياطية التلقائية",
            variable=self.auto_backup_var,
            bootstyle="primary"
        )
        auto_backup_check.pack(anchor=W, pady=(0, 15))
        
        # فترة النسخ الاحتياطية
        ttk_bs.Label(content_frame, text="فترة النسخ الاحتياطية (بالساعات):").pack(anchor=W, pady=(0, 5))
        self.backup_interval_var = tk.IntVar(value=self.settings_data['backup_interval_hours'])
        backup_spin = ttk_bs.Spinbox(
            content_frame,
            from_=1,
            to=168,
            textvariable=self.backup_interval_var,
            width=47
        )
        backup_spin.pack(fill=X, pady=(0, 15))
        
        # مسار النسخ الاحتياطية
        ttk_bs.Label(content_frame, text="مسار النسخ الاحتياطية:").pack(anchor=W, pady=(0, 5))
        
        path_frame = ttk_bs.Frame(content_frame)
        path_frame.pack(fill=X, pady=(0, 15))
        
        self.backup_path_var = tk.StringVar(value="./backups")
        path_entry = ttk_bs.Entry(path_frame, textvariable=self.backup_path_var)
        path_entry.pack(side=LEFT, fill=X, expand=True, padx=(0, 10))
        
        browse_btn = ttk_bs.Button(
            path_frame,
            text="تصفح",
            command=self.browse_backup_path,
            bootstyle="outline-primary",
            width=15
        )
        browse_btn.pack(side=RIGHT)
    
    def create_inventory_tab(self):
        """إنشاء تبويب إعدادات المخزون"""
        inventory_frame = ttk_bs.Frame(self.notebook)
        self.notebook.add(inventory_frame, text="📦 المخزون")
        
        # إطار المحتوى
        content_frame = ttk_bs.Frame(inventory_frame)
        content_frame.pack(fill=BOTH, expand=True, padx=20, pady=20)
        
        # حد المخزون المنخفض
        ttk_bs.Label(content_frame, text="حد المخزون المنخفض:").pack(anchor=W, pady=(0, 5))
        self.low_stock_var = tk.IntVar(value=self.settings_data['low_stock_threshold'])
        stock_spin = ttk_bs.Spinbox(
            content_frame,
            from_=1,
            to=100,
            textvariable=self.low_stock_var,
            width=47
        )
        stock_spin.pack(fill=X, pady=(0, 15))
        
        # عدد العناصر في الصفحة
        ttk_bs.Label(content_frame, text="عدد العناصر في الصفحة:").pack(anchor=W, pady=(0, 5))
        self.items_per_page_var = tk.IntVar(value=self.settings_data['items_per_page'])
        items_spin = ttk_bs.Spinbox(
            content_frame,
            from_=10,
            to=200,
            textvariable=self.items_per_page_var,
            width=47
        )
        items_spin.pack(fill=X, pady=(0, 15))
    
    def browse_backup_path(self):
        """تصفح مسار النسخ الاحتياطية"""
        folder_path = filedialog.askdirectory(
            title="اختر مجلد النسخ الاحتياطية",
            initialdir=self.backup_path_var.get()
        )
        if folder_path:
            self.backup_path_var.set(folder_path)
    
    def create_buttons(self, parent):
        """إنشاء أزرار الحفظ والإلغاء"""
        buttons_frame = ttk_bs.Frame(parent)
        buttons_frame.pack(fill=X)
        
        # زر الحفظ
        save_btn = ttk_bs.Button(
            buttons_frame,
            text="💾 حفظ الإعدادات",
            command=self.save_settings,
            bootstyle="success",
            width=20
        )
        save_btn.pack(side=RIGHT, padx=5)
        
        # زر الإلغاء
        cancel_btn = ttk_bs.Button(
            buttons_frame,
            text="❌ إلغاء",
            command=self.settings_window.destroy,
            bootstyle="secondary",
            width=15
        )
        cancel_btn.pack(side=RIGHT, padx=5)
        
        # زر استعادة الافتراضية
        reset_btn = ttk_bs.Button(
            buttons_frame,
            text="🔄 استعادة الافتراضية",
            command=self.reset_to_defaults,
            bootstyle="outline-warning",
            width=22
        )
        reset_btn.pack(side=LEFT)
    
    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            # جمع البيانات من النموذج
            new_settings = {
                'app_name': self.app_name_var.get(),
                'theme': self.theme_var.get(),
                'language': self.language_var.get(),
                'auto_backup': self.auto_backup_var.get(),
                'backup_interval_hours': self.backup_interval_var.get(),
                'session_timeout_minutes': self.session_timeout_var.get(),
                'max_login_attempts': self.max_login_var.get(),
                'password_min_length': self.password_length_var.get(),
                'low_stock_threshold': self.low_stock_var.get(),
                'items_per_page': self.items_per_page_var.get(),
                'date_format': self.date_format_var.get(),
                'currency': self.currency_var.get(),
                'font_family': self.font_family_var.get(),
                'font_size': self.font_size_var.get(),
                'button_width': self.button_width_var.get(),
                'entry_width': self.entry_width_var.get(),
            }
            
            # حفظ الإعدادات في ملف
            settings_file = Path("settings.json")
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(new_settings, f, ensure_ascii=False, indent=2)
            
            messagebox.showinfo("نجح", "تم حفظ الإعدادات بنجاح!\n\nسيتم تطبيق بعض الإعدادات عند إعادة تشغيل التطبيق.")
            self.settings_window.destroy()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ الإعدادات: {e}")
    
    def reset_to_defaults(self):
        """استعادة الإعدادات الافتراضية"""
        if messagebox.askyesno("تأكيد", "هل تريد استعادة جميع الإعدادات إلى القيم الافتراضية؟"):
            # استعادة القيم الافتراضية
            self.app_name_var.set("نظام إدارة المخازن والمستودعات")
            self.theme_var.set("cosmo")
            self.language_var.set("ar")
            self.auto_backup_var.set(True)
            self.backup_interval_var.set(24)
            self.session_timeout_var.set(60)
            self.max_login_var.set(3)
            self.password_length_var.set(6)
            self.low_stock_var.set(5)
            self.items_per_page_var.set(50)
            self.date_format_var.set("%Y-%m-%d")
            self.currency_var.set("ريال سعودي")
            self.font_family_var.set("Arial")
            self.font_size_var.set(11)
            self.button_width_var.set(15)
            self.entry_width_var.set(30)
            
            messagebox.showinfo("تم", "تم استعادة الإعدادات الافتراضية")

# اختبار النافذة
if __name__ == "__main__":
    root = tk.Tk()
    root.withdraw()  # إخفاء النافذة الرئيسية
    
    # إنشاء نافذة الاختبار
    window = SettingsWindow(root, None)
    root.mainloop()
