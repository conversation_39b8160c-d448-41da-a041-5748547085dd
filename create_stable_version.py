#!/usr/bin/env python3
"""
إنشاء إصدار مستقر نهائي - نظام إدارة المخازن
Create Final Stable Version - Desktop Stores Management System
"""

import shutil
from pathlib import Path
import time

def create_stable_version():
    """إنشاء إصدار مستقر نهائي"""
    print("إنشاء الإصدار المستقر النهائي...")
    print("=" * 50)
    
    # استخدام الحزمة الأولى التي كانت تعمل بشكل جيد
    source_dir = Path("Desktop_Stores_Management_Portable")
    stable_dir = Path("نظام_إدارة_المخازن_المستقر")
    
    if not source_dir.exists():
        print("خطأ: الحزمة المصدر غير موجودة!")
        return False
    
    # حذف الإصدار السابق إن وجد
    if stable_dir.exists():
        shutil.rmtree(stable_dir)
        print("تم حذف الإصدار السابق")
    
    # نسخ الحزمة المستقرة
    print("نسخ الحزمة المستقرة...")
    shutil.copytree(source_dir, stable_dir)
    
    # إضافة المجلدات الجديدة
    new_dirs = {
        "imports": "مجلد الاستيراد - ضع ملفات Excel هنا للاستيراد",
        "exports": "مجلد التصدير - يحتوي على الملفات المُصدرة"
    }
    
    for dir_name, description in new_dirs.items():
        dir_path = stable_dir / dir_name
        dir_path.mkdir(exist_ok=True)
        
        readme_file = dir_path / "README.txt"
        readme_file.write_text(description, encoding='utf-8')
        print(f"تم إضافة مجلد: {dir_name}")
    
    # تحديث ملف التشغيل
    batch_content = """@echo off
chcp 65001 > nul
title نظام إدارة المخازن والمستودعات - الإصدار المستقر

cls
echo.
echo ========================================
echo      نظام إدارة المخازن والمستودعات
echo        Desktop Stores Management System
echo ========================================
echo.
echo الإصدار: 1.3.0 المستقر
echo تاريخ البناء: %date% %time%
echo.
echo المميزات:
echo - واجهة مستقرة وموثوقة
echo - أداء محسن ومستقر
echo - حل مشاكل التعليق
echo - سهولة في الاستخدام
echo.
echo ========================================
echo.

cd /d "%~dp0"

echo بدء تشغيل التطبيق...
echo.

if exist "نظام_إدارة_المخازن.exe" (
    start "" "نظام_إدارة_المخازن.exe"
    echo تم تشغيل التطبيق بنجاح
    echo.
    echo بيانات الدخول الافتراضية:
    echo اسم المستخدم: admin
    echo كلمة المرور: admin
    echo.
    echo يرجى تغيير كلمة المرور بعد أول تسجيل دخول
    echo.
    echo يمكنك إغلاق هذه النافذة الآن
    timeout /t 10 > nul
) else (
    echo خطأ: لم يتم العثور على الملف التنفيذي
    echo يرجى التأكد من وجود ملف "نظام_إدارة_المخازن.exe"
    pause
)
"""
    
    batch_file = stable_dir / "تشغيل_البرنامج.bat"
    batch_file.write_text(batch_content, encoding='utf-8')
    print("تم تحديث ملف التشغيل")
    
    # إنشاء ملف معلومات الإصدار المستقر
    version_info = f"""نظام إدارة المخازن والمستودعات
Desktop Stores Management System

الإصدار: 1.3.0 المستقر (Stable Release)
تاريخ البناء: {time.strftime('%Y-%m-%d %H:%M:%S')}
نوع البناء: Stable Portable Package

========================================
مميزات الإصدار المستقر:
========================================

✅ واجهة مستقرة وموثوقة
✅ أداء محسن ومستقر
✅ حل مشاكل التعليق والبطء
✅ سهولة في الاستخدام
✅ توافق عالي مع جميع أنظمة Windows
✅ لا يحتاج صلاحيات خاصة للتشغيل
✅ جميع المكتبات مدمجة

========================================
الوظائف الرئيسية:
========================================

🏪 إدارة المخزون:
- إضافة وتعديل الأصناف
- تتبع الكميات والحركات
- تنبيهات المخزون المنخفض

👥 إدارة المستفيدين:
- إضافة الجهات المستفيدة
- تصنيف حسب الإدارات

📊 المعاملات:
- إنشاء عمليات الصرف
- طباعة الإيصالات
- تتبع العمليات

📈 التقارير:
- تقارير المخزون
- تقارير العمليات
- تصدير Excel و PDF

🔒 الأمان:
- نظام مستخدمين
- صلاحيات متعددة
- نسخ احتياطي آمن

========================================
بيانات الدخول الافتراضية:
========================================

اسم المستخدم: admin
كلمة المرور: admin

⚠️ مهم: غير كلمة المرور فوراً بعد أول تسجيل دخول

========================================
تعليمات التشغيل:
========================================

1️⃣ التشغيل:
- انقر مرتين على "تشغيل_البرنامج.bat"
- أو انقر على "نظام_إدارة_المخازن.exe"

2️⃣ في حالة مشاكل:
- جرب التشغيل بصلاحيات المدير
- تأكد من وجود مساحة كافية على القرص
- أغلق برامج مكافحة الفيروسات مؤقتاً

3️⃣ النقل إلى جهاز آخر:
- انسخ المجلد بالكامل
- لا حاجة لتثبيت أي برامج إضافية

========================================
المجلدات:
========================================

📁 data/: قاعدة البيانات والملفات المهمة
📁 reports/: التقارير المُصدرة
📁 backups/: النسخ الاحتياطية
📁 logs/: ملفات السجل
📁 imports/: ملفات Excel للاستيراد
📁 exports/: الملفات المُصدرة

========================================
نصائح مهمة:
========================================

✅ اعمل نسخة احتياطية دورية
✅ لا تحذف مجلد _internal
✅ احتفظ بمساحة فارغة على القرص
✅ أغلق التطبيق بشكل طبيعي

========================================
الدعم الفني:
========================================

📧 للمساعدة:
- راجع ملفات السجل في مجلد logs/
- تحقق من رسائل الخطأ
- احتفظ بنسخة احتياطية دائماً

© 2025 Desktop Stores Team
جميع الحقوق محفوظة
"""
    
    version_file = stable_dir / "معلومات_الإصدار.txt"
    version_file.write_text(version_info, encoding='utf-8')
    print("تم إنشاء ملف معلومات الإصدار")
    
    # إنشاء دليل سريع
    quick_guide = """دليل البدء السريع
================

🚀 التشغيل:
1. انقر مرتين على "تشغيل_البرنامج.bat"
2. اسم المستخدم: admin
3. كلمة المرور: admin
4. غير كلمة المرور فوراً!

📋 الوظائف الأساسية:
- المخزون: إدارة الأصناف والكميات
- المستفيدون: إدارة الجهات المستفيدة
- المعاملات: عمليات الصرف والاستلام
- التقارير: عرض وطباعة التقارير

📥 استيراد البيانات:
1. ضع ملف Excel في مجلد "imports"
2. من القائمة: ملف > استيراد
3. اختر الملف واتبع التعليمات

💾 النسخ الاحتياطي:
- تلقائي: يومياً في مجلد "backups"
- يدوي: من قائمة "أدوات"

⚠️ تحذيرات:
- لا تحذف مجلد "_internal"
- اعمل نسخة احتياطية دورية
- أغلق التطبيق بشكل طبيعي

للمساعدة الكاملة: راجع "معلومات_الإصدار.txt"
"""
    
    guide_file = stable_dir / "دليل_سريع.txt"
    guide_file.write_text(quick_guide, encoding='utf-8')
    print("تم إنشاء الدليل السريع")
    
    # حساب حجم الحزمة
    total_size = sum(f.stat().st_size for f in stable_dir.rglob('*') if f.is_file())
    size_mb = total_size / (1024 * 1024)
    
    print("\n" + "=" * 50)
    print("🎉 تم إنشاء الإصدار المستقر بنجاح!")
    print(f"📁 اسم المجلد: {stable_dir}")
    print(f"📊 الحجم: {size_mb:.1f} MB")
    print("✅ إصدار مستقر وموثوق")
    print("🚀 جاهز للتوزيع والاستخدام")
    print("=" * 50)
    
    return True

if __name__ == "__main__":
    create_stable_version()