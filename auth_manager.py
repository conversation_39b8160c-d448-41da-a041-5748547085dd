"""
مدير المصادقة والأمان - تطبيق إدارة المخازن
Authentication & Security Manager - Desktop Stores Management System
"""

import hashlib
import secrets
import time
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
import json
import logging

from models import User
from database import db_manager
from config import SECURITY_CONFIG, get_message

logger = logging.getLogger(__name__)

class AuthManager:
    """مدير المصادقة والأمان"""
    
    def __init__(self):
        self.current_user = None
        self.session_data = {}
        self.failed_attempts = {}
        self.session_timeout = SECURITY_CONFIG.get("session_timeout_minutes", 60)
        self.max_attempts = SECURITY_CONFIG.get("max_login_attempts", 3)
        self.lockout_duration = 15  # دقائق
    
    def authenticate(self, username: str, password: str) -> tuple[bool, str, Optional[User]]:
        """
        مصادقة المستخدم
        Returns: (success, message, user)
        """
        try:
            # التحقق من محاولات الدخول الفاشلة
            if self.is_account_locked(username):
                remaining_time = self.get_lockout_remaining_time(username)
                return False, f"الحساب مقفل. المحاولة مرة أخرى بعد {remaining_time} دقيقة", None
            
            # البحث عن المستخدم
            user = User.get_by_username(username)
            if not user:
                self.record_failed_attempt(username)
                return False, get_message("invalid_credentials"), None
            
            # التحقق من حالة المستخدم
            if not user.is_active:
                return False, "الحساب غير نشط. يرجى الاتصال بالمدير", None
            
            # التحقق من كلمة المرور
            if not user.verify_password(password):
                self.record_failed_attempt(username)
                user.failed_login_attempts += 1
                user.save()
                return False, get_message("invalid_credentials"), None
            
            # نجح تسجيل الدخول
            self.clear_failed_attempts(username)
            user.failed_login_attempts = 0
            user.last_login = datetime.now()
            user.save()
            
            # إنشاء جلسة
            self.create_session(user)
            
            # تسجيل العملية
            self.log_activity(user.id, "تسجيل دخول", "users", user.id)
            
            logger.info(f"نجح تسجيل دخول المستخدم: {username}")
            return True, "تم تسجيل الدخول بنجاح", user
            
        except Exception as e:
            logger.error(f"خطأ في مصادقة المستخدم {username}: {e}")
            return False, "حدث خطأ في تسجيل الدخول", None
    
    def logout(self, user: User):
        """تسجيل خروج المستخدم"""
        try:
            if user and user.id:
                # تسجيل العملية
                self.log_activity(user.id, "تسجيل خروج", "users", user.id)
                
                # إنهاء الجلسة
                self.end_session(user)
                
                logger.info(f"تم تسجيل خروج المستخدم: {user.username}")
            
        except Exception as e:
            logger.error(f"خطأ في تسجيل خروج المستخدم: {e}")
    
    def create_session(self, user: User):
        """إنشاء جلسة للمستخدم"""
        try:
            session_id = secrets.token_hex(32)
            session_start = datetime.now()
            session_end = session_start + timedelta(minutes=self.session_timeout)
            
            self.session_data = {
                "session_id": session_id,
                "user_id": user.id,
                "username": user.username,
                "start_time": session_start,
                "end_time": session_end,
                "last_activity": session_start,
                "is_active": True
            }
            
            self.current_user = user
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء الجلسة: {e}")
    
    def end_session(self, user: User):
        """إنهاء جلسة المستخدم"""
        try:
            self.session_data = {}
            self.current_user = None
            
        except Exception as e:
            logger.error(f"خطأ في إنهاء الجلسة: {e}")
    
    def is_session_valid(self) -> bool:
        """التحقق من صحة الجلسة"""
        try:
            if not self.session_data or not self.session_data.get("is_active"):
                return False
            
            current_time = datetime.now()
            end_time = self.session_data.get("end_time")
            
            if not end_time or current_time > end_time:
                return False
            
            # تحديث آخر نشاط
            self.session_data["last_activity"] = current_time
            
            return True
            
        except Exception as e:
            logger.error(f"خطأ في التحقق من صحة الجلسة: {e}")
            return False
    
    def extend_session(self):
        """تمديد الجلسة"""
        try:
            if self.session_data and self.session_data.get("is_active"):
                current_time = datetime.now()
                new_end_time = current_time + timedelta(minutes=self.session_timeout)
                self.session_data["end_time"] = new_end_time
                self.session_data["last_activity"] = current_time
                
        except Exception as e:
            logger.error(f"خطأ في تمديد الجلسة: {e}")
    
    def record_failed_attempt(self, username: str):
        """تسجيل محاولة دخول فاشلة"""
        try:
            current_time = datetime.now()
            
            if username not in self.failed_attempts:
                self.failed_attempts[username] = {
                    "count": 0,
                    "last_attempt": current_time,
                    "locked_until": None
                }
            
            self.failed_attempts[username]["count"] += 1
            self.failed_attempts[username]["last_attempt"] = current_time
            
            # قفل الحساب إذا تجاوز الحد الأقصى
            if self.failed_attempts[username]["count"] >= self.max_attempts:
                locked_until = current_time + timedelta(minutes=self.lockout_duration)
                self.failed_attempts[username]["locked_until"] = locked_until
                
                logger.warning(f"تم قفل الحساب {username} حتى {locked_until}")
            
        except Exception as e:
            logger.error(f"خطأ في تسجيل المحاولة الفاشلة: {e}")
    
    def clear_failed_attempts(self, username: str):
        """مسح محاولات الدخول الفاشلة"""
        try:
            if username in self.failed_attempts:
                del self.failed_attempts[username]
                
        except Exception as e:
            logger.error(f"خطأ في مسح المحاولات الفاشلة: {e}")
    
    def is_account_locked(self, username: str) -> bool:
        """التحقق من قفل الحساب"""
        try:
            if username not in self.failed_attempts:
                return False
            
            locked_until = self.failed_attempts[username].get("locked_until")
            if not locked_until:
                return False
            
            current_time = datetime.now()
            if current_time >= locked_until:
                # انتهت فترة القفل
                self.clear_failed_attempts(username)
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"خطأ في التحقق من قفل الحساب: {e}")
            return False
    
    def get_lockout_remaining_time(self, username: str) -> int:
        """الحصول على الوقت المتبقي لفك القفل (بالدقائق)"""
        try:
            if username not in self.failed_attempts:
                return 0
            
            locked_until = self.failed_attempts[username].get("locked_until")
            if not locked_until:
                return 0
            
            current_time = datetime.now()
            if current_time >= locked_until:
                return 0
            
            remaining = locked_until - current_time
            return int(remaining.total_seconds() / 60) + 1
            
        except Exception as e:
            logger.error(f"خطأ في حساب الوقت المتبقي: {e}")
            return 0
    
    def change_password(self, user: User, old_password: str, new_password: str) -> tuple[bool, str]:
        """تغيير كلمة المرور"""
        try:
            # التحقق من كلمة المرور القديمة
            if not user.verify_password(old_password):
                return False, "كلمة المرور القديمة غير صحيحة"
            
            # التحقق من قوة كلمة المرور الجديدة
            if not self.is_password_strong(new_password):
                return False, "كلمة المرور الجديدة ضعيفة. يجب أن تحتوي على 6 أحرف على الأقل"
            
            # تحديث كلمة المرور
            user.set_password(new_password)
            if user.save():
                # تسجيل العملية
                self.log_activity(user.id, "تغيير كلمة المرور", "users", user.id)
                
                logger.info(f"تم تغيير كلمة مرور المستخدم: {user.username}")
                return True, "تم تغيير كلمة المرور بنجاح"
            else:
                return False, "فشل في حفظ كلمة المرور الجديدة"
            
        except Exception as e:
            logger.error(f"خطأ في تغيير كلمة المرور: {e}")
            return False, "حدث خطأ في تغيير كلمة المرور"
    
    def is_password_strong(self, password: str) -> bool:
        """التحقق من قوة كلمة المرور"""
        try:
            min_length = SECURITY_CONFIG.get("password_min_length", 6)
            
            if len(password) < min_length:
                return False
            
            # يمكن إضافة قواعد إضافية هنا
            # مثل: أحرف كبيرة، أرقام، رموز خاصة
            
            return True
            
        except Exception as e:
            logger.error(f"خطأ في التحقق من قوة كلمة المرور: {e}")
            return False
    
    def reset_password(self, username: str) -> tuple[bool, str, str]:
        """إعادة تعيين كلمة المرور"""
        try:
            user = User.get_by_username(username)
            if not user:
                return False, "المستخدم غير موجود", ""
            
            # إنشاء كلمة مرور مؤقتة
            temp_password = self.generate_temp_password()
            
            # تحديث كلمة المرور
            user.set_password(temp_password)
            if user.save():
                # تسجيل العملية
                self.log_activity(user.id, "إعادة تعيين كلمة المرور", "users", user.id)
                
                logger.info(f"تم إعادة تعيين كلمة مرور المستخدم: {username}")
                return True, "تم إعادة تعيين كلمة المرور بنجاح", temp_password
            else:
                return False, "فشل في حفظ كلمة المرور الجديدة", ""
            
        except Exception as e:
            logger.error(f"خطأ في إعادة تعيين كلمة المرور: {e}")
            return False, "حدث خطأ في إعادة تعيين كلمة المرور", ""
    
    def generate_temp_password(self, length: int = 8) -> str:
        """إنشاء كلمة مرور مؤقتة"""
        import string
        import random
        
        characters = string.ascii_letters + string.digits
        return ''.join(random.choice(characters) for _ in range(length))
    
    def has_permission(self, user: User, module: str, permission: str) -> bool:
        """التحقق من الصلاحيات"""
        try:
            if not user or not user.is_active:
                return False
            
            # المدير له جميع الصلاحيات
            if user.is_admin:
                return True
            
            # التحقق من صلاحيات الدور
            query = """
                SELECT p.* FROM permissions p
                JOIN user_roles ur ON p.role_id = ur.role_id
                WHERE ur.user_id = ? AND p.module_name = ?
            """
            
            permissions = db_manager.fetch_all(query, (user.id, module))
            
            for perm in permissions:
                if perm.get(f"can_{permission}", False):
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"خطأ في التحقق من الصلاحيات: {e}")
            return False
    
    def log_activity(self, user_id: int, action: str, table_name: str = None, 
                    record_id: int = None, old_values: str = None, new_values: str = None):
        """تسجيل نشاط المستخدم"""
        try:
            if not SECURITY_CONFIG.get("audit_log_enabled", True):
                return
            
            db_manager.execute_query("""
                INSERT INTO audit_log (user_id, action, table_name, record_id, old_values, new_values)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (user_id, action, table_name, record_id, old_values, new_values))
            
        except Exception as e:
            logger.error(f"خطأ في تسجيل النشاط: {e}")
    
    def get_user_activities(self, user_id: int, limit: int = 50) -> list:
        """الحصول على أنشطة المستخدم"""
        try:
            query = """
                SELECT * FROM audit_log 
                WHERE user_id = ? 
                ORDER BY created_at DESC 
                LIMIT ?
            """
            
            return db_manager.fetch_all(query, (user_id, limit))
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على أنشطة المستخدم: {e}")
            return []
    
    def get_current_user(self) -> Optional[User]:
        """الحصول على المستخدم الحالي"""
        return self.current_user
    
    def is_logged_in(self) -> bool:
        """التحقق من تسجيل الدخول"""
        return self.current_user is not None and self.is_session_valid()
