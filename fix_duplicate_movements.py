#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import sys
import os

# إضافة مسار المشروع
sys.path.append('.')

try:
    from database import db_manager
    
    print('🔍 فحص وإصلاح حركات المخزون المكررة...')
    print('=' * 60)

    # فحص الحركات المكررة
    try:
        duplicate_movements = db_manager.fetch_all('''
            SELECT item_number, movement_type, quantity, movement_date, 
                   organization_name, notes, user_id, COUNT(*) as count
            FROM inventory_movements_new
            WHERE is_active = 1
            GROUP BY item_number, movement_type, quantity, movement_date, 
                     organization_name, notes, user_id
            HAVING COUNT(*) > 1
            ORDER BY count DESC
        ''')
        
        if duplicate_movements:
            print(f'⚠️ تم العثور على {len(duplicate_movements)} مجموعة من الحركات المكررة:')
            for dup in duplicate_movements:
                print(f'  📦 صنف {dup["item_number"]} | 🔄 {dup["movement_type"]} | 📊 {dup["quantity"]} | 🔢 مكرر {dup["count"]} مرة')
        else:
            print('✅ لا توجد حركات مكررة')
            
    except Exception as e:
        print(f'❌ خطأ في فحص الحركات المكررة: {e}')

    # إنشاء جدول مؤقت للحركات الفريدة
    try:
        print('\n🔄 إنشاء جدول مؤقت للحركات الفريدة...')
        
        # إنشاء جدول مؤقت
        db_manager.execute_query('''
            CREATE TEMPORARY TABLE unique_movements AS
            SELECT MIN(id) as id, item_number, movement_type, quantity, 
                   organization_type, organization_name, notes, user_id, 
                   movement_date, is_active, created_at, updated_at
            FROM inventory_movements_new
            WHERE is_active = 1
            GROUP BY item_number, movement_type, quantity, movement_date, 
                     organization_name, notes, user_id
        ''')
        
        # عد الحركات الفريدة
        unique_count = db_manager.fetch_one('''
            SELECT COUNT(*) as count
            FROM unique_movements
        ''')
        
        print(f'✅ تم إنشاء جدول مؤقت بـ {unique_count["count"]} حركة فريدة')
        
    except Exception as e:
        print(f'❌ خطأ في إنشاء الجدول المؤقت: {e}')

    # حذف الحركات المكررة والاحتفاظ بالفريدة فقط
    try:
        print('\n🗑️ حذف الحركات المكررة...')
        
        # تعطيل الحركات المكررة
        db_manager.execute_query('''
            UPDATE inventory_movements_new 
            SET is_active = 0
            WHERE id NOT IN (SELECT id FROM unique_movements)
            AND is_active = 1
        ''')
        
        # التحقق من النتائج
        final_count = db_manager.fetch_one('''
            SELECT COUNT(*) as count
            FROM inventory_movements_new
            WHERE is_active = 1
        ''')
        
        print(f'✅ تم الاحتفاظ بـ {final_count["count"]} حركة فريدة')
        
    except Exception as e:
        print(f'❌ خطأ في حذف الحركات المكررة: {e}')

    # إنشاء حركات المخزون المفقودة للأصناف التي لا تملك حركات
    try:
        print('\n🔄 إنشاء حركات المخزون المفقودة...')
        
        # البحث عن الأصناف التي لا تملك حركات مخزون
        items_without_movements = db_manager.fetch_all('''
            SELECT ai.id, ai.item_number, ai.item_name, ai.current_quantity, 
                   ai.data_entry_user_id, ai.entry_date
            FROM added_items ai
            LEFT JOIN inventory_movements_new im ON ai.item_number = im.item_number 
                AND im.is_active = 1
            WHERE ai.is_active = 1 AND im.id IS NULL
        ''')
        
        if items_without_movements:
            print(f'📦 تم العثور على {len(items_without_movements)} صنف بدون حركات مخزون')
            
            # إنشاء حركات إضافة للأصناف المفقودة
            for item in items_without_movements:
                try:
                    db_manager.execute_query('''
                        INSERT INTO inventory_movements_new 
                        (item_number, movement_type, quantity, organization_type, 
                         organization_name, notes, user_id, movement_date, is_active)
                        VALUES (?, 'إضافة', ?, 'نظام', 'استيراد البيانات', 
                                'حركة تلقائية لصنف مستورد', ?, ?, 1)
                    ''', [
                        item["item_number"],
                        item["current_quantity"] or 0,
                        item["data_entry_user_id"] or 1,
                        item["entry_date"] or '2025-07-05'
                    ])
                    
                except Exception as e:
                    print(f'❌ خطأ في إنشاء حركة للصنف {item["item_number"]}: {e}')
            
            print(f'✅ تم إنشاء حركات مخزون لـ {len(items_without_movements)} صنف')
        else:
            print('✅ جميع الأصناف لديها حركات مخزون')
            
    except Exception as e:
        print(f'❌ خطأ في إنشاء الحركات المفقودة: {e}')

    # التحقق من النتائج النهائية
    try:
        print('\n📊 النتائج النهائية:')
        print('=' * 40)
        
        final_items_count = db_manager.fetch_one('''
            SELECT COUNT(*) as count
            FROM added_items
            WHERE is_active = 1
        ''')
        
        final_movements_count = db_manager.fetch_one('''
            SELECT COUNT(*) as count
            FROM inventory_movements_new
            WHERE is_active = 1
        ''')
        
        movement_types = db_manager.fetch_all('''
            SELECT movement_type, COUNT(*) as count
            FROM inventory_movements_new
            WHERE is_active = 1
            GROUP BY movement_type
            ORDER BY count DESC
        ''')
        
        print(f'📦 إجمالي الأصناف النشطة: {final_items_count["count"]}')
        print(f'📦 إجمالي حركات المخزون النشطة: {final_movements_count["count"]}')
        print('\n📊 حركات المخزون حسب النوع:')
        for mov_type in movement_types:
            print(f'  🔄 {mov_type["movement_type"]}: {mov_type["count"]} حركة')
            
    except Exception as e:
        print(f'❌ خطأ في عرض النتائج النهائية: {e}')

except Exception as e:
    print(f'❌ خطأ عام: {e}')
    import traceback
    traceback.print_exc()
