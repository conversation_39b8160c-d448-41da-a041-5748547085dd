#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import sys
import os

# إضافة مسار المشروع
sys.path.append('.')

try:
    from database import db_manager
    
    print('🔍 إنشاء حركات المخزون المفقودة...')
    print('=' * 60)

    # فحص بنية جدول added_items أولاً
    try:
        structure = db_manager.fetch_all('''
            PRAGMA table_info(added_items)
        ''')
        
        print('📋 أعمدة جدول added_items:')
        for col in structure:
            print(f'  📄 {col["name"]} ({col["type"]})')
            
    except Exception as e:
        print(f'❌ خطأ في فحص بنية added_items: {e}')

    # البحث عن الأصناف التي لا تملك حركات مخزون
    try:
        items_without_movements = db_manager.fetch_all('''
            SELECT ai.id, ai.item_number, ai.item_name, ai.current_quantity, 
                   ai.created_at
            FROM added_items ai
            LEFT JOIN inventory_movements_new im ON ai.item_number = im.item_number 
                AND im.is_active = 1
            WHERE ai.is_active = 1 AND im.id IS NULL
        ''')
        
        if items_without_movements:
            print(f'\n📦 تم العثور على {len(items_without_movements)} صنف بدون حركات مخزون:')
            
            # عرض أول 10 أصناف كمثال
            for i, item in enumerate(items_without_movements[:10]):
                print(f'  📦 {item["item_number"]}: {item["item_name"]} (كمية: {item["current_quantity"]})')
            
            if len(items_without_movements) > 10:
                print(f'  ... و {len(items_without_movements) - 10} صنف آخر')
            
            # إنشاء حركات إضافة للأصناف المفقودة
            print(f'\n🔄 إنشاء حركات مخزون لـ {len(items_without_movements)} صنف...')
            
            success_count = 0
            for item in items_without_movements:
                try:
                    # تحديد التاريخ
                    movement_date = item["created_at"] if item["created_at"] else '2025-07-05'
                    
                    db_manager.execute_query('''
                        INSERT INTO inventory_movements_new 
                        (item_number, movement_type, quantity, organization_type, 
                         organization_name, notes, user_id, movement_date, is_active)
                        VALUES (?, 'إضافة', ?, 'نظام', 'استيراد البيانات', 
                                'حركة تلقائية لصنف مستورد', 1, ?, 1)
                    ''', [
                        item["item_number"],
                        item["current_quantity"] or 0,
                        movement_date
                    ])
                    
                    success_count += 1
                    
                except Exception as e:
                    print(f'❌ خطأ في إنشاء حركة للصنف {item["item_number"]}: {e}')
            
            print(f'✅ تم إنشاء حركات مخزون لـ {success_count} صنف بنجاح')
            
        else:
            print('✅ جميع الأصناف لديها حركات مخزون')
            
    except Exception as e:
        print(f'❌ خطأ في البحث عن الأصناف المفقودة: {e}')

    # التحقق من النتائج النهائية
    try:
        print('\n📊 النتائج النهائية:')
        print('=' * 40)
        
        final_items_count = db_manager.fetch_one('''
            SELECT COUNT(*) as count
            FROM added_items
            WHERE is_active = 1
        ''')
        
        final_movements_count = db_manager.fetch_one('''
            SELECT COUNT(*) as count
            FROM inventory_movements_new
            WHERE is_active = 1
        ''')
        
        movement_types = db_manager.fetch_all('''
            SELECT movement_type, COUNT(*) as count
            FROM inventory_movements_new
            WHERE is_active = 1
            GROUP BY movement_type
            ORDER BY count DESC
        ''')
        
        print(f'📦 إجمالي الأصناف النشطة: {final_items_count["count"]}')
        print(f'📦 إجمالي حركات المخزون النشطة: {final_movements_count["count"]}')
        print('\n📊 حركات المخزون حسب النوع:')
        for mov_type in movement_types:
            print(f'  🔄 {mov_type["movement_type"]}: {mov_type["count"]} حركة')
            
        # التحقق من التطابق
        if final_items_count["count"] == final_movements_count["count"]:
            print('\n🎉 ممتاز! عدد الأصناف يطابق عدد حركات المخزون')
        else:
            print(f'\n⚠️ لا يزال هناك عدم تطابق: {final_items_count["count"]} صنف مقابل {final_movements_count["count"]} حركة')
            
    except Exception as e:
        print(f'❌ خطأ في عرض النتائج النهائية: {e}')

except Exception as e:
    print(f'❌ خطأ عام: {e}')
    import traceback
    traceback.print_exc()
