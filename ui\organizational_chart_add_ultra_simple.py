"""
نافذة إضافة صنف جديد للجدول التنظيمي - نسخة بسيطة جداً
"""

import tkinter as tk
from tkinter import ttk, messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from models import OrganizationalChart
from ui.success_message import AutoSuccessMessage


class OrganizationalChartAddUltraSimple:
    """نافذة إضافة صنف جديد - نسخة بسيطة جداً"""

    def __init__(self, parent, main_window=None):
        self.parent = parent
        self.main_window = main_window
        self.window = None

        # متغيرات النموذج
        self.sequence_var = tk.IntVar()
        self.item_code_var = tk.StringVar()
        self.item_name_var = tk.StringVar()
        self.unit_var = tk.StringVar()

        self.create_window()

    def create_window(self):
        """إنشاء النافذة"""
        # إنشاء النافذة
        self.window = tk.Toplevel(self.parent)
        self.window.title("إضافة صنف جديد للجدول التنظيمي")
        self.window.geometry("900x450")
        self.window.resizable(False, False)

        # توسيط النافذة
        self.window.update_idletasks()
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        x = (screen_width - 900) // 2
        y = (screen_height - 450) // 2
        self.window.geometry(f"900x450+{x}+{y}")

        # جعل النافذة في المقدمة
        self.window.lift()
        self.window.focus_force()
        self.window.grab_set()

        # تحميل الرقم التسلسلي
        self.load_next_sequence()

        # إنشاء المحتوى
        self.create_content()

    def create_content(self):
        """إنشاء محتوى النافذة"""
        # العنوان
        title_label = tk.Label(
            self.window,
            text="إضافة صنف جديد للجدول التنظيمي",
            font=("Arial", 16, "bold"),
            fg="blue"
        )
        title_label.pack(pady=20)

        # الإطار الرئيسي للحقول
        main_frame = tk.Frame(self.window)
        main_frame.pack(expand=True, fill="both", padx=50, pady=20)

        # الإطار الأيمن (حقلين)
        right_frame = tk.Frame(main_frame)
        right_frame.pack(side="right", fill="both", expand=True, padx=(0, 25))

        # الإطار الأيسر (حقلين)
        left_frame = tk.Frame(main_frame)
        left_frame.pack(side="left", fill="both", expand=True, padx=(25, 0))

        # الحقول في الإطار الأيمن
        # التسلسل
        tk.Label(right_frame, text="التسلسل", font=("Arial", 12)).pack(anchor="e", pady=(20, 5))
        sequence_entry = tk.Entry(
            right_frame,
            textvariable=self.sequence_var,
            width=45,
            state="readonly",
            justify="center",
            font=("Arial", 11)
        )
        sequence_entry.pack(pady=(0, 20))

        # رقم الصنف
        tk.Label(right_frame, text="رقم الصنف *", font=("Arial", 12), fg="red").pack(anchor="e", pady=(0, 5))
        item_code_entry = tk.Entry(
            right_frame,
            textvariable=self.item_code_var,
            width=45,
            justify="center",
            font=("Arial", 11)
        )
        item_code_entry.pack(pady=(0, 20))
        item_code_entry.focus()

        # الحقول في الإطار الأيسر
        # اسم الصنف
        tk.Label(left_frame, text="اسم الصنف *", font=("Arial", 12), fg="red").pack(anchor="e", pady=(20, 5))
        item_name_entry = tk.Entry(
            left_frame,
            textvariable=self.item_name_var,
            width=45,
            justify="center",
            font=("Arial", 11)
        )
        item_name_entry.pack(pady=(0, 20))

        # اسم المعدة
        tk.Label(left_frame, text="اسم المعدة", font=("Arial", 12)).pack(anchor="e", pady=(0, 5))
        unit_entry = tk.Entry(
            left_frame,
            textvariable=self.unit_var,
            width=45,
            justify="center",
            font=("Arial", 11)
        )
        unit_entry.pack(pady=(0, 20))

        # ملاحظة الحقول المطلوبة
        note_frame = tk.Frame(self.window)
        note_frame.pack(pady=5)
        tk.Label(note_frame, text="* الحقول المطلوبة", font=("Arial", 10), fg="red").pack()

        # رسالة إرشادية للحفظ
        instruction_frame = tk.Frame(self.window)
        instruction_frame.pack(pady=10)
        instruction_label = tk.Label(
            instruction_frame,
            text="لإتمام عملية حفظ الصنف الجديد\nاضغط Enter",
            font=("Arial", 12, "bold"),
            fg="green",
            justify="center"
        )
        instruction_label.pack()

        # إطار الأزرار في الأسفل
        buttons_frame = tk.Frame(self.window)
        buttons_frame.pack(pady=30)

        # زر الحفظ
        save_btn = tk.Button(
            buttons_frame,
            text="حفظ الصنف",
            command=self.save_item,
            bg="green",
            fg="white",
            font=("Arial", 12, "bold"),
            width=20,
            height=2
        )
        save_btn.pack(side="right", padx=10)

        # زر الإلغاء
        cancel_btn = tk.Button(
            buttons_frame,
            text="إلغاء",
            command=self.window.destroy,
            bg="red",
            fg="white",
            font=("Arial", 12, "bold"),
            width=20,
            height=2
        )
        cancel_btn.pack(side="left", padx=10)

    def load_next_sequence(self):
        """تحميل الرقم التسلسلي التالي"""
        try:
            next_sequence = OrganizationalChart.get_next_sequence_number()
            self.sequence_var.set(next_sequence)
        except Exception as e:
            print(f"خطأ في تحميل الرقم التسلسلي: {e}")
            self.sequence_var.set(1)

    def save_item(self):
        """حفظ الصنف"""
        try:
            # التحقق من البيانات المطلوبة
            item_code = self.item_code_var.get().strip()
            item_name = self.item_name_var.get().strip()

            if not item_code:
                messagebox.showerror("خطأ", "يرجى إدخال رقم الصنف")
                return

            if not item_name:
                messagebox.showerror("خطأ", "يرجى إدخال اسم الصنف")
                return

            # التحقق من عدم تكرار رقم الصنف
            if OrganizationalChart.is_item_code_exists(item_code):
                messagebox.showerror("خطأ", f"رقم الصنف '{item_code}' موجود مسبقاً")
                return

            # التحقق من عدم تكرار اسم الصنف
            if OrganizationalChart.is_item_name_exists(item_name):
                messagebox.showerror("خطأ", f"اسم الصنف '{item_name}' موجود مسبقاً")
                return

            # إنشاء الصنف الجديد
            item = OrganizationalChart()
            item.sequence_number = self.sequence_var.get()
            item.item_code = item_code
            item.item_name = item_name
            item.unit = self.unit_var.get().strip() if self.unit_var.get().strip() else None
            item.quantity = 0.0

            # حفظ الصنف
            if item.save():
                # تحديث الشاشة الرئيسية
                if self.main_window and hasattr(self.main_window, 'refresh_data'):
                    self.main_window.refresh_data()

                # رسالة نجاح تلقائية تختفي خلال 3 ثواني
                AutoSuccessMessage.show(
                    self.main_window.window if self.main_window else self.parent,
                    f"تم إضافة الصنف '{item_name}' بنجاح ✅\n\nتم حفظ الصنف الجديد في النظام",
                    duration=3000
                )

                # إغلاق النافذة
                self.window.destroy()
            else:
                messagebox.showerror("خطأ", "فشل في حفظ الصنف")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ الصنف: {e}")
            print(f"خطأ في الحفظ: {e}")
            import traceback
            traceback.print_exc()


# اختبار الشاشة
if __name__ == "__main__":
    import sys
    from pathlib import Path
    
    # إضافة مسار المشروع
    project_root = Path(__file__).parent.parent
    sys.path.insert(0, str(project_root))
    
    try:
        # إعداد قاعدة البيانات
        from database import db_manager
        db_manager.init_database()
        
        # إنشاء نافذة اختبار
        root = tk.Tk()
        root.title("اختبار شاشة إضافة صنف - بسيطة جداً")
        root.geometry("300x200")
        
        def open_add_window():
            add_window = OrganizationalChartAddUltraSimple(root)
        
        tk.Button(
            root,
            text="فتح شاشة إضافة صنف جديد",
            command=open_add_window,
            bg="blue",
            fg="white",
            width=25,
            height=3
        ).pack(expand=True)
        
        root.mainloop()
        
    except Exception as e:
        print(f"خطأ: {e}")
        import traceback
        traceback.print_exc()
