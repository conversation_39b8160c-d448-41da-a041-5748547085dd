#!/usr/bin/env python3
"""
مكون حقل الإدخال مع الإكمال التلقائي
Auto-complete Entry Widget
"""

import tkinter as tk
from tkinter import ttk
import ttkbootstrap as ttk_bs
from typing import List, Callable, Optional, Any


class AutocompleteEntry(ttk_bs.Frame):
    """حقل إدخال مع إكمال تلقائي"""
    
    def __init__(self, parent, data_source: List[Any], 
                 display_func: Callable[[Any], str] = None,
                 on_select: Callable[[Any], None] = None,
                 placeholder: str = "",
                 width: int = 30,
                 **kwargs):
        """
        إنشاء حقل إدخال مع إكمال تلقائي
        
        Args:
            parent: النافذة الأب
            data_source: قائمة البيانات للبحث فيها
            display_func: دالة لعرض العنصر (افتراضي: str)
            on_select: دالة يتم استدعاؤها عند اختيار عنصر
            placeholder: نص توضيحي
            width: عرض الحقل
        """
        super().__init__(parent, **kwargs)
        
        self.data_source = data_source
        self.display_func = display_func or (lambda x: str(x))
        self.on_select = on_select
        self.placeholder = placeholder
        self.width = width
        
        # المتغيرات
        self.entry_var = tk.StringVar()
        self.selected_item = None
        self.filtered_items = []
        self.is_dropdown_visible = False
        
        # إنشاء المكونات
        self.setup_widgets()
        
        # ربط الأحداث
        self.setup_events()
    
    def setup_widgets(self):
        """إعداد المكونات"""
        # حقل الإدخال
        self.entry = ttk_bs.Entry(
            self,
            textvariable=self.entry_var,
            width=self.width
        )
        self.entry.pack(fill=tk.X)
        
        # إضافة placeholder
        if self.placeholder:
            self.entry.insert(0, self.placeholder)
            self.entry.configure(foreground='gray')
        
        # قائمة الاقتراحات (مخفية في البداية)
        self.suggestions_frame = ttk_bs.Frame(self)
        
        # Listbox للاقتراحات
        self.suggestions_listbox = tk.Listbox(
            self.suggestions_frame,
            height=6,
            font=('Arial', 10),
            selectmode=tk.SINGLE,
            activestyle='dotbox'
        )
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(
            self.suggestions_frame,
            orient=tk.VERTICAL,
            command=self.suggestions_listbox.yview
        )
        self.suggestions_listbox.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط قائمة الاقتراحات
        self.suggestions_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def setup_events(self):
        """ربط الأحداث"""
        # أحداث حقل الإدخال
        self.entry.bind('<KeyRelease>', self.on_key_release)
        self.entry.bind('<FocusIn>', self.on_focus_in)
        self.entry.bind('<FocusOut>', self.on_focus_out)
        self.entry.bind('<Return>', self.on_enter_pressed)
        self.entry.bind('<Down>', self.on_arrow_down)
        self.entry.bind('<Up>', self.on_arrow_up)
        self.entry.bind('<Escape>', self.hide_suggestions)
        
        # أحداث قائمة الاقتراحات
        self.suggestions_listbox.bind('<Double-Button-1>', self.on_suggestion_double_click)
        self.suggestions_listbox.bind('<Return>', self.on_suggestion_select)
        self.suggestions_listbox.bind('<Button-1>', self.on_suggestion_click)
    
    def on_focus_in(self, event):
        """عند التركيز على الحقل"""
        # إزالة placeholder
        if self.entry.get() == self.placeholder:
            self.entry.delete(0, tk.END)
            self.entry.configure(foreground='black')
    
    def on_focus_out(self, event):
        """عند فقدان التركيز"""
        # إخفاء الاقتراحات بعد تأخير قصير
        self.after(200, self.hide_suggestions)
        
        # إضافة placeholder إذا كان الحقل فارغ
        if not self.entry.get() and self.placeholder:
            self.entry.insert(0, self.placeholder)
            self.entry.configure(foreground='gray')
    
    def on_key_release(self, event):
        """عند تحرير مفتاح"""
        # تجاهل مفاتيح التنقل
        if event.keysym in ['Up', 'Down', 'Left', 'Right', 'Return', 'Tab']:
            return
        
        search_text = self.entry.get().strip()
        
        # إذا كان النص فارغ أو placeholder
        if not search_text or search_text == self.placeholder:
            self.hide_suggestions()
            self.selected_item = None
            return
        
        # البحث في البيانات
        self.filter_suggestions(search_text)
    
    def filter_suggestions(self, search_text: str):
        """تصفية الاقتراحات بناء على النص المدخل"""
        search_text = search_text.lower()
        self.filtered_items = []
        
        for item in self.data_source:
            display_text = self.display_func(item).lower()
            if search_text in display_text:
                self.filtered_items.append(item)
        
        # عرض الاقتراحات
        if self.filtered_items:
            self.show_suggestions()
        else:
            self.hide_suggestions()
    
    def show_suggestions(self):
        """عرض قائمة الاقتراحات"""
        if not self.filtered_items:
            return
        
        # مسح القائمة الحالية
        self.suggestions_listbox.delete(0, tk.END)
        
        # إضافة الاقتراحات
        for item in self.filtered_items[:10]:  # أقصى 10 اقتراحات
            display_text = self.display_func(item)
            self.suggestions_listbox.insert(tk.END, display_text)
        
        # عرض الإطار
        if not self.is_dropdown_visible:
            self.suggestions_frame.pack(fill=tk.X, pady=(2, 0))
            self.is_dropdown_visible = True
        
        # تحديد أول عنصر
        if self.suggestions_listbox.size() > 0:
            self.suggestions_listbox.selection_set(0)
            self.suggestions_listbox.activate(0)
    
    def hide_suggestions(self, event=None):
        """إخفاء قائمة الاقتراحات"""
        if self.is_dropdown_visible:
            self.suggestions_frame.pack_forget()
            self.is_dropdown_visible = False
    
    def on_arrow_down(self, event):
        """عند الضغط على سهم لأسفل"""
        if self.is_dropdown_visible and self.suggestions_listbox.size() > 0:
            current = self.suggestions_listbox.curselection()
            if current:
                next_index = min(current[0] + 1, self.suggestions_listbox.size() - 1)
            else:
                next_index = 0
            
            self.suggestions_listbox.selection_clear(0, tk.END)
            self.suggestions_listbox.selection_set(next_index)
            self.suggestions_listbox.activate(next_index)
            self.suggestions_listbox.see(next_index)
            return 'break'
    
    def on_arrow_up(self, event):
        """عند الضغط على سهم لأعلى"""
        if self.is_dropdown_visible and self.suggestions_listbox.size() > 0:
            current = self.suggestions_listbox.curselection()
            if current:
                prev_index = max(current[0] - 1, 0)
            else:
                prev_index = self.suggestions_listbox.size() - 1
            
            self.suggestions_listbox.selection_clear(0, tk.END)
            self.suggestions_listbox.selection_set(prev_index)
            self.suggestions_listbox.activate(prev_index)
            self.suggestions_listbox.see(prev_index)
            return 'break'
    
    def on_enter_pressed(self, event):
        """عند الضغط على Enter"""
        if self.is_dropdown_visible and self.suggestions_listbox.curselection():
            self.select_current_suggestion()
            return 'break'
    
    def on_suggestion_click(self, event):
        """عند النقر على اقتراح"""
        # تأخير قصير للسماح بالتحديد
        self.after(100, self.select_current_suggestion)
    
    def on_suggestion_double_click(self, event):
        """عند النقر المزدوج على اقتراح"""
        self.select_current_suggestion()
    
    def on_suggestion_select(self, event):
        """عند اختيار اقتراح بـ Enter"""
        self.select_current_suggestion()
    
    def select_current_suggestion(self):
        """اختيار الاقتراح الحالي"""
        selection = self.suggestions_listbox.curselection()
        if not selection or not self.filtered_items:
            return
        
        # الحصول على العنصر المحدد
        index = selection[0]
        if 0 <= index < len(self.filtered_items):
            selected_item = self.filtered_items[index]
            self.select_item(selected_item)
    
    def select_item(self, item):
        """اختيار عنصر"""
        self.selected_item = item
        display_text = self.display_func(item)
        
        # تحديث حقل الإدخال
        self.entry_var.set(display_text)
        
        # إخفاء الاقتراحات
        self.hide_suggestions()
        
        # استدعاء دالة الاختيار
        if self.on_select:
            self.on_select(item)
    
    def get_selected_item(self):
        """الحصول على العنصر المحدد"""
        return self.selected_item
    
    def set_selected_item(self, item):
        """تعيين العنصر المحدد"""
        if item:
            self.select_item(item)
        else:
            self.clear()
    
    def clear(self):
        """مسح الحقل"""
        self.entry_var.set("")
        self.selected_item = None
        self.hide_suggestions()
        
        # إضافة placeholder
        if self.placeholder:
            self.entry.insert(0, self.placeholder)
            self.entry.configure(foreground='gray')
    
    def get_text(self):
        """الحصول على النص المدخل"""
        text = self.entry.get()
        return text if text != self.placeholder else ""
    
    def set_text(self, text: str):
        """تعيين النص"""
        self.entry_var.set(text)
        if text:
            self.entry.configure(foreground='black')
    
    def update_data_source(self, new_data: List[Any]):
        """تحديث مصدر البيانات"""
        self.data_source = new_data
        self.hide_suggestions()
    
    def focus(self):
        """التركيز على الحقل"""
        self.entry.focus_set()