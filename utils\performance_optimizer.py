#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
محسن الأداء العام - Performance Optimizer
يحتوي على تحسينات عامة لأداء التطبيق
"""

import gc
import threading
import time
import tkinter as tk
from typing import Callable, Any
import psutil
import os

class PerformanceOptimizer:
    """محسن الأداء العام"""
    
    def __init__(self):
        self.memory_threshold = 500  # MB
        self.cleanup_interval = 300  # 5 minutes
        self.cleanup_thread = None
        self.running = False
    
    def start_memory_monitor(self):
        """بدء مراقبة الذاكرة"""
        if not self.running:
            self.running = True
            self.cleanup_thread = threading.Thread(target=self._memory_cleanup_loop, daemon=True)
            self.cleanup_thread.start()
            print("تم بدء مراقبة الذاكرة")
    
    def stop_memory_monitor(self):
        """إيقاف مراقبة الذاكرة"""
        self.running = False
        if self.cleanup_thread:
            self.cleanup_thread.join(timeout=1)
        print("تم إيقاف مراقبة الذاكرة")
    
    def _memory_cleanup_loop(self):
        """حلقة تنظيف الذاكرة"""
        while self.running:
            try:
                # الحصول على استخدام الذاكرة
                process = psutil.Process(os.getpid())
                memory_mb = process.memory_info().rss / 1024 / 1024
                
                if memory_mb > self.memory_threshold:
                    print(f"استخدام الذاكرة: {memory_mb:.1f} MB - بدء التنظيف")
                    self.cleanup_memory()
                
                # انتظار قبل الفحص التالي
                time.sleep(self.cleanup_interval)
                
            except Exception as e:
                print(f"خطأ في مراقبة الذاكرة: {e}")
                time.sleep(60)  # انتظار دقيقة في حالة الخطأ
    
    def cleanup_memory(self):
        """تنظيف الذاكرة"""
        try:
            # تشغيل garbage collector
            collected = gc.collect()
            
            # الحصول على استخدام الذاكرة بعد التنظيف
            process = psutil.Process(os.getpid())
            memory_mb = process.memory_info().rss / 1024 / 1024
            
            print(f"تم تنظيف {collected} كائن من الذاكرة")
            print(f"استخدام الذاكرة بعد التنظيف: {memory_mb:.1f} MB")
            
        except Exception as e:
            print(f"خطأ في تنظيف الذاكرة: {e}")
    
    @staticmethod
    def optimize_tkinter_performance(root):
        """تحسين أداء Tkinter"""
        try:
            # تحسين إعدادات Tkinter
            root.tk.call('tk', 'scaling', 1.0)
            
            # تحسين الرسم
            root.configure(takefocus=False)
            
            # تقليل تحديثات الشاشة غير الضرورية
            root.update_idletasks()
            
            print("تم تحسين أداء Tkinter")
            
        except Exception as e:
            print(f"خطأ في تحسين Tkinter: {e}")
    
    @staticmethod
    def optimize_database_queries():
        """تحسين استعلامات قاعدة البيانات"""
        try:
            from database import db_manager
            
            # تحسين إعدادات SQLite
            optimizations = [
                "PRAGMA journal_mode = WAL",
                "PRAGMA synchronous = NORMAL", 
                "PRAGMA cache_size = 10000",
                "PRAGMA temp_store = MEMORY",
                "PRAGMA mmap_size = 268435456"  # 256MB
            ]
            
            for pragma in optimizations:
                try:
                    db_manager.execute_query(pragma)
                except Exception as e:
                    print(f"تحذير: فشل في تطبيق {pragma}: {e}")
            
            print("تم تحسين إعدادات قاعدة البيانات")
            
        except Exception as e:
            print(f"خطأ في تحسين قاعدة البيانات: {e}")
    
    @staticmethod
    def batch_process(items, batch_size=100, process_func=None, progress_callback=None):
        """معالجة البيانات على دفعات لتحسين الأداء"""
        if not items or not process_func:
            return []
        
        results = []
        total_items = len(items)
        
        for i in range(0, total_items, batch_size):
            batch = items[i:i + batch_size]
            
            # معالجة الدفعة
            batch_results = process_func(batch)
            results.extend(batch_results)
            
            # تحديث التقدم
            if progress_callback:
                progress = ((i + len(batch)) / total_items) * 100
                progress_callback(progress, f"معالجة {i + len(batch)} من {total_items}")
            
            # تنظيف الذاكرة بين الدفعات
            if i % (batch_size * 5) == 0:  # كل 5 دفعات
                gc.collect()
        
        return results
    
    @staticmethod
    def debounce(wait_time=0.5):
        """ديكوريتر لتأخير تنفيذ الدالة (debouncing)"""
        def decorator(func):
            def wrapper(*args, **kwargs):
                def call_func():
                    func(*args, **kwargs)
                
                # إلغاء المؤقت السابق إن وجد
                if hasattr(wrapper, 'timer'):
                    wrapper.timer.cancel()
                
                # إنشاء مؤقت جديد
                wrapper.timer = threading.Timer(wait_time, call_func)
                wrapper.timer.start()
            
            return wrapper
        return decorator
    
    @staticmethod
    def throttle(wait_time=1.0):
        """ديكوريتر لتحديد معدل تنفيذ الدالة (throttling)"""
        def decorator(func):
            last_called = [0]
            
            def wrapper(*args, **kwargs):
                now = time.time()
                if now - last_called[0] >= wait_time:
                    last_called[0] = now
                    return func(*args, **kwargs)
            
            return wrapper
        return decorator

class UIOptimizer:
    """محسن واجهة المستخدم"""
    
    @staticmethod
    def optimize_treeview(treeview, max_visible_items=1000):
        """تحسين أداء Treeview للبيانات الكبيرة"""
        try:
            # تحديد عدد العناصر المرئية
            if hasattr(treeview, 'configure'):
                treeview.configure(height=min(20, max_visible_items // 50))
            
            # تحسين التمرير
            if hasattr(treeview, 'yview'):
                treeview.bind('<MouseWheel>', lambda e: treeview.yview_scroll(
                    int(-1 * (e.delta / 120)), "units"))
            
            print(f"تم تحسين Treeview للعرض الأمثل")
            
        except Exception as e:
            print(f"خطأ في تحسين Treeview: {e}")
    
    @staticmethod
    def lazy_load_data(treeview, data_loader, page_size=100):
        """تحميل البيانات تدريجياً (Lazy Loading)"""
        try:
            current_page = [0]
            loading = [False]
            
            def load_next_page():
                if loading[0]:
                    return
                
                loading[0] = True
                try:
                    start_idx = current_page[0] * page_size
                    end_idx = start_idx + page_size
                    
                    # تحميل البيانات
                    page_data = data_loader(start_idx, end_idx)
                    
                    # إضافة البيانات للـ Treeview
                    for item in page_data:
                        treeview.insert("", "end", values=item)
                    
                    current_page[0] += 1
                    
                except Exception as e:
                    print(f"خطأ في تحميل الصفحة: {e}")
                finally:
                    loading[0] = False
            
            # ربط التمرير بتحميل البيانات
            def on_scroll(*args):
                # تحقق من الوصول لنهاية القائمة
                if treeview.yview()[1] >= 0.9:  # 90% من التمرير
                    load_next_page()
            
            treeview.bind('<Configure>', lambda e: on_scroll())
            
            # تحميل الصفحة الأولى
            load_next_page()
            
            print("تم تفعيل التحميل التدريجي")
            
        except Exception as e:
            print(f"خطأ في إعداد التحميل التدريجي: {e}")

# إنشاء مثيل عام للمحسن
performance_optimizer = PerformanceOptimizer()

def optimize_application_startup():
    """تحسين بدء التطبيق"""
    try:
        # تحسين قاعدة البيانات
        PerformanceOptimizer.optimize_database_queries()
        
        # بدء مراقبة الذاكرة
        performance_optimizer.start_memory_monitor()
        
        print("تم تحسين بدء التطبيق")
        
    except Exception as e:
        print(f"خطأ في تحسين بدء التطبيق: {e}")

def optimize_application_shutdown():
    """تحسين إغلاق التطبيق"""
    try:
        # إيقاف مراقبة الذاكرة
        performance_optimizer.stop_memory_monitor()
        
        # تنظيف الذاكرة النهائي
        performance_optimizer.cleanup_memory()
        
        print("تم تحسين إغلاق التطبيق")
        
    except Exception as e:
        print(f"خطأ في تحسين إغلاق التطبيق: {e}")