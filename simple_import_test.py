#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار بسيط لاستيراد المستفيدين
Simple Beneficiaries Import Test
"""

import os
import sys
import pandas as pd
import tempfile
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import db_manager
from utils.excel_import_manager import ExcelImportManager

def create_test_excel():
    """إنشاء ملف Excel للاختبار"""
    print("📝 إنشاء ملف Excel للاختبار...")
    
    # التأكد من وجود إدارة ووحدة للاختبار
    ensure_test_departments()
    
    # بيانات تجريبية
    test_data = [
        {
            'الاسم': 'أحمد محمد علي - اختبار',
            'الرقم العام': 'TEST001',
            'الرتبة': 'نقيب',
            'الإدارة': 'إدارة الاختبار',
            'الوحدة': 'وحدة الاختبار'
        },
        {
            'الاسم': 'محمد أحمد حسن - اختبار',
            'الرقم العام': 'TEST002',
            'الرتبة': 'ملازم',
            'الإدارة': 'إدارة الاختبار',
            'الوحدة': 'وحدة الاختبار'
        },
        {
            'الاسم': 'علي حسن محمد - اختبار',
            'الرقم العام': 'TEST003',
            'الرتبة': 'رقيب',
            'الإدارة': 'إدارة الاختبار',
            'الوحدة': 'وحدة الاختبار'
        }
    ]
    
    # إنشاء ملف مؤقت
    temp_file = tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False)
    temp_path = temp_file.name
    temp_file.close()
    
    # إنشاء DataFrame وحفظه
    df = pd.DataFrame(test_data)
    df.to_excel(temp_path, index=False)
    
    print(f"✅ تم إنشاء ملف Excel: {temp_path}")
    print(f"📊 الملف يحتوي على {len(test_data)} مستفيد للاختبار")
    
    return temp_path

def ensure_test_departments():
    """التأكد من وجود إدارة ووحدة للاختبار"""
    try:
        # البحث عن إدارة الاختبار
        dept = db_manager.fetch_one("SELECT id FROM departments WHERE name = 'إدارة الاختبار'")
        if not dept:
            # إنشاء إدارة الاختبار
            db_manager.execute_query(
                "INSERT INTO departments (name, is_active) VALUES (?, ?)",
                ("إدارة الاختبار", 1)
            )
            print("✅ تم إنشاء إدارة الاختبار")
        
        # البحث عن وحدة الاختبار
        unit = db_manager.fetch_one("SELECT id FROM units WHERE name = 'وحدة الاختبار'")
        if not unit:
            # إنشاء وحدة الاختبار
            db_manager.execute_query(
                "INSERT INTO units (name, is_active) VALUES (?, ?)",
                ("وحدة الاختبار", 1)
            )
            print("✅ تم إنشاء وحدة الاختبار")
            
    except Exception as e:
        print(f"⚠️ تحذير: فشل في إنشاء إدارة/وحدة الاختبار: {e}")

def test_import(file_path):
    """اختبار استيراد الملف"""
    print(f"📥 بدء اختبار استيراد الملف: {file_path}")
    
    def progress_callback(progress, message):
        print(f"   📊 {progress:.1f}% - {message}")
    
    def cancel_check():
        return False
    
    try:
        # تنفيذ الاستيراد
        result = ExcelImportManager.import_beneficiaries_from_excel(
            file_path,
            progress_callback=progress_callback,
            cancel_check=cancel_check
        )
        
        print("🎉 تم الانتهاء من الاستيراد!")
        print(f"✅ نجح: {result.success_count}")
        print(f"🔄 مكرر: {result.duplicate_count}")
        print(f"❌ أخطاء: {result.error_count}")
        
        return result
        
    except Exception as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        import traceback
        print(f"📋 تفاصيل الخطأ: {traceback.format_exc()}")
        return None

def show_current_data():
    """عرض البيانات الحالية"""
    print("📊 البيانات الحالية في قاعدة البيانات:")
    
    try:
        beneficiaries = db_manager.fetch_all("""
            SELECT b.id, b.name, b.number, b.rank, d.name as dept_name, u.name as unit_name, b.is_active
            FROM beneficiaries b
            LEFT JOIN departments d ON b.department_id = d.id
            LEFT JOIN units u ON b.unit_id = u.id
            ORDER BY b.id DESC
            LIMIT 10
        """)
        
        if not beneficiaries:
            print("   📭 لا توجد بيانات")
            return
        
        print(f"   📋 عدد المستفيدين: {len(beneficiaries)}")
        print("   " + "-" * 80)
        print("   الرقم | الاسم | الرقم العام | الرتبة | الإدارة | الوحدة | الحالة")
        print("   " + "-" * 80)
        
        for ben in beneficiaries:
            status = "نشط" if ben[6] else "غير نشط"
            print(f"   {ben[0]:4} | {ben[1][:15]:15} | {ben[2]:10} | {ben[3] or '':8} | {ben[4] or '':10} | {ben[5] or '':10} | {status}")
        
    except Exception as e:
        print(f"❌ خطأ في عرض البيانات: {e}")

def clean_test_data():
    """تنظيف البيانات التجريبية"""
    print("🧹 تنظيف البيانات التجريبية...")
    
    try:
        result = db_manager.execute_query("DELETE FROM beneficiaries WHERE number LIKE 'TEST%'")
        deleted_count = result.rowcount if result else 0
        print(f"✅ تم حذف {deleted_count} مستفيد تجريبي")
        
    except Exception as e:
        print(f"❌ خطأ في تنظيف البيانات: {e}")

def main():
    """الدالة الرئيسية"""
    print("🚀 اختبار بسيط لاستيراد المستفيدين")
    print("=" * 60)
    
    while True:
        print("\nاختر العملية:")
        print("1. إنشاء ملف Excel للاختبار")
        print("2. اختبار استيراد ملف موجود")
        print("3. عرض البيانات الحالية")
        print("4. تنظيف البيانات التجريبية")
        print("5. اختبار كامل (إنشاء + استيراد)")
        print("0. خروج")
        
        choice = input("\nأدخل اختيارك (0-5): ").strip()
        
        if choice == "1":
            file_path = create_test_excel()
            print(f"\n📁 يمكنك الآن استيراد الملف: {file_path}")
            
        elif choice == "2":
            file_path = input("أدخل مسار ملف Excel: ").strip()
            if os.path.exists(file_path):
                test_import(file_path)
            else:
                print("❌ الملف غير موجود")
                
        elif choice == "3":
            show_current_data()
            
        elif choice == "4":
            clean_test_data()
            
        elif choice == "5":
            print("🔄 تشغيل اختبار كامل...")
            
            # إنشاء ملف الاختبار
            file_path = create_test_excel()
            
            # عرض البيانات قبل الاستيراد
            print("\n📊 البيانات قبل الاستيراد:")
            show_current_data()
            
            # تنفيذ الاستيراد
            print("\n📥 تنفيذ الاستيراد...")
            result = test_import(file_path)
            
            # عرض البيانات بعد الاستيراد
            print("\n📊 البيانات بعد الاستيراد:")
            show_current_data()
            
            # تنظيف الملف المؤقت
            try:
                os.unlink(file_path)
                print(f"🗑️ تم حذف الملف المؤقت: {file_path}")
            except:
                pass
                
            if result and result.success_count > 0:
                print("\n🎉 الاختبار نجح! الاستيراد يعمل بشكل صحيح")
            else:
                print("\n❌ الاختبار فشل! هناك مشكلة في الاستيراد")
                
        elif choice == "0":
            print("👋 وداعاً!")
            break
            
        else:
            print("❌ اختيار غير صحيح")

if __name__ == "__main__":
    main()
