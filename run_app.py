#!/usr/bin/env python3
"""
ملف تشغيل محسن - نظام إدارة المخازن والمستودعات
Enhanced Launcher - Desktop Stores Management System

الإصدار: 1.2
تاريخ التحديث: 2025-07-02

تشغيل التطبيق:
python run_app.py
"""

import sys
import os
from pathlib import Path
import subprocess
import tkinter as tk
from tkinter import messagebox

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_python_version():
    """التحقق من إصدار Python"""
    if sys.version_info < (3, 8):
        messagebox.showerror(
            "خطأ في الإصدار",
            f"يتطلب هذا التطبيق Python 3.8 أو أحدث\n"
            f"الإصدار الحالي: {sys.version}\n"
            f"يرجى تحديث Python وإعادة المحاولة"
        )
        return False
    return True

def install_missing_packages():
    """تثبيت المكتبات المفقودة"""
    required_packages = [
        'ttkbootstrap',
        'bcrypt', 
        'pillow',
        'reportlab',
        'openpyxl',
        'pandas',
        'psutil',
        'pyperclip'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'pillow':
                __import__('PIL')
            else:
                __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"تثبيت المكتبات المفقودة: {missing_packages}")
        for package in missing_packages:
            try:
                print(f"تثبيت {package}...")
                result = subprocess.run(
                    [sys.executable, "-m", "pip", "install", package, "--force-reinstall"],
                    capture_output=True,
                    text=True
                )
                
                if result.returncode == 0:
                    print(f"✅ تم تثبيت {package} بنجاح")
                else:
                    print(f"❌ فشل في تثبيت {package}: {result.stderr}")
                    return False
                    
            except Exception as e:
                print(f"❌ خطأ في تثبيت {package}: {e}")
                return False
    
    return True

def setup_safe_environment():
    """إعداد بيئة آمنة للتطبيق"""
    try:
        # إنشاء المجلدات المطلوبة
        directories = [
            "data",
            "reports", 
            "backups",
            "logs",
            "assets",
            "assets/icons"
        ]
        
        for directory in directories:
            dir_path = project_root / directory
            dir_path.mkdir(exist_ok=True)
        
        print("✅ تم إعداد بيئة التطبيق بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إعداد البيئة: {e}")
        return False

def fix_import_issues():
    """إصلاح مشاكل الاستيراد"""
    try:
        # التأكد من وجود ملف __init__.py في مجلد ui
        ui_init_path = project_root / "ui" / "__init__.py"
        if not ui_init_path.exists():
            ui_init_path.write_text("# UI Package\n")
        
        # التأكد من وجود ملف __init__.py في مجلد utils
        utils_init_path = project_root / "utils" / "__init__.py"
        if not utils_init_path.exists():
            utils_init_path.write_text("# Utils Package\n")
            
        print("✅ تم إصلاح مشاكل الاستيراد")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح الاستيراد: {e}")
        return False

def run_main_application():
    """تشغيل التطبيق الرئيسي"""
    try:
        print("🚀 بدء تشغيل التطبيق...")
        
        # استيراد التطبيق الرئيسي
        from main import StoresManagementApp
        
        # إنشاء وتشغيل التطبيق
        app = StoresManagementApp()
        app.run()
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        messagebox.showerror("خطأ", f"فشل في تشغيل التطبيق:\n{e}")
        return False
    
    return True

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🏪 نظام إدارة المخازن والمستودعات - المشغل المحسن")
    print("=" * 60)
    
    # التحقق من إصدار Python
    if not check_python_version():
        return
    
    # تثبيت المكتبات المفقودة
    print("📦 التحقق من المكتبات المطلوبة...")
    if not install_missing_packages():
        print("❌ فشل في تثبيت المكتبات المطلوبة")
        return
    
    # إعداد البيئة
    print("🔧 إعداد بيئة التطبيق...")
    if not setup_safe_environment():
        print("❌ فشل في إعداد البيئة")
        return
    
    # إصلاح مشاكل الاستيراد
    print("🔨 إصلاح مشاكل الاستيراد...")
    if not fix_import_issues():
        print("❌ فشل في إصلاح مشاكل الاستيراد")
        return
    
    # تشغيل التطبيق
    print("✅ جميع الفحوصات نجحت - بدء التطبيق...")
    run_main_application()

if __name__ == "__main__":
    main()