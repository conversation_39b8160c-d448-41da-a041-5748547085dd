#!/usr/bin/env python3
"""
إنشاء حزمة كاملة باسم عربي - نظام إدارة المخازن
Create Complete Arabic Package - Desktop Stores Management System
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path
import time

class ArabicPackageBuilder:
    """منشئ الحزمة العربية"""
    
    def __init__(self):
        self.app_name = "نظام_إدارة_المخازن_والمستودعات"
        self.version = "2.0.0"
        self.build_date = time.strftime('%Y-%m-%d %H:%M:%S')
        
    def print_header(self):
        """طباعة رأس البرنامج"""
        print("=" * 70)
        print("🏗️  منشئ الحزمة العربية - نظام إدارة المخازن والمستودعات")
        print("   Arabic Package Builder - Desktop Stores Management System")
        print("=" * 70)
        print(f"📦 اسم التطبيق: {self.app_name}")
        print(f"🔢 الإصدار: {self.version}")
        print(f"📅 تاريخ البناء: {self.build_date}")
        print("=" * 70)
    
    def clean_previous_builds(self):
        """تنظيف البناءات السابقة"""
        print("\n🧹 تنظيف البناءات السابقة...")
        
        # مجلدات البناء المؤقتة
        temp_dirs = ['build', 'dist', '__pycache__']
        
        for temp_dir in temp_dirs:
            if Path(temp_dir).exists():
                try:
                    shutil.rmtree(temp_dir)
                    print(f"   ✅ تم حذف مجلد: {temp_dir}")
                except Exception as e:
                    print(f"   ⚠️ تعذر حذف {temp_dir}: {e}")
        
        # ملفات spec
        spec_files = list(Path('.').glob('*.spec'))
        for spec_file in spec_files:
            try:
                spec_file.unlink()
                print(f"   ✅ تم حذف ملف: {spec_file}")
            except Exception as e:
                print(f"   ⚠️ تعذر حذف {spec_file}: {e}")
    
    def check_requirements(self):
        """فحص المتطلبات"""
        print("\n🔍 فحص المتطلبات...")
        
        # فحص PyInstaller
        try:
            import PyInstaller
            print(f"   ✅ PyInstaller متوفر - الإصدار: {PyInstaller.__version__}")
        except ImportError:
            print("   ❌ PyInstaller غير مثبت!")
            print("   قم بتثبيته باستخدام: pip install pyinstaller")
            return False
        
        # فحص الملف الرئيسي
        if not Path('run_app.py').exists():
            print("   ❌ ملف run_app.py غير موجود!")
            return False
        else:
            print("   ✅ ملف run_app.py موجود")
        
        # فحص الأيقونة
        icon_path = Path('assets/app_icon.ico')
        if not icon_path.exists():
            print(f"   ⚠️ ملف الأيقونة غير موجود: {icon_path}")
            print("   سيتم البناء بدون أيقونة")
        else:
            print(f"   ✅ ملف الأيقونة موجود: {icon_path}")
        
        # فحص المجلدات المطلوبة
        required_dirs = ['ui', 'utils', 'assets']
        for req_dir in required_dirs:
            if Path(req_dir).exists():
                print(f"   ✅ مجلد {req_dir} موجود")
            else:
                print(f"   ⚠️ مجلد {req_dir} غير موجود")
        
        return True
    
    def build_executable(self):
        """بناء الملف التنفيذي"""
        print("\n🔨 بناء الملف التنفيذي...")
        
        # إعداد أوامر PyInstaller
        pyinstaller_args = [
            '--name=' + self.app_name,
            '--onedir',  # مجلد واحد يحتوي على جميع المكتبات
            '--windowed',  # بدون نافذة console
            '--noconfirm',  # عدم طلب تأكيد
            '--clean',  # تنظيف البناء السابق
            '--distpath=dist',  # مجلد الإخراج
            '--workpath=build',  # مجلد العمل المؤقت
        ]
        
        # إضافة الأيقونة إذا كانت موجودة
        icon_path = Path('assets/app_icon.ico')
        if icon_path.exists():
            pyinstaller_args.append(f'--icon={icon_path}')
        
        # إضافة البيانات والملفات المطلوبة
        data_additions = [
            ('assets', 'assets'),
            ('ui', 'ui'),
            ('utils', 'utils'),
            ('config.py', '.'),
            ('models.py', '.'),
            ('database.py', '.'),
            ('permissions_manager.py', '.'),
            ('activity_monitor.py', '.'),
            ('auth_manager.py', '.'),
            ('window_manager.py', '.'),
            ('font_manager.py', '.'),
        ]
        
        # فحص وإضافة الملفات الموجودة فقط
        for source, dest in data_additions:
            if Path(source).exists():
                pyinstaller_args.append(f'--add-data={source};{dest}')
                print(f"   ✅ سيتم تضمين: {source}")
            else:
                print(f"   ⚠️ ملف غير موجود: {source}")
        
        # إضافة مكتبات مخفية مطلوبة
        hidden_imports = [
            'tkinter', 'tkinter.ttk', 'tkinter.messagebox', 'tkinter.filedialog',
            'ttkbootstrap', 'ttkbootstrap.constants', 'ttkbootstrap.style',
            'PIL', 'PIL.Image', 'PIL.ImageTk', 'PIL.ImageDraw',
            'pandas', 'sqlite3', 'bcrypt', 'hashlib',
            'reportlab', 'reportlab.pdfgen', 'reportlab.lib',
            'matplotlib', 'matplotlib.pyplot', 'matplotlib.backends',
            'openpyxl', 'xlsxwriter', 'xlrd',
            'datetime', 'threading', 'queue', 'json', 'csv',
            'os', 'sys', 'pathlib', 'shutil', 'tempfile',
        ]
        
        for module in hidden_imports:
            pyinstaller_args.append(f'--hidden-import={module}')
        
        # إضافة الملف الرئيسي
        pyinstaller_args.append('run_app.py')
        
        print("   🚀 بدء عملية البناء...")
        print("   ⏳ هذا قد يستغرق عدة دقائق...")
        
        try:
            # تشغيل PyInstaller
            result = subprocess.run(['pyinstaller'] + pyinstaller_args, 
                                  capture_output=True, text=True, encoding='utf-8')
            
            if result.returncode == 0:
                print("   ✅ تم بناء الملف التنفيذي بنجاح!")
                return True
            else:
                print("   ❌ فشل في بناء الملف التنفيذي!")
                print(f"   خطأ: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"   ❌ خطأ في عملية البناء: {e}")
            return False
    
    def create_data_directories(self, app_dir):
        """إنشاء مجلدات البيانات"""
        print("\n📁 إنشاء مجلدات البيانات...")
        
        data_dirs = {
            "data": "مجلد البيانات - يحتوي على قاعدة البيانات والملفات المهمة",
            "reports": "مجلد التقارير - يحتوي على التقارير المُصدرة", 
            "backups": "مجلد النسخ الاحتياطية - يحتوي على نسخ احتياطية من البيانات",
            "logs": "مجلد السجلات - يحتوي على ملفات سجل التطبيق",
            "imports": "مجلد الاستيراد - ضع ملفات Excel هنا للاستيراد",
            "exports": "مجلد التصدير - يحتوي على الملفات المُصدرة",
            "temp": "مجلد مؤقت - للملفات المؤقتة أثناء العمل"
        }
        
        for dir_name, description in data_dirs.items():
            dir_path = app_dir / dir_name
            dir_path.mkdir(exist_ok=True)
            
            # إنشاء ملف README في كل مجلد
            readme_file = dir_path / "README.txt"
            readme_file.write_text(description, encoding='utf-8')
            print(f"   ✅ تم إنشاء مجلد: {dir_name}")
    
    def create_batch_files(self, app_dir):
        """إنشاء ملفات التشغيل السريع"""
        print("\n📄 إنشاء ملفات التشغيل...")
        
        # ملف التشغيل العادي
        batch_content = f"""@echo off
chcp 65001 > nul
title {self.app_name} - الإصدار العربي الكامل

cls
echo.
echo ========================================
echo      نظام إدارة المخازن والمستودعات
echo        Desktop Stores Management System
echo ========================================
echo.
echo الإصدار: {self.version} العربي الكامل
echo تاريخ البناء: {self.build_date}
echo.
echo المميزات الجديدة:
echo - اسم عربي كامل للتطبيق
echo - جميع المكتبات في مجلد واحد
echo - سهولة النقل بين الأجهزة
echo - أداء محسن ومستقر
echo - واجهة عربية كاملة
echo.
echo ========================================
echo.

cd /d "%~dp0"

echo 🚀 بدء تشغيل التطبيق...
echo.

if exist "{self.app_name}.exe" (
    start "" "{self.app_name}.exe"
    echo ✅ تم تشغيل التطبيق بنجاح
    echo.
    echo 🔐 بيانات الدخول الافتراضية:
    echo    اسم المستخدم: admin
    echo    كلمة المرور: admin
    echo.
    echo ⚠️  مهم جداً: يرجى تغيير كلمة المرور بعد أول تسجيل دخول
    echo.
    echo 💡 نصائح مهمة:
    echo    - ضع ملفات Excel في مجلد imports للاستيراد
    echo    - التقارير ستحفظ في مجلد reports
    echo    - النسخ الاحتياطية في مجلد backups
    echo    - لا تحذف مجلد _internal أبداً
    echo.
    echo يمكنك إغلاق هذه النافذة الآن
    echo أو انتظر 15 ثانية للإغلاق التلقائي
    timeout /t 15 > nul
) else (
    echo ❌ خطأ: لم يتم العثور على الملف التنفيذي
    echo يرجى التأكد من وجود ملف "{self.app_name}.exe"
    echo.
    echo تحقق من:
    echo - وجود جميع الملفات في المجلد
    echo - عدم حذف أي ملفات من مجلد _internal
    echo - وجود مساحة كافية على القرص
    echo.
    pause
)
"""
        
        batch_file = app_dir / "تشغيل_البرنامج.bat"
        batch_file.write_text(batch_content, encoding='utf-8')
        print("   ✅ تم إنشاء ملف التشغيل العادي")
        
        # ملف التشغيل بصلاحيات المدير
        admin_batch_content = f"""@echo off
chcp 65001 > nul

:: التحقق من صلاحيات المدير
net session >nul 2>&1
if %errorLevel% == 0 (
    echo 🔧 تشغيل بصلاحيات المدير...
) else (
    echo 🔐 طلب صلاحيات المدير...
    echo يرجى الموافقة على طلب الصلاحيات في النافذة التالية
    powershell -Command "Start-Process '%~f0' -Verb RunAs"
    exit /b
)

title {self.app_name} - وضع المدير

cd /d "%~dp0"
call "تشغيل_البرنامج.bat"
"""
        
        admin_batch_file = app_dir / "تشغيل_بصلاحيات_المدير.bat"
        admin_batch_file.write_text(admin_batch_content, encoding='utf-8')
        print("   ✅ تم إنشاء ملف التشغيل بصلاحيات المدير")
        
        # ملف تشغيل سريع (مباشر)
        quick_batch_content = f"""@echo off
chcp 65001 > nul
cd /d "%~dp0"
start "" "{self.app_name}.exe"
"""
        
        quick_batch_file = app_dir / "تشغيل_سريع.bat"
        quick_batch_file.write_text(quick_batch_content, encoding='utf-8')
        print("   ✅ تم إنشاء ملف التشغيل السريع")
    
    def create_documentation(self, app_dir):
        """إنشاء ملفات التوثيق"""
        print("\n📚 إنشاء ملفات التوثيق...")
        
        # ملف معلومات الإصدار الشامل
        version_info = f"""{self.app_name}
Desktop Stores Management System

الإصدار: {self.version} العربي الكامل
تاريخ البناء: {self.build_date}
نوع البناء: Complete Arabic Package with All Libraries

========================================
✅ تم تحقيق جميع المتطلبات بنسبة 100%:
========================================

1️⃣ اسم التطبيق بالعربية الكاملة:
   ✅ اسم الملف التنفيذي: {self.app_name}.exe
   ✅ عنوان النافذة والقوائم بالعربية
   ✅ جميع النصوص والرسائل بالعربية
   ✅ دعم كامل للغة العربية في الواجهة

2️⃣ جميع المكتبات في مجلد واحد:
   ✅ مجلد _internal يحتوي على جميع المكتبات المطلوبة
   ✅ لا يحتاج تثبيت Python أو أي مكتبات خارجية
   ✅ يعمل على أي جهاز Windows مباشرة بدون إعداد
   ✅ حجم محسن وسرعة تشغيل عالية

3️⃣ سهولة النقل والتوزيع المطلقة:
   ✅ مجلد واحد يحتوي على كل شيء
   ✅ نسخ ولصق المجلد ينقل التطبيق كاملاً
   ✅ لا يحتاج تثبيت أو إعداد إضافي
   ✅ يعمل من أي مكان على القرص الصلب
   ✅ يمكن تشغيله من USB أو قرص خارجي

========================================
📁 محتويات الحزمة الكاملة:
========================================

📄 الملفات التنفيذية:
- {self.app_name}.exe (الملف التنفيذي الرئيسي)
- تشغيل_البرنامج.bat (تشغيل مع معلومات مفصلة)
- تشغيل_بصلاحيات_المدير.bat (تشغيل بصلاحيات مدير)
- تشغيل_سريع.bat (تشغيل مباشر بدون رسائل)

📁 مجلدات البيانات (جاهزة للاستخدام):
- data/: قاعدة البيانات والملفات المهمة
- reports/: التقارير المُصدرة (PDF, Excel)
- backups/: النسخ الاحتياطية التلقائية واليدوية
- logs/: ملفات سجل التطبيق والأخطاء
- imports/: ملفات Excel للاستيراد
- exports/: الملفات المُصدرة من التطبيق
- temp/: ملفات مؤقتة أثناء العمل

📁 ملفات النظام (لا تحذف):
- _internal/: جميع المكتبات والمتطلبات
- assets/: الأيقونات والصور
- ui/: ملفات واجهة المستخدم
- utils/: أدوات مساعدة

📚 ملفات التوثيق:
- معلومات_الإصدار.txt (هذا الملف)
- دليل_المستخدم_السريع.txt
- استكشاف_الأخطاء_وحلها.txt
- تعليمات_النقل_والتوزيع.txt

========================================
🚀 تعليمات التشغيل والاستخدام:
========================================

1️⃣ التشغيل الأول (مُوصى به):
   - انقر مرتين على "تشغيل_البرنامج.bat"
   - ستظهر معلومات مفيدة قبل تشغيل التطبيق
   - اتبع التعليمات المعروضة

2️⃣ التشغيل السريع:
   - انقر مرتين على "تشغيل_سريع.bat"
   - أو انقر مباشرة على "{self.app_name}.exe"

3️⃣ في حالة مشاكل الصلاحيات:
   - استخدم "تشغيل_بصلاحيات_المدير.bat"
   - أو انقر بالزر الأيمن على الملف التنفيذي واختر "تشغيل كمدير"

4️⃣ بيانات الدخول الافتراضية:
   - اسم المستخدم: admin
   - كلمة المرور: admin
   - ⚠️ مهم جداً: غير كلمة المرور فوراً بعد أول تسجيل دخول!

========================================
📦 تعليمات النقل إلى جهاز آخر:
========================================

✅ الطريقة الصحيحة:
1. انسخ مجلد "{self.app_name}" بالكامل
2. الصق المجلد في الجهاز الجديد
3. شغل التطبيق مباشرة (لا حاجة لأي إعداد)

❌ تجنب هذه الأخطاء:
- لا تنسخ الملف التنفيذي فقط
- لا تحذف مجلد _internal
- لا تغير أسماء الملفات أو المجلدات
- لا تنقل الملفات منفردة

========================================
💻 متطلبات التشغيل:
========================================

🖥️ نظام التشغيل:
- Windows 10 أو أحدث (مُوصى به بشدة)
- Windows 8.1 (مدعوم)
- Windows Server 2016+ (للشبكات)

⚙️ المواصفات الدنيا:
- المعالج: Intel/AMD 1.5 GHz أو أسرع
- الذاكرة: 4 GB RAM (8 GB مُوصى به للملفات الكبيرة)
- التخزين: 1 GB مساحة فارغة (2 GB مُوصى به)
- الشاشة: 1024x768 (1920x1080 مُوصى به)

🌐 متطلبات إضافية:
- دعم اللغة العربية في النظام
- خطوط عربية مثبتة (متوفرة افتراضياً في Windows)

========================================
🎯 المميزات الرئيسية للتطبيق:
========================================

🏪 إدارة المخزون الشاملة:
- إضافة وتعديل وحذف الأصناف
- تتبع الكميات والحركات بدقة
- تنبيهات المخزون المنخفض
- تقارير حالة المخزون المفصلة
- إدارة الوحدات والتصنيفات

👥 إدارة المستفيدين المتقدمة:
- إضافة الجهات والأشخاص المستفيدين
- تصنيف حسب الإدارات والأقسام
- تتبع تاريخ التعاملات الكامل
- معلومات تفصيلية لكل مستفيد

📊 نظام المعاملات المتطور:
- إنشاء عمليات الصرف والاستلام
- طباعة إيصالات العمليات المفصلة
- تتبع تاريخ جميع العمليات
- ربط العمليات بالمستفيدين والأصناف
- إمكانية التعديل والإلغاء

📈 التقارير والإحصائيات الشاملة:
- تقارير المخزون التفصيلية
- تقارير العمليات والمعاملات
- تقارير المستفيدين والجهات
- إحصائيات شاملة ومرئية
- تصدير بصيغ Excel و PDF عالية الجودة

🔒 الأمان والصلاحيات المتقدمة:
- نظام مستخدمين متعدد المستويات
- صلاحيات مختلفة حسب الدور والمسؤولية
- تسجيل جميع العمليات والتغييرات
- نسخ احتياطي آمن ومشفر
- حماية من الوصول غير المصرح

📥📤 الاستيراد والتصدير المحسن:
- استيراد البيانات من ملفات Excel
- تصدير التقارير بصيغ متعددة
- نسخ احتياطي شامل ومجدول
- استعادة البيانات السريعة
- دعم الملفات الكبيرة

========================================
🔧 استكشاف الأخطاء وحلها:
========================================

❓ التطبيق لا يبدأ:
✅ الحلول:
- جرب "تشغيل_بصلاحيات_المدير.bat"
- تأكد من وجود مساحة كافية على القرص (1 GB+)
- أغلق برامج مكافحة الفيروسات مؤقتاً
- تأكد من عدم حذف أي ملفات من مجلد _internal

❓ بطء في الأداء:
✅ الحلول:
- أغلق البرامج الأخرى غير الضرورية
- تأكد من وجود ذاكرة كافية (4 GB+ متاحة)
- نظف مجلد logs من الملفات القديمة
- أعد تشغيل الكمبيوتر

❓ مشاكل في استيراد Excel:
✅ الحلول:
- ضع الملفات في مجلد imports/
- تأكد من تنسيق البيانات الصحيح
- جرب ملفات أصغر أولاً (أقل من 1000 صف)
- احفظ الملف بصيغة .xlsx

❓ رسائل خطأ أو مشاكل في قاعدة البيانات:
✅ الحلول:
- استخدم النسخة الاحتياطية الأحدث من مجلد backups/
- تحقق من ملفات السجل في مجلد logs/
- في الحالات الصعبة: احذف ملف data/stores_management.db لإنشاء قاعدة جديدة

========================================
💡 نصائح مهمة للاستخدام الأمثل:
========================================

✅ الأمان والحماية:
- غير كلمة مرور admin فوراً بعد التثبيت
- اعمل نسخة احتياطية دورية (أسبوعياً على الأقل)
- لا تحذف مجلد _internal أو أي من محتوياته أبداً
- احتفظ بنسخة من التطبيق في مكان آمن

✅ الأداء والسرعة:
- أغلق البرامج الأخرى عند استيراد ملفات كبيرة
- نظف مجلد logs/ دورياً (شهرياً)
- احتفظ بمساحة فارغة كافية على القرص (2 GB+)
- أعد تشغيل التطبيق بعد العمليات الكبيرة

✅ إدارة البيانات:
- احفظ ملفات Excel في مجلد imports/ قبل الاستيراد
- راجع التقارير في مجلد reports/ دورياً
- تحقق من النسخ الاحتياطية في مجلد backups/
- نظم البيانات بانتظام لتجنب التراكم

========================================
📞 الدعم الفني والمساعدة:
========================================

📧 للحصول على المساعدة:
- راجع ملف "دليل_المستخدم_السريع.txt"
- تحقق من ملفات السجل في مجلد logs/ للتفاصيل
- راجع ملف "استكشاف_الأخطاء_وحلها.txt"
- احتفظ بنسخة احتياطية دائماً قبل أي تغيير

📋 عند طلب المساعدة الفنية:
- اجمع معلومات الخطأ من ملفات السجل
- حدد خطوات إعادة إنتاج المشكلة بالتفصيل
- احتفظ بنسخة من البيانات قبل أي محاولة إصلاح
- اذكر إصدار Windows ومواصفات الجهاز

========================================
🎊 شكر وتقدير:
========================================

شكراً لاستخدامكم نظام إدارة المخازن والمستودعات
نتمنى أن يساعدكم في تنظيم وإدارة مخازنكم بأقصى كفاءة ممكنة

هذا التطبيق تم تطويره خصيصاً ليلبي احتياجات:
- المؤسسات الحكومية والخاصة
- الشركات الصغيرة والمتوسطة
- المخازن والمستودعات
- المكاتب والإدارات

========================================
⚖️ حقوق الطبع والنشر:
========================================

© 2025 Desktop Stores Team
جميع الحقوق محفوظة

هذا البرنامج مجاني للاستخدام الشخصي والتجاري
يُمنع إعادة التوزيع أو التعديل بدون إذن كتابي
للحصول على ترخيص تجاري أو تخصيص، يرجى التواصل معنا

========================================
🔄 تحديثات مستقبلية:
========================================

نعمل باستمرار على تطوير وتحسين التطبيق
التحديثات القادمة ستشمل:
- مميزات جديدة حسب اقتراحاتكم
- تحسينات في الأداء والسرعة
- دعم أفضل للملفات الكبيرة
- واجهة مستخدم محسنة

للحصول على التحديثات:
- تابعونا على وسائل التواصل الاجتماعي
- زوروا الموقع الرسمي دورياً
- اشتركوا في النشرة الإخبارية

========================================
🌟 تقييمكم يهمنا:
========================================

إذا أعجبكم التطبيق، يرجى:
- مشاركة تجربتكم مع الآخرين
- إرسال اقتراحاتكم للتحسين
- تقييم التطبيق ومشاركة رأيكم
- نشر الكلمة بين زملائكم

رأيكم واقتراحاتكم تساعدنا على التطوير والتحسين المستمر

========================================
"""
        
        version_file = app_dir / "معلومات_الإصدار.txt"
        version_file.write_text(version_info, encoding='utf-8')
        print("   ✅ تم إنشاء ملف معلومات الإصدار الشامل")
        
        # دليل المستخدم السريع
        quick_guide = f"""دليل المستخدم السريع
==================

🚀 البدء السريع:
1. انقر مرتين على "تشغيل_البرنامج.bat"
2. اسم المستخدم: admin
3. كلمة المرور: admin
4. غير كلمة المرور فوراً!

📋 الوظائف الرئيسية:
- إدارة الأصناف: قائمة "المخزون"
- إدارة المستفيدين: قائمة "المستفيدون"
- المعاملات: قائمة "المعاملات"
- التقارير: قائمة "التقارير"
- النسخ الاحتياطي: قائمة "أدوات"

📥 استيراد البيانات:
1. ضع ملف Excel في مجلد "imports"
2. من القائمة: ملف > استيراد من Excel
3. اختر الملف واتبع التعليمات
4. انتظر انتهاء العملية (لا تغلق البرنامج)

💾 النسخ الاحتياطي:
- تلقائي: كل يوم في مجلد "backups"
- يدوي: قائمة "أدوات" > "نسخة احتياطية"

⚠️ تحذيرات مهمة:
- لا تحذف مجلد "_internal"
- اعمل نسخة احتياطية قبل أي تحديث
- استخدم "تشغيل_بصلاحيات_المدير.bat" عند الحاجة

للمساعدة الكاملة: راجع "معلومات_الإصدار.txt"
"""
        
        guide_file = app_dir / "دليل_المستخدم_السريع.txt"
        guide_file.write_text(quick_guide, encoding='utf-8')
        print("   ✅ تم إنشاء دليل المستخدم السريع")
        
        # ملف استكشاف الأخطاء
        troubleshooting = """استكشاف الأخطاء وحلها
====================

🔧 المشاكل الشائعة والحلول:

❌ المشكلة: التطبيق لا يبدأ
✅ الحلول:
1. تشغيل بصلاحيات المدير
2. التحقق من مساحة القرص (1 GB على الأقل)
3. إغلاق برامج مكافحة الفيروسات مؤقتاً
4. التأكد من عدم حذف ملفات من مجلد _internal
5. إعادة تشغيل الكمبيوتر

❌ المشكلة: بطء في الأداء
✅ الحلول:
1. إغلاق البرامج الأخرى
2. تنظيف مجلد logs (حذف الملفات القديمة)
3. التأكد من وجود ذاكرة كافية (4 GB+)
4. إعادة تشغيل التطبيق
5. إعادة تشغيل الكمبيوتر

❌ المشكلة: تعليق عند استيراد Excel
✅ الحلول:
1. تقسيم الملف إلى ملفات أصغر (أقل من 1000 صف)
2. التأكد من تنسيق البيانات الصحيح
3. إزالة الخلايا الفارغة
4. حفظ الملف بصيغة .xlsx
5. إغلاق Excel قبل الاستيراد

❌ المشكلة: خطأ في قاعدة البيانات
✅ الحلول:
1. استخدام النسخة الاحتياطية الأحدث
2. نسخ ملف قاعدة البيانات من مجلد backups
3. في الحالات الصعبة: حذف ملف data/stores_management.db

❌ المشكلة: رسائل خطأ غريبة
✅ الحلول:
1. تحقق من ملفات السجل في مجلد logs
2. تشغيل بصلاحيات المدير
3. إعادة تشغيل التطبيق
4. إعادة تشغيل الكمبيوتر

🆘 في حالة الطوارئ:
1. انسخ مجلد data بالكامل (نسخة احتياطية)
2. انسخ مجلد backups بالكامل
3. أعد تحميل التطبيق من جديد
4. استعد البيانات من النسخة الاحتياطية

📞 طلب المساعدة:
عند طلب المساعدة، يرجى تقديم:
- وصف المشكلة بالتفصيل
- رسالة الخطأ (إن وجدت)
- محتوى ملف السجل الأحدث من مجلد logs
- خطوات إعادة إنتاج المشكلة
- إصدار Windows ومواصفات الجهاز
"""
        
        troubleshooting_file = app_dir / "استكشاف_الأخطاء_وحلها.txt"
        troubleshooting_file.write_text(troubleshooting, encoding='utf-8')
        print("   ✅ تم إنشاء دليل استكشاف الأخطاء")
        
        # تعليمات النقل والتوزيع
        transfer_guide = f"""تعليمات النقل والتوزيع
===================

📦 كيفية نقل التطبيق إلى جهاز آخر:

✅ الطريقة الصحيحة:
1. انسخ مجلد "{self.app_name}" بالكامل
2. الصق المجلد في الجهاز الجديد (أي مكان)
3. شغل التطبيق مباشرة (لا حاجة لأي إعداد)

❌ تجنب هذه الأخطاء:
- لا تنسخ الملف التنفيذي فقط
- لا تحذف مجلد _internal
- لا تغير أسماء الملفات أو المجلدات
- لا تنقل الملفات منفردة

🔄 نقل البيانات:
- البيانات محفوظة في مجلد data/
- النسخ الاحتياطية في مجلد backups/
- التقارير في مجلد reports/
- كل شيء ينتقل مع المجلد الرئيسي

💻 متطلبات الجهاز الجديد:
- Windows 8.1 أو أحدث
- 4 GB RAM أو أكثر
- 1 GB مساحة فارغة
- لا يحتاج تثبيت أي برامج إضافية

🌐 التوزيع على عدة أجهزة:
1. انسخ المجلد الرئيسي
2. وزع النسخ على الأجهزة المطلوبة
3. كل جهاز سيعمل بشكل مستقل
4. البيانات منفصلة لكل جهاز

🔒 الأمان عند النقل:
- احتفظ بنسخة احتياطية قبل النقل
- تأكد من نقل جميع الملفات
- اختبر التطبيق في الجهاز الجديد
- لا تحذف النسخة الأصلية حتى تتأكد

📋 قائمة مراجعة النقل:
□ نسخ المجلد الرئيسي بالكامل
□ التأكد من وجود مجلد _internal
□ التأكد من وجود الملف التنفيذي
□ التأكد من وجود مجلدات البيانات
□ اختبار التشغيل في الجهاز الجديد
□ التأكد من عمل جميع الوظائف

✅ علامات النجاح:
- التطبيق يبدأ بدون أخطاء
- جميع القوائم تعمل
- البيانات تظهر بشكل صحيح
- يمكن إنشاء معاملات جديدة
- التقارير تعمل بشكل طبيعي
"""
        
        transfer_file = app_dir / "تعليمات_النقل_والتوزيع.txt"
        transfer_file.write_text(transfer_guide, encoding='utf-8')
        print("   ✅ تم إنشاء تعليمات النقل والتوزيع")
    
    def finalize_package(self):
        """إنهاء الحزمة وعرض النتائج"""
        print("\n🎯 إنهاء الحزمة...")
        
        app_dir = Path("dist") / self.app_name
        
        if not app_dir.exists():
            print("   ❌ مجلد التطبيق غير موجود!")
            return False
        
        # إنشاء مجلدات البيانات
        self.create_data_directories(app_dir)
        
        # إنشاء ملفات التشغيل
        self.create_batch_files(app_dir)
        
        # إنشاء التوثيق
        self.create_documentation(app_dir)
        
        # حساب حجم الحزمة
        total_size = sum(f.stat().st_size for f in app_dir.rglob('*') if f.is_file())
        size_mb = total_size / (1024 * 1024)
        
        # عد الملفات
        file_count = len(list(app_dir.rglob('*')))
        
        return app_dir, size_mb, file_count
    
    def print_success_summary(self, app_dir, size_mb, file_count):
        """طباعة ملخص النجاح"""
        print("\n" + "=" * 70)
        print("🎉 تم إنشاء الحزمة العربية الكاملة بنجاح!")
        print("=" * 70)
        print(f"📁 مجلد التطبيق: {app_dir}")
        print(f"📄 الملف التنفيذي: {self.app_name}.exe")
        print(f"📊 الحجم الإجمالي: {size_mb:.1f} MB")
        print(f"📋 عدد الملفات: {file_count:,}")
        print("=" * 70)
        print("✅ المتطلبات المحققة:")
        print("   🔤 اسم عربي كامل للتطبيق")
        print("   📦 جميع المكتبات في مجلد _internal")
        print("   🚀 جاهز للنقل والتوزيع الفوري")
        print("   💻 يعمل على أي جهاز Windows")
        print("   📚 توثيق شامل ومفصل")
        print("   🔧 ملفات تشغيل متعددة")
        print("   📁 مجلدات بيانات جاهزة")
        print("=" * 70)
        print("🎊 الحزمة جاهزة للاستخدام والتوزيع!")
        print("📍 يمكنك العثور على التطبيق في مجلد dist/")
        print("=" * 70)
    
    def run(self):
        """تشغيل عملية البناء الكاملة"""
        # طباعة الرأس
        self.print_header()
        
        # تنظيف البناءات السابقة
        self.clean_previous_builds()
        
        # فحص المتطلبات
        if not self.check_requirements():
            print("\n❌ فشل في فحص المتطلبات!")
            return False
        
        # بناء الملف التنفيذي
        if not self.build_executable():
            print("\n❌ فشل في بناء الملف التنفيذي!")
            return False
        
        # إنهاء الحزمة
        result = self.finalize_package()
        if not result:
            print("\n❌ فشل في إنهاء الحزمة!")
            return False
        
        app_dir, size_mb, file_count = result
        
        # طباعة ملخص النجاح
        self.print_success_summary(app_dir, size_mb, file_count)
        
        return True

def main():
    """الدالة الرئيسية"""
    builder = ArabicPackageBuilder()
    success = builder.run()
    
    if success:
        print("\n🎉 تمت العملية بنجاح!")
        return 0
    else:
        print("\n💥 فشلت العملية!")
        return 1

if __name__ == "__main__":
    sys.exit(main())#!/usr/bin/env python3
"""
إنشاء حزمة كاملة باسم عربي - نظام إدارة المخازن
Create Complete Arabic Package - Desktop Stores Management System
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path
import time

class ArabicPackageBuilder:
    """منشئ الحزمة العربية"""
    
    def __init__(self):
        self.app_name = "نظام_إدارة_المخازن_والمستودعات"
        self.version = "2.0.0"
        self.build_date = time.strftime('%Y-%m-%d %H:%M:%S')
        
    def print_header(self):
        """طباعة رأس البرنامج"""
        print("=" * 70)
        print("🏗️  منشئ الحزمة العربية - نظام إدارة المخازن والمستودعات")
        print("   Arabic Package Builder - Desktop Stores Management System")
        print("=" * 70)
        print(f"📦 اسم التطبيق: {self.app_name}")
        print(f"🔢 الإصدار: {self.version}")
        print(f"📅 تاريخ البناء: {self.build_date}")
        print("=" * 70)
    
    def clean_previous_builds(self):
        """تنظيف البناءات السابقة"""
        print("\n🧹 تنظيف البناءات السابقة...")
        
        # مجلدات البناء المؤقتة
        temp_dirs = ['build', 'dist', '__pycache__']
        
        for temp_dir in temp_dirs:
            if Path(temp_dir).exists():
                try:
                    shutil.rmtree(temp_dir)
                    print(f"   ✅ تم حذف مجلد: {temp_dir}")
                except Exception as e:
                    print(f"   ⚠️ تعذر حذف {temp_dir}: {e}")
        
        # ملفات spec
        spec_files = list(Path('.').glob('*.spec'))
        for spec_file in spec_files:
            try:
                spec_file.unlink()
                print(f"   ✅ تم حذف ملف: {spec_file}")
            except Exception as e:
                print(f"   ⚠️ تعذر حذف {spec_file}: {e}")
    
    def check_requirements(self):
        """فحص المتطلبات"""
        print("\n🔍 فحص المتطلبات...")
        
        # فحص PyInstaller
        try:
            import PyInstaller
            print(f"   ✅ PyInstaller متوفر - الإصدار: {PyInstaller.__version__}")
        except ImportError:
            print("   ❌ PyInstaller غير مثبت!")
            print("   قم بتثبيته باستخدام: pip install pyinstaller")
            return False
        
        # فحص الملف الرئيسي
        if not Path('run_app.py').exists():
            print("   ❌ ملف run_app.py غير موجود!")
            return False
        else:
            print("   ✅ ملف run_app.py موجود")
        
        # فحص الأيقونة
        icon_path = Path('assets/app_icon.ico')
        if not icon_path.exists():
            print(f"   ⚠️ ملف الأيقونة غير موجود: {icon_path}")
            print("   سيتم البناء بدون أيقونة")
        else:
            print(f"   ✅ ملف الأيقونة موجود: {icon_path}")
        
        # فحص المجلدات المطلوبة
        required_dirs = ['ui', 'utils', 'assets']
        for req_dir in required_dirs:
            if Path(req_dir).exists():
                print(f"   ✅ مجلد {req_dir} موجود")
            else:
                print(f"   ⚠️ مجلد {req_dir} غير موجود")
        
        return True
    
    def build_executable(self):
        """بناء الملف التنفيذي"""
        print("\n🔨 بناء الملف التنفيذي...")
        
        # إعداد أوامر PyInstaller
        pyinstaller_args = [
            '--name=' + self.app_name,
            '--onedir',  # مجلد واحد يحتوي على جميع المكتبات
            '--windowed',  # بدون نافذة console
            '--noconfirm',  # عدم طلب تأكيد
            '--clean',  # تنظيف البناء السابق
            '--distpath=dist',  # مجلد الإخراج
            '--workpath=build',  # مجلد العمل المؤقت
        ]
        
        # إضافة الأيقونة إذا كانت موجودة
        icon_path = Path('assets/app_icon.ico')
        if icon_path.exists():
            pyinstaller_args.append(f'--icon={icon_path}')
        
        # إضافة البيانات والملفات المطلوبة
        data_additions = [
            ('assets', 'assets'),
            ('ui', 'ui'),
            ('utils', 'utils'),
            ('config.py', '.'),
            ('models.py', '.'),
            ('database.py', '.'),
            ('permissions_manager.py', '.'),
            ('activity_monitor.py', '.'),
            ('auth_manager.py', '.'),
            ('window_manager.py', '.'),
            ('font_manager.py', '.'),
        ]
        
        # فحص وإضافة الملفات الموجودة فقط
        for source, dest in data_additions:
            if Path(source).exists():
                pyinstaller_args.append(f'--add-data={source};{dest}')
                print(f"   ✅ سيتم تضمين: {source}")
            else:
                print(f"   ⚠️ ملف غير موجود: {source}")
        
        # إضافة مكتبات مخفية مطلوبة
        hidden_imports = [
            'tkinter', 'tkinter.ttk', 'tkinter.messagebox', 'tkinter.filedialog',
            'ttkbootstrap', 'ttkbootstrap.constants', 'ttkbootstrap.style',
            'PIL', 'PIL.Image', 'PIL.ImageTk', 'PIL.ImageDraw',
            'pandas', 'sqlite3', 'bcrypt', 'hashlib',
            'reportlab', 'reportlab.pdfgen', 'reportlab.lib',
            'matplotlib', 'matplotlib.pyplot', 'matplotlib.backends',
            'openpyxl', 'xlsxwriter', 'xlrd',
            'datetime', 'threading', 'queue', 'json', 'csv',
            'os', 'sys', 'pathlib', 'shutil', 'tempfile',
        ]
        
        for module in hidden_imports:
            pyinstaller_args.append(f'--hidden-import={module}')
        
        # إضافة الملف الرئيسي
        pyinstaller_args.append('run_app.py')
        
        print("   🚀 بدء عملية البناء...")
        print("   ⏳ هذا قد يستغرق عدة دقائق...")
        
        try:
            # تشغيل PyInstaller
            result = subprocess.run(['pyinstaller'] + pyinstaller_args, 
                                  capture_output=True, text=True, encoding='utf-8')
            
            if result.returncode == 0:
                print("   ✅ تم بناء الملف التنفيذي بنجاح!")
                return True
            else:
                print("   ❌ فشل في بناء الملف التنفيذي!")
                print(f"   خطأ: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"   ❌ خطأ في عملية البناء: {e}")
            return False
    
    def create_data_directories(self, app_dir):
        """إنشاء مجلدات البيانات"""
        print("\n📁 إنشاء مجلدات البيانات...")
        
        data_dirs = {
            "data": "مجلد البيانات - يحتوي على قاعدة البيانات والملفات المهمة",
            "reports": "مجلد التقارير - يحتوي على التقارير المُصدرة", 
            "backups": "مجلد النسخ الاحتياطية - يحتوي على نسخ احتياطية من البيانات",
            "logs": "مجلد السجلات - يحتوي على ملفات سجل التطبيق",
            "imports": "مجلد الاستيراد - ضع ملفات Excel هنا للاستيراد",
            "exports": "مجلد التصدير - يحتوي على الملفات المُصدرة",
            "temp": "مجلد مؤقت - للملفات المؤقتة أثناء العمل"
        }
        
        for dir_name, description in data_dirs.items():
            dir_path = app_dir / dir_name
            dir_path.mkdir(exist_ok=True)
            
            # إنشاء ملف README في كل مجلد
            readme_file = dir_path / "README.txt"
            readme_file.write_text(description, encoding='utf-8')
            print(f"   ✅ تم إنشاء مجلد: {dir_name}")
    
    def create_batch_files(self, app_dir):
        """إنشاء ملفات التشغيل السريع"""
        print("\n📄 إنشاء ملفات التشغيل...")
        
        # ملف التشغيل العادي
        batch_content = f"""@echo off
chcp 65001 > nul
title {self.app_name} - الإصدار العربي الكامل

cls
echo.
echo ========================================
echo      نظام إدارة المخازن والمستودعات
echo        Desktop Stores Management System
echo ========================================
echo.
echo الإصدار: {self.version} العربي الكامل
echo تاريخ البناء: {self.build_date}
echo.
echo المميزات الجديدة:
echo - اسم عربي كامل للتطبيق
echo - جميع المكتبات في مجلد واحد
echo - سهولة النقل بين الأجهزة
echo - أداء محسن ومستقر
echo - واجهة عربية كاملة
echo.
echo ========================================
echo.

cd /d "%~dp0"

echo 🚀 بدء تشغيل التطبيق...
echo.

if exist "{self.app_name}.exe" (
    start "" "{self.app_name}.exe"
    echo ✅ تم تشغيل التطبيق بنجاح
    echo.
    echo 🔐 بيانات الدخول الافتراضية:
    echo    اسم المستخدم: admin
    echo    كلمة المرور: admin
    echo.
    echo ⚠️  مهم جداً: يرجى تغيير كلمة المرور بعد أول تسجيل دخول
    echo.
    echo 💡 نصائح مهمة:
    echo    - ضع ملفات Excel في مجلد imports للاستيراد
    echo    - التقارير ستحفظ في مجلد reports
    echo    - النسخ الاحتياطية في مجلد backups
    echo    - لا تحذف مجلد _internal أبداً
    echo.
    echo يمكنك إغلاق هذه النافذة الآن
    echo أو انتظر 15 ثانية للإغلاق التلقائي
    timeout /t 15 > nul
) else (
    echo ❌ خطأ: لم يتم العثور على الملف التنفيذي
    echo يرجى التأكد من وجود ملف "{self.app_name}.exe"
    echo.
    echo تحقق من:
    echo - وجود جميع الملفات في المجلد
    echo - عدم حذف أي ملفات من مجلد _internal
    echo - وجود مساحة كافية على القرص
    echo.
    pause
)
"""
        
        batch_file = app_dir / "تشغيل_البرنامج.bat"
        batch_file.write_text(batch_content, encoding='utf-8')
        print("   ✅ تم إنشاء ملف التشغيل العادي")
        
        # ملف التشغيل بصلاحيات المدير
        admin_batch_content = f"""@echo off
chcp 65001 > nul

:: التحقق من صلاحيات المدير
net session >nul 2>&1
if %errorLevel% == 0 (
    echo 🔧 تشغيل بصلاحيات المدير...
) else (
    echo 🔐 طلب صلاحيات المدير...
    echo يرجى الموافقة على طلب الصلاحيات في النافذة التالية
    powershell -Command "Start-Process '%~f0' -Verb RunAs"
    exit /b
)

title {self.app_name} - وضع المدير

cd /d "%~dp0"
call "تشغيل_البرنامج.bat"
"""
        
        admin_batch_file = app_dir / "تشغيل_بصلاحيات_المدير.bat"
        admin_batch_file.write_text(admin_batch_content, encoding='utf-8')
        print("   ✅ تم إنشاء ملف التشغيل بصلاحيات المدير")
        
        # ملف تشغيل سريع (مباشر)
        quick_batch_content = f"""@echo off
chcp 65001 > nul
cd /d "%~dp0"
start "" "{self.app_name}.exe"
"""
        
        quick_batch_file = app_dir / "تشغيل_سريع.bat"
        quick_batch_file.write_text(quick_batch_content, encoding='utf-8')
        print("   ✅ تم إنشاء ملف التشغيل السريع")
    
    def create_documentation(self, app_dir):
        """إنشاء ملفات التوثيق"""
        print("\n📚 إنشاء ملفات التوثيق...")
        
        # ملف معلومات الإصدار الشامل
        version_info = f"""{self.app_name}
Desktop Stores Management System

الإصدار: {self.version} العربي الكامل
تاريخ البناء: {self.build_date}
نوع البناء: Complete Arabic Package with All Libraries

========================================
✅ تم تحقيق جميع المتطلبات بنسبة 100%:
========================================

1️⃣ اسم التطبيق بالعربية الكاملة:
   ✅ اسم الملف التنفيذي: {self.app_name}.exe
   ✅ عنوان النافذة والقوائم بالعربية
   ✅ جميع النصوص والرسائل بالعربية
   ✅ دعم كامل للغة العربية في الواجهة

2️⃣ جميع المكتبات في مجلد واحد:
   ✅ مجلد _internal يحتوي على جميع المكتبات المطلوبة
   ✅ لا يحتاج تثبيت Python أو أي مكتبات خارجية
   ✅ يعمل على أي جهاز Windows مباشرة بدون إعداد
   ✅ حجم محسن وسرعة تشغيل عالية

3️⃣ سهولة النقل والتوزيع المطلقة:
   ✅ مجلد واحد يحتوي على كل شيء
   ✅ نسخ ولصق المجلد ينقل التطبيق كاملاً
   ✅ لا يحتاج تثبيت أو إعداد إضافي
   ✅ يعمل من أي مكان على القرص الصلب
   ✅ يمكن تشغيله من USB أو قرص خارجي

========================================
📁 محتويات الحزمة الكاملة:
========================================

📄 الملفات التنفيذية:
- {self.app_name}.exe (الملف التنفيذي الرئيسي)
- تشغيل_البرنامج.bat (تشغيل مع معلومات مفصلة)
- تشغيل_بصلاحيات_المدير.bat (تشغيل بصلاحيات مدير)
- تشغيل_سريع.bat (تشغيل مباشر بدون رسائل)

📁 مجلدات البيانات (جاهزة للاستخدام):
- data/: قاعدة البيانات والملفات المهمة
- reports/: التقارير المُصدرة (PDF, Excel)
- backups/: النسخ الاحتياطية التلقائية واليدوية
- logs/: ملفات سجل التطبيق والأخطاء
- imports/: ملفات Excel للاستيراد
- exports/: الملفات المُصدرة من التطبيق
- temp/: ملفات مؤقتة أثناء العمل

📁 ملفات النظام (لا تحذف):
- _internal/: جميع المكتبات والمتطلبات
- assets/: الأيقونات والصور
- ui/: ملفات واجهة المستخدم
- utils/: أدوات مساعدة

📚 ملفات التوثيق:
- معلومات_الإصدار.txt (هذا الملف)
- دليل_المستخدم_السريع.txt
- استكشاف_الأخطاء_وحلها.txt
- تعليمات_النقل_والتوزيع.txt

========================================
🚀 تعليمات التشغيل والاستخدام:
========================================

1️⃣ التشغيل الأول (مُوصى به):
   - انقر مرتين على "تشغيل_البرنامج.bat"
   - ستظهر معلومات مفيدة قبل تشغيل التطبيق
   - اتبع التعليمات المعروضة

2️⃣ التشغيل السريع:
   - انقر مرتين على "تشغيل_سريع.bat"
   - أو انقر مباشرة على "{self.app_name}.exe"

3️⃣ في حالة مشاكل الصلاحيات:
   - استخدم "تشغيل_بصلاحيات_المدير.bat"
   - أو انقر بالزر الأيمن على الملف التنفيذي واختر "تشغيل كمدير"

4️⃣ بيانات الدخول الافتراضية:
   - اسم المستخدم: admin
   - كلمة المرور: admin
   - ⚠️ مهم جداً: غير كلمة المرور فوراً بعد أول تسجيل دخول!

========================================
📦 تعليمات النقل إلى جهاز آخر:
========================================

✅ الطريقة الصحيحة:
1. انسخ مجلد "{self.app_name}" بالكامل
2. الصق المجلد في الجهاز الجديد
3. شغل التطبيق مباشرة (لا حاجة لأي إعداد)

❌ تجنب هذه الأخطاء:
- لا تنسخ الملف التنفيذي فقط
- لا تحذف مجلد _internal
- لا تغير أسماء الملفات أو المجلدات
- لا تنقل الملفات منفردة

========================================
💻 متطلبات التشغيل:
========================================

🖥️ نظام التشغيل:
- Windows 10 أو أحدث (مُوصى به بشدة)
- Windows 8.1 (مدعوم)
- Windows Server 2016+ (للشبكات)

⚙️ المواصفات الدنيا:
- المعالج: Intel/AMD 1.5 GHz أو أسرع
- الذاكرة: 4 GB RAM (8 GB مُوصى به للملفات الكبيرة)
- التخزين: 1 GB مساحة فارغة (2 GB مُوصى به)
- الشاشة: 1024x768 (1920x1080 مُوصى به)

🌐 متطلبات إضافية:
- دعم اللغة العربية في النظام
- خطوط عربية مثبتة (متوفرة افتراضياً في Windows)

========================================
🎯 المميزات الرئيسية للتطبيق:
========================================

🏪 إدارة المخزون الشاملة:
- إضافة وتعديل وحذف الأصناف
- تتبع الكميات والحركات بدقة
- تنبيهات المخزون المنخفض
- تقارير حالة المخزون المفصلة
- إدارة الوحدات والتصنيفات

👥 إدارة المستفيدين المتقدمة:
- إضافة الجهات والأشخاص المستفيدين
- تصنيف حسب الإدارات والأقسام
- تتبع تاريخ التعاملات الكامل
- معلومات تفصيلية لكل مستفيد

📊 نظام المعاملات المتطور:
- إنشاء عمليات الصرف والاستلام
- طباعة إيصالات العمليات المفصلة
- تتبع تاريخ جميع العمليات
- ربط العمليات بالمستفيدين والأصناف
- إمكانية التعديل والإلغاء

📈 التقارير والإحصائيات الشاملة:
- تقارير المخزون التفصيلية
- تقارير العمليات والمعاملات
- تقارير المستفيدين والجهات
- إحصائيات شاملة ومرئية
- تصدير بصيغ Excel و PDF عالية الجودة

🔒 الأمان والصلاحيات المتقدمة:
- نظام مستخدمين متعدد المستويات
- صلاحيات مختلفة حسب الدور والمسؤولية
- تسجيل جميع العمليات والتغييرات
- نسخ احتياطي آمن ومشفر
- حماية من الوصول غير المصرح

📥📤 الاستيراد والتصدير المحسن:
- استيراد البيانات من ملفات Excel
- تصدير التقارير بصيغ متعددة
- نسخ احتياطي شامل ومجدول
- استعادة البيانات السريعة
- دعم الملفات الكبيرة

========================================
🔧 استكشاف الأخطاء وحلها:
========================================

❓ التطبيق لا يبدأ:
✅ الحلول:
- جرب "تشغيل_بصلاحيات_المدير.bat"
- تأكد من وجود مساحة كافية على القرص (1 GB+)
- أغلق برامج مكافحة الفيروسات مؤقتاً
- تأكد من عدم حذف أي ملفات من مجلد _internal

❓ بطء في الأداء:
✅ الحلول:
- أغلق البرامج الأخرى غير الضرورية
- تأكد من وجود ذاكرة كافية (4 GB+ متاحة)
- نظف مجلد logs من الملفات القديمة
- أعد تشغيل الكمبيوتر

❓ مشاكل في استيراد Excel:
✅ الحلول:
- ضع الملفات في مجلد imports/
- تأكد من تنسيق البيانات الصحيح
- جرب ملفات أصغر أولاً (أقل من 1000 صف)
- احفظ الملف بصيغة .xlsx

❓ رسائل خطأ أو مشاكل في قاعدة البيانات:
✅ الحلول:
- استخدم النسخة الاحتياطية الأحدث من مجلد backups/
- تحقق من ملفات السجل في مجلد logs/
- في الحالات الصعبة: احذف ملف data/stores_management.db لإنشاء قاعدة جديدة

========================================
💡 نصائح مهمة للاستخدام الأمثل:
========================================

✅ الأمان والحماية:
- غير كلمة مرور admin فوراً بعد التثبيت
- اعمل نسخة احتياطية دورية (أسبوعياً على الأقل)
- لا تحذف مجلد _internal أو أي من محتوياته أبداً
- احتفظ بنسخة من التطبيق في مكان آمن

✅ الأداء والسرعة:
- أغلق البرامج الأخرى عند استيراد ملفات كبيرة
- نظف مجلد logs/ دورياً (شهرياً)
- احتفظ بمساحة فارغة كافية على القرص (2 GB+)
- أعد تشغيل التطبيق بعد العمليات الكبيرة

✅ إدارة البيانات:
- احفظ ملفات Excel في مجلد imports/ قبل الاستيراد
- راجع التقارير في مجلد reports/ دورياً
- تحقق من النسخ الاحتياطية في مجلد backups/
- نظم البيانات بانتظام لتجنب التراكم

========================================
📞 الدعم الفني والمساعدة:
========================================

📧 للحصول على المساعدة:
- راجع ملف "دليل_المستخدم_السريع.txt"
- تحقق من ملفات السجل في مجلد logs/ للتفاصيل
- راجع ملف "استكشاف_الأخطاء_وحلها.txt"
- احتفظ بنسخة احتياطية دائماً قبل أي تغيير

📋 عند طلب المساعدة الفنية:
- اجمع معلومات الخطأ من ملفات السجل
- حدد خطوات إعادة إنتاج المشكلة بالتفصيل
- احتفظ بنسخة من البيانات قبل أي محاولة إصلاح
- اذكر إصدار Windows ومواصفات الجهاز

========================================
🎊 شكر وتقدير:
========================================

شكراً لاستخدامكم نظام إدارة المخازن والمستودعات
نتمنى أن يساعدكم في تنظيم وإدارة مخازنكم بأقصى كفاءة ممكنة

هذا التطبيق تم تطويره خصيصاً ليلبي احتياجات:
- المؤسسات الحكومية والخاصة
- الشركات الصغيرة والمتوسطة
- المخازن والمستودعات
- المكاتب والإدارات

========================================
⚖️ حقوق الطبع والنشر:
========================================

© 2025 Desktop Stores Team
جميع الحقوق محفوظة

هذا البرنامج مجاني للاستخدام الشخصي والتجاري
يُمنع إعادة التوزيع أو التعديل بدون إذن كتابي
للحصول على ترخيص تجاري أو تخصيص، يرجى التواصل معنا

========================================
🔄 تحديثات مستقبلية:
========================================

نعمل باستمرار على تطوير وتحسين التطبيق
التحديثات القادمة ستشمل:
- مميزات جديدة حسب اقتراحاتكم
- تحسينات في الأداء والسرعة
- دعم أفضل للملفات الكبيرة
- واجهة مستخدم محسنة

للحصول على التحديثات:
- تابعونا على وسائل التواصل الاجتماعي
- زوروا الموقع الرسمي دورياً
- اشتركوا في النشرة الإخبارية

========================================
🌟 تقييمكم يهمنا:
========================================

إذا أعجبكم التطبيق، يرجى:
- مشاركة تجربتكم مع الآخرين
- إرسال اقتراحاتكم للتحسين
- تقييم التطبيق ومشاركة رأيكم
- نشر الكلمة بين زملائكم

رأيكم واقتراحاتكم تساعدنا على التطوير والتحسين المستمر

========================================
"""
        
        version_file = app_dir / "معلومات_الإصدار.txt"
        version_file.write_text(version_info, encoding='utf-8')
        print("   ✅ تم إنشاء ملف معلومات الإصدار الشامل")
        
        # دليل المستخدم السريع
        quick_guide = f"""دليل المستخدم السريع
==================

🚀 البدء السريع:
1. انقر مرتين على "تشغيل_البرنامج.bat"
2. اسم المستخدم: admin
3. كلمة المرور: admin
4. غير كلمة المرور فوراً!

📋 الوظائف الرئيسية:
- إدارة الأصناف: قائمة "المخزون"
- إدارة المستفيدين: قائمة "المستفيدون"
- المعاملات: قائمة "المعاملات"
- التقارير: قائمة "التقارير"
- النسخ الاحتياطي: قائمة "أدوات"

📥 استيراد البيانات:
1. ضع ملف Excel في مجلد "imports"
2. من القائمة: ملف > استيراد من Excel
3. اختر الملف واتبع التعليمات
4. انتظر انتهاء العملية (لا تغلق البرنامج)

💾 النسخ الاحتياطي:
- تلقائي: كل يوم في مجلد "backups"
- يدوي: قائمة "أدوات" > "نسخة احتياطية"

⚠️ تحذيرات مهمة:
- لا تحذف مجلد "_internal"
- اعمل نسخة احتياطية قبل أي تحديث
- استخدم "تشغيل_بصلاحيات_المدير.bat" عند الحاجة

للمساعدة الكاملة: راجع "معلومات_الإصدار.txt"
"""
        
        guide_file = app_dir / "دليل_المستخدم_السريع.txt"
        guide_file.write_text(quick_guide, encoding='utf-8')
        print("   ✅ تم إنشاء دليل المستخدم السريع")
        
        # ملف استكشاف الأخطاء
        troubleshooting = """استكشاف الأخطاء وحلها
====================

🔧 المشاكل الشائعة والحلول:

❌ المشكلة: التطبيق لا يبدأ
✅ الحلول:
1. تشغيل بصلاحيات المدير
2. التحقق من مساحة القرص (1 GB على الأقل)
3. إغلاق برامج مكافحة الفيروسات مؤقتاً
4. التأكد من عدم حذف ملفات من مجلد _internal
5. إعادة تشغيل الكمبيوتر

❌ المشكلة: بطء في الأداء
✅ الحلول:
1. إغلاق البرامج الأخرى
2. تنظيف مجلد logs (حذف الملفات القديمة)
3. التأكد من وجود ذاكرة كافية (4 GB+)
4. إعادة تشغيل التطبيق
5. إعادة تشغيل الكمبيوتر

❌ المشكلة: تعليق عند استيراد Excel
✅ الحلول:
1. تقسيم الملف إلى ملفات أصغر (أقل من 1000 صف)
2. التأكد من تنسيق البيانات الصحيح
3. إزالة الخلايا الفارغة
4. حفظ الملف بصيغة .xlsx
5. إغلاق Excel قبل الاستيراد

❌ المشكلة: خطأ في قاعدة البيانات
✅ الحلول:
1. استخدام النسخة الاحتياطية الأحدث
2. نسخ ملف قاعدة البيانات من مجلد backups
3. في الحالات الصعبة: حذف ملف data/stores_management.db

❌ المشكلة: رسائل خطأ غريبة
✅ الحلول:
1. تحقق من ملفات السجل في مجلد logs
2. تشغيل بصلاحيات المدير
3. إعادة تشغيل التطبيق
4. إعادة تشغيل الكمبيوتر

🆘 في حالة الطوارئ:
1. انسخ مجلد data بالكامل (نسخة احتياطية)
2. انسخ مجلد backups بالكامل
3. أعد تحميل التطبيق من جديد
4. استعد البيانات من النسخة الاحتياطية

📞 طلب المساعدة:
عند طلب المساعدة، يرجى تقديم:
- وصف المشكلة بالتفصيل
- رسالة الخطأ (إن وجدت)
- محتوى ملف السجل الأحدث من مجلد logs
- خطوات إعادة إنتاج المشكلة
- إصدار Windows ومواصفات الجهاز
"""
        
        troubleshooting_file = app_dir / "استكشاف_الأخطاء_وحلها.txt"
        troubleshooting_file.write_text(troubleshooting, encoding='utf-8')
        print("   ✅ تم إنشاء دليل استكشاف الأخطاء")
        
        # تعليمات النقل والتوزيع
        transfer_guide = f"""تعليمات النقل والتوزيع
===================

📦 كيفية نقل التطبيق إلى جهاز آخر:

✅ الطريقة الصحيحة:
1. انسخ مجلد "{self.app_name}" بالكامل
2. الصق المجلد في الجهاز الجديد (أي مكان)
3. شغل التطبيق مباشرة (لا حاجة لأي إعداد)

❌ تجنب هذه الأخطاء:
- لا تنسخ الملف التنفيذي فقط
- لا تحذف مجلد _internal
- لا تغير أسماء الملفات أو المجلدات
- لا تنقل الملفات منفردة

🔄 نقل البيانات:
- البيانات محفوظة في مجلد data/
- النسخ الاحتياطية في مجلد backups/
- التقارير في مجلد reports/
- كل شيء ينتقل مع المجلد الرئيسي

💻 متطلبات الجهاز الجديد:
- Windows 8.1 أو أحدث
- 4 GB RAM أو أكثر
- 1 GB مساحة فارغة
- لا يحتاج تثبيت أي برامج إضافية

🌐 التوزيع على عدة أجهزة:
1. انسخ المجلد الرئيسي
2. وزع النسخ على الأجهزة المطلوبة
3. كل جهاز سيعمل بشكل مستقل
4. البيانات منفصلة لكل جهاز

🔒 الأمان عند النقل:
- احتفظ بنسخة احتياطية قبل النقل
- تأكد من نقل جميع الملفات
- اختبر التطبيق في الجهاز الجديد
- لا تحذف النسخة الأصلية حتى تتأكد

📋 قائمة مراجعة النقل:
□ نسخ المجلد الرئيسي بالكامل
□ التأكد من وجود مجلد _internal
□ التأكد من وجود الملف التنفيذي
□ التأكد من وجود مجلدات البيانات
□ اختبار التشغيل في الجهاز الجديد
□ التأكد من عمل جميع الوظائف

✅ علامات النجاح:
- التطبيق يبدأ بدون أخطاء
- جميع القوائم تعمل
- البيانات تظهر بشكل صحيح
- يمكن إنشاء معاملات جديدة
- التقارير تعمل بشكل طبيعي
"""
        
        transfer_file = app_dir / "تعليمات_النقل_والتوزيع.txt"
        transfer_file.write_text(transfer_guide, encoding='utf-8')
        print("   ✅ تم إنشاء تعليمات النقل والتوزيع")
    
    def finalize_package(self):
        """إنهاء الحزمة وعرض النتائج"""
        print("\n🎯 إنهاء الحزمة...")
        
        app_dir = Path("dist") / self.app_name
        
        if not app_dir.exists():
            print("   ❌ مجلد التطبيق غير موجود!")
            return False
        
        # إنشاء مجلدات البيانات
        self.create_data_directories(app_dir)
        
        # إنشاء ملفات التشغيل
        self.create_batch_files(app_dir)
        
        # إنشاء التوثيق
        self.create_documentation(app_dir)
        
        # حساب حجم الحزمة
        total_size = sum(f.stat().st_size for f in app_dir.rglob('*') if f.is_file())
        size_mb = total_size / (1024 * 1024)
        
        # عد الملفات
        file_count = len(list(app_dir.rglob('*')))
        
        return app_dir, size_mb, file_count
    
    def print_success_summary(self, app_dir, size_mb, file_count):
        """طباعة ملخص النجاح"""
        print("\n" + "=" * 70)
        print("🎉 تم إنشاء الحزمة العربية الكاملة بنجاح!")
        print("=" * 70)
        print(f"📁 مجلد التطبيق: {app_dir}")
        print(f"📄 الملف التنفيذي: {self.app_name}.exe")
        print(f"📊 الحجم الإجمالي: {size_mb:.1f} MB")
        print(f"📋 عدد الملفات: {file_count:,}")
        print("=" * 70)
        print("✅ المتطلبات المحققة:")
        print("   🔤 اسم عربي كامل للتطبيق")
        print("   📦 جميع المكتبات في مجلد _internal")
        print("   🚀 جاهز للنقل والتوزيع الفوري")
        print("   💻 يعمل على أي جهاز Windows")
        print("   📚 توثيق شامل ومفصل")
        print("   🔧 ملفات تشغيل متعددة")
        print("   📁 مجلدات بيانات جاهزة")
        print("=" * 70)
        print("🎊 الحزمة جاهزة للاستخدام والتوزيع!")
        print("📍 يمكنك العثور على التطبيق في مجلد dist/")
        print("=" * 70)
    
    def run(self):
        """تشغيل عملية البناء الكاملة"""
        # طباعة الرأس
        self.print_header()
        
        # تنظيف البناءات السابقة
        self.clean_previous_builds()
        
        # فحص المتطلبات
        if not self.check_requirements():
            print("\n❌ فشل في فحص المتطلبات!")
            return False
        
        # بناء الملف التنفيذي
        if not self.build_executable():
            print("\n❌ فشل في بناء الملف التنفيذي!")
            return False
        
        # إنهاء الحزمة
        result = self.finalize_package()
        if not result:
            print("\n❌ فشل في إنهاء الحزمة!")
            return False
        
        app_dir, size_mb, file_count = result
        
        # طباعة ملخص النجاح
        self.print_success_summary(app_dir, size_mb, file_count)
        
        return True

def main():
    """الدالة الرئيسية"""
    builder = ArabicPackageBuilder()
    success = builder.run()
    
    if success:
        print("\n🎉 تمت العملية بنجاح!")
        return 0
    else:
        print("\n💥 فشلت العملية!")
        return 1

if __name__ == "__main__":
    sys.exit(main())