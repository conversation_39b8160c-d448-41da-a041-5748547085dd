#!/usr/bin/env python3
"""
بناء ملف تنفيذي باسم عربي - نظام إدارة المخازن
Build Arabic Named Executable - Desktop Stores Management System
"""

import PyInstaller.__main__
import os
from pathlib import Path

def build_arabic_exe():
    """بناء الملف التنفيذي باسم عربي"""
    print("🔨 بناء نظام إدارة المخازن...")
    print("=" * 50)
    
    # اسم التطبيق بالعربية
    app_name = "نظام_إدارة_المخازن_والمستودعات"
    
    # إعداد أوامر PyInstaller
    pyinstaller_args = [
        f'--name={app_name}',
        '--onedir',  # مجلد واحد يحتوي على جميع المكتبات
        '--console',  # تفعيل نافذة console لعرض الأخطاء
        '--noconfirm',  # عدم طلب تأكيد
        '--clean',  # تنظيف البناء السابق
        '--noupx',  # تعطيل ضغط UPX لتجنب مشاكل محتملة
    ]
    
    # إضافة الأيقونة إذا كانت موجودة
    icon_path = Path('assets/app_icon.ico')
    if icon_path.exists():
        pyinstaller_args.append(f'--icon={icon_path}')
        print(f"✅ سيتم استخدام الأيقونة: {icon_path}")
    else:
        print("⚠️ ملف الأيقونة غير موجود، سيتم البناء بدون أيقونة")
    
    # إضافة البيانات والملفات المطلوبة
    data_files = [
        ('assets', 'assets'),
        ('ui', 'ui'),
        ('utils', 'utils'),
        ('config.py', '.'),
        ('models.py', '.'),
        ('database.py', '.'),
        ('permissions_manager.py', '.'),
        ('activity_monitor.py', '.'),
    ]
    
    for source, dest in data_files:
        if Path(source).exists():
            pyinstaller_args.append(f'--add-data={source};{dest}')
            print(f"✅ سيتم تضمين: {source}")
    
    # إضافة مكتبات مخفية مطلوبة
    hidden_imports = [
        'tkinter', 'tkinter.ttk', 'ttkbootstrap',
        'PIL', 'PIL.Image', 'PIL.ImageTk',
        'pandas', 'sqlite3', 'bcrypt',
        'reportlab', 'matplotlib', 'openpyxl', 'xlsxwriter'
    ]
    
    for module in hidden_imports:
        pyinstaller_args.append(f'--hidden-import={module}')
    
    # إضافة الملف الرئيسي
    pyinstaller_args.append('run_app.py')
    
    print("🚀 بدء عملية البناء...")
    print("⏳ قد يستغرق هذا عدة دقائق...")
    
    try:
        # تشغيل PyInstaller
        PyInstaller.__main__.run(pyinstaller_args)
        print("✅ تم بناء الملف التنفيذي بنجاح!")
        print(f"📁 المجلد: dist/{app_name}/")
        print(f"📄 الملف التنفيذي: {app_name}.exe")
        print("🎉 جميع المكتبات مدمجة في مجلد _internal")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في عملية البناء: {e}")
        return False

if __name__ == "__main__":
    build_arabic_exe()
