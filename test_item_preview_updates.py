#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تحديث شاشة بيانات الصنف
Test Item Preview Window Updates
"""

import tkinter as tk
import sys
import os

# إضافة المجلد الجذر للمسار
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from database import db_manager
from ui.item_preview_window import ItemPreviewWindow

def test_item_preview_updates():
    """اختبار تحديث شاشة بيانات الصنف"""
    
    print("🧪 بدء اختبار تحديث شاشة بيانات الصنف...")
    
    # إنشاء النافذة الرئيسية
    root = tk.Tk()
    root.withdraw()  # إخفاء النافذة الرئيسية
    
    try:
        # البحث عن صنف موجود في قاعدة البيانات
        items = db_manager.fetch_all("""
            SELECT * FROM added_items 
            WHERE is_active = 1 
            LIMIT 1
        """)
        
        if not items:
            print("❌ لا توجد أصناف في قاعدة البيانات للاختبار")
            return
        
        item = items[0]
        print(f"✅ تم العثور على صنف للاختبار: {item[2]} - {item[3]}")
        
        # تحضير بيانات الصنف
        item_data = {
            'id': item[0],
            'sequence_number': item[1],
            'item_number': item[2],
            'item_name': item[3],
            'custody_type': item[4],
            'classification': item[5],
            'unit': item[6],
            'current_quantity': item[7],
            'entered_quantity': item[8],
            'data_entry_user': item[9],
            'entry_date': item[10],
            'is_active': item[11]
        }
        
        print(f"📊 بيانات الصنف: {item_data}")
        
        # فتح شاشة بيانات الصنف
        preview_window = ItemPreviewWindow(root, item_data)
        
        print("✅ تم فتح شاشة بيانات الصنف بنجاح")
        print("🔄 اختبار التحديث التلقائي...")
        
        # اختبار التحديث اليدوي
        preview_window.refresh_data()
        print("✅ تم اختبار التحديث اليدوي")
        
        # اختبار حساب الكميات
        current_qty = preview_window.calculate_current_quantity()
        entered_qty = preview_window.get_entered_quantity()
        dispensed_qty = preview_window.get_dispensed_quantity()
        
        print(f"📊 نتائج حساب الكميات:")
        print(f"   - الكمية الحالية: {current_qty}")
        print(f"   - الكمية المدخلة: {entered_qty}")
        print(f"   - الكمية المصروفة: {dispensed_qty}")
        
        # اختبار تحديث قاعدة البيانات
        preview_window.update_database_quantity()
        print("✅ تم اختبار تحديث قاعدة البيانات")
        
        # التحقق من التحديث في قاعدة البيانات
        updated_item = db_manager.fetch_one("""
            SELECT current_quantity FROM added_items WHERE id = ?
        """, (item[0],))
        
        if updated_item:
            print(f"✅ الكمية المحدثة في قاعدة البيانات: {updated_item[0]}")
        
        print("🎉 تم اختبار جميع وظائف التحديث بنجاح!")
        
        # تشغيل النافذة
        root.mainloop()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        try:
            root.destroy()
        except:
            pass

if __name__ == "__main__":
    test_item_preview_updates()
