#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
شاشة إدارة الوحدات
"""

import tkinter as tk
from tkinter import ttk, messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from typing import List, Optional
import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models import Unit, Department
from database import db_manager
from ui.global_shortcuts import GlobalShortcuts, ContextHandler

class UnitsWindow:
    """شاشة إدارة الوحدات"""
    
    def __init__(self, parent, main_window=None):
        self.parent = parent
        self.main_window = main_window
        self.current_unit = None
        self.units_data = []
        self.departments_data = []
        self.update_callback = None  # callback للتحديث الخارجي
        
        self.setup_window()
        self.create_widgets()
        self.load_data()
    
    def set_update_callback(self, callback):
        """تعيين دالة callback للتحديث الخارجي"""
        self.update_callback = callback
        print("[callback] تم تعيين دالة التحديث الخارجي")
    
    def setup_window(self):
        """إعداد النافذة"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("إدارة الوحدات")
        self.window.geometry("1100x700")
        self.window.resizable(True, True)
        
        # توسيط النافذة
        self.center_window()
        
        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        self.window.grab_set()
        self.window.lift()
        self.window.focus_force()
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        x = (screen_width - 1100) // 2
        y = (screen_height - 700) // 2
        self.window.geometry(f"1100x700+{x}+{y}")
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.window)
        main_frame.pack(fill=BOTH, expand=True, padx=20, pady=20)

        # العنوان
        self.create_header(main_frame)

        # نموذج الإدخال
        self.create_input_form(main_frame)

        # جدول البيانات
        self.create_data_table(main_frame)

        # شريط الحالة
        self.create_status_bar(main_frame)
    
    def create_header(self, parent):
        """إنشاء العنوان"""
        header_frame = ttk_bs.Frame(parent)
        header_frame.pack(fill=X, pady=(0, 20))

        # العنوان
        title_label = ttk_bs.Label(
            header_frame,
            text="🏛️ إدارة الوحدات",
            bootstyle="primary"
        )
        title_label.pack()

        # تفعيل مفاتيح الاختصار العامة
        self.setup_shortcuts()

    def create_input_form(self, parent):
        """إنشاء نموذج الإدخال"""
        # إطار النموذج
        form_frame = ttk_bs.LabelFrame(parent, text="إضافة وحدة جديدة", padding=15)
        form_frame.pack(fill=X, pady=(0, 20))

        # إطار الحقول
        fields_frame = ttk_bs.Frame(form_frame)
        fields_frame.pack(fill=X)

        # متغيرات النموذج
        self.unit_name_var = tk.StringVar()

        # حقل اسم الوحدة
        ttk_bs.Label(fields_frame, text="اسم الوحدة:").pack(side=LEFT, padx=(0, 10))

        name_entry = ttk_bs.Entry(
            fields_frame,
            textvariable=self.unit_name_var,
            width=30
        )
        name_entry.pack(side=LEFT, padx=(0, 15))

        # زر الحفظ
        save_btn = ttk_bs.Button(
            fields_frame,
            text="💾 حفظ",
            command=self.save_unit,
            bootstyle="success",
            width=12
        )
        save_btn.pack(side=LEFT, padx=(0, 10))

        # زر مسح
        clear_btn = ttk_bs.Button(
            fields_frame,
            text="🗑️ مسح",
            command=self.clear_form,
            bootstyle="secondary",
            width=12
        )
        clear_btn.pack(side=LEFT)
    
    def create_data_table(self, parent):
        """إنشاء جدول البيانات"""
        # إطار الجدول
        table_frame = ttk_bs.LabelFrame(parent, text="قائمة الوحدات", padding=10)
        table_frame.pack(fill=BOTH, expand=True, pady=(0, 10))

        # إنشاء Treeview
        columns = ("id", "name", "status")
        self.tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)

        # تعيين عناوين الأعمدة
        self.tree.heading("id", text="التسلسل")
        self.tree.heading("name", text="اسم الوحدة")
        self.tree.heading("status", text="الحالة")

        # تعيين عرض الأعمدة
        self.tree.column("id", width=100, anchor=CENTER)
        self.tree.column("name", width=400, anchor=E)
        self.tree.column("status", width=150, anchor=CENTER)

        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient=VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)

        # تخطيط الجدول
        self.tree.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar.pack(side=RIGHT, fill=Y)

        # ربط الأحداث
        self.tree.bind("<<TreeviewSelect>>", self.on_item_select)
        self.tree.bind("<Button-3>", self.show_context_menu)  # النقر بالزر الأيمن
    
    def create_status_bar(self, parent):
        """إنشاء شريط الحالة"""
        self.status_bar = ttk_bs.Label(
            parent,
            text="جاهز",
            relief=SUNKEN,
            anchor=W
        )
        self.status_bar.pack(fill=X, side=BOTTOM)
    
    def load_data(self):
        """تحميل البيانات مرتبة حسب التسلسل"""
        try:
            self.units_data = Unit.get_all(active_only=False)
            # ترتيب البيانات حسب ID من الأصغر إلى الأكبر
            self.units_data.sort(key=lambda x: x.id or 0)
            self.populate_table()
            self.update_status(f"تم تحميل {len(self.units_data)} وحدة (مرتبة حسب التسلسل)")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل البيانات: {e}")
            self.update_status("خطأ في تحميل البيانات")

    def populate_table(self):
        """ملء الجدول بالبيانات"""
        # مسح البيانات الموجودة
        for item in self.tree.get_children():
            self.tree.delete(item)

        # إضافة البيانات الجديدة
        for unit in self.units_data:
            status = "نشط" if unit.is_active else "غير نشط"
            self.tree.insert("", "end", values=(
                unit.id,
                unit.name,
                status
            ))
    
    def on_item_select(self, event):
        """معالج اختيار عنصر من الجدول"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            unit_id = item['values'][0]
            self.current_unit = next(
                (u for u in self.units_data if u.id == unit_id), None
            )

    def show_context_menu(self, event):
        """عرض القائمة المنبثقة"""
        # تحديد العنصر المحدد
        item = self.tree.identify_row(event.y)
        if item:
            self.tree.selection_set(item)
            self.on_item_select(event)

            # إنشاء القائمة المنبثقة
            context_menu = tk.Menu(self.window, tearoff=0)
            context_menu.add_command(label="✏️ تعديل الوحدة", command=self.edit_unit)
            context_menu.add_command(label="🗑️ حذف الوحدة", command=self.delete_unit)

            # عرض القائمة
            try:
                context_menu.tk_popup(event.x_root, event.y_root)
            finally:
                context_menu.grab_release()

    def save_unit(self):
        """حفظ وحدة جديدة"""
        unit_name = self.unit_name_var.get().strip()

        if not unit_name:
            messagebox.showerror("خطأ", "يرجى إدخال اسم الوحدة")
            return

        try:
            # إنشاء وحدة جديدة
            unit = Unit(name=unit_name, is_active=True)

            if unit.save():
                self.show_success_message("تم إضافة الوحدة بنجاح")
                self.clear_form()
                self.refresh_data()
                
                # استدعاء callback للتحديث الخارجي
                if self.update_callback:
                    try:
                        self.update_callback()
                        print("[callback] تم استدعاء دالة التحديث الخارجي بعد الإضافة")
                    except Exception as e:
                        print(f"[callback] خطأ في استدعاء دالة التحديث: {e}")
            else:
                messagebox.showerror("خطأ", "فشل في إضافة الوحدة")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ: {e}")

    def clear_form(self):
        """مسح النموذج"""
        self.unit_name_var.set("")
    
    def edit_unit(self):
        """تعديل الوحدة المحددة"""
        if not self.current_unit:
            messagebox.showwarning("تحذير", "يرجى اختيار وحدة للتعديل")
            return

        # نافذة تعديل بسيطة
        dialog = UnitEditDialog(self.window, "تعديل الوحدة", self.current_unit)
        if dialog.result:
            self.current_unit.name = dialog.result['name']
            self.current_unit.is_active = dialog.result['is_active']

            if self.current_unit.save():
                self.show_success_message("تم تحديث الوحدة بنجاح")
                self.refresh_data()
                
                # استدعاء callback للتحديث الخارجي
                if self.update_callback:
                    try:
                        self.update_callback()
                        print("[callback] تم استدعاء دالة التحديث الخارجي بعد التعديل")
                    except Exception as e:
                        print(f"[callback] خطأ في استدعاء دالة التحديث: {e}")
            else:
                messagebox.showerror("خطأ", "فشل في تحديث الوحدة")
    
    def delete_unit(self):
        """حذف الوحدة المحددة"""
        if not self.current_unit:
            messagebox.showwarning("تحذير", "يرجى اختيار وحدة للحذف")
            return

        # التحقق من وجود إدارات مرتبطة بالوحدة
        if not self.check_unit_can_be_deleted():
            return

        if messagebox.askyesno("تأكيد الحذف",
                              f"هل تريد حذف الوحدة '{self.current_unit.name}' نهائياً؟\n"
                              "سيتم حذف الوحدة وجميع البيانات المرتبطة بها."):
            try:
                # حذف البيانات المرتبطة أولاً
                unit_id = self.current_unit.id
                
                # حذف الأقسام المرتبطة بالإدارات التابعة لهذه الوحدة
                db_manager.execute_query("""
                    DELETE FROM sections 
                    WHERE department_id IN (
                        SELECT id FROM departments WHERE unit_id = ?
                    )
                """, (unit_id,))
                
                # حذف الإدارات المرتبطة بهذه الوحدة
                db_manager.execute_query("DELETE FROM departments WHERE unit_id = ?", (unit_id,))
                
                # حذف المستفيدين المرتبطين بهذه الوحدة
                db_manager.execute_query("DELETE FROM beneficiaries WHERE unit_id = ?", (unit_id,))
                
                # حذف الوحدة نفسها نهائياً من قاعدة البيانات
                db_manager.execute_query("DELETE FROM units WHERE id = ?", (unit_id,))
                
                self.show_success_message("تم حذف الوحدة وجميع البيانات المرتبطة بها بنجاح")
                self.current_unit = None  # مسح المتغير
                self.refresh_data()
                
                # استدعاء callback للتحديث الخارجي
                if self.update_callback:
                    try:
                        self.update_callback()
                        print("[callback] تم استدعاء دالة التحديث الخارجي بعد الحذف")
                    except Exception as e:
                        print(f"[callback] خطأ في استدعاء دالة التحديث: {e}")
                    
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ أثناء الحذف: {str(e)}")

    def check_unit_can_be_deleted(self):
        """التحقق من إمكانية حذف الوحدة"""
        try:
            unit_id = self.current_unit.id

            # التحقق من وجود إدارات مرتبطة بالوحدة
            try:
                departments_count = db_manager.fetch_one(
                    "SELECT COUNT(*) as count FROM departments WHERE unit_id = ? AND is_active = 1",
                    (unit_id,)
                )
                count = departments_count['count'] if departments_count else 0

                if count > 0:
                    self.show_error_message(
                        f"❌ لا يمكن حذف الوحدة '{self.current_unit.name}'\n\n"
                        f"يوجد {count} إدارة مرتبطة بهذه الوحدة في شاشة إدارة الإدارات\n\n"
                        "يرجى حذف أو نقل الإدارات أولاً"
                    )
                    return False
            except Exception as e:
                print(f"خطأ في فحص الإدارات: {e}")

            # التحقق من وجود مستفيدين مرتبطين بالوحدة
            try:
                beneficiaries_count = db_manager.fetch_one(
                    "SELECT COUNT(*) as count FROM beneficiaries WHERE unit_id = ? AND is_active = 1",
                    (unit_id,)
                )
                count = beneficiaries_count['count'] if beneficiaries_count else 0

                if count > 0:
                    self.show_error_message(
                        f"❌ لا يمكن حذف الوحدة '{self.current_unit.name}'\n\n"
                        f"يوجد {count} مستفيد مرتبط بهذه الوحدة\n\n"
                        "يرجى حذف أو نقل المستفيدين أولاً"
                    )
                    return False
            except Exception as e:
                print(f"خطأ في فحص المستفيدين: {e}")

            return True

        except Exception as e:
            print(f"خطأ عام في التحقق من الحذف: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ أثناء التحقق من إمكانية الحذف: {e}")
            return False
    
    def refresh_data(self):
        """تحديث البيانات"""
        self.load_data()
    
    def update_status(self, message):
        """تحديث شريط الحالة"""
        self.status_bar.config(text=message)

    def show_success_message(self, message="تم بنجاح"):
        """عرض رسالة النجاح تختفي خلال 3 ثوان أو بالضغط"""
        # إنشاء نافذة رسالة مخصصة
        success_window = tk.Toplevel(self.window)
        success_window.title("✅ تم بنجاح")
        success_window.geometry("400x200")
        success_window.resizable(False, False)
        success_window.transient(self.window)
        success_window.grab_set()
        success_window.configure(bg='#d4edda')

        # توسيط النافذة
        success_window.update_idletasks()
        x = (success_window.winfo_screenwidth() - 400) // 2
        y = (success_window.winfo_screenheight() - 200) // 2
        success_window.geometry(f"400x200+{x}+{y}")

        # ربط الضغط في أي مكان لإغلاق النافذة
        def close_on_click(event=None):
            success_window.destroy()

        success_window.bind("<Button-1>", close_on_click)
        success_window.bind("<Key>", close_on_click)
        success_window.focus_set()

        # محتوى الرسالة
        frame = ttk_bs.Frame(success_window)
        frame.pack(fill=tk.BOTH, expand=True, padx=30, pady=30)
        frame.bind("<Button-1>", close_on_click)

        # أيقونة النجاح
        success_icon = ttk_bs.Label(
            frame,
            text="✅",
            bootstyle="success"
        )
        success_icon.pack(pady=10)
        success_icon.bind("<Button-1>", close_on_click)

        # رسالة النجاح
        success_label = ttk_bs.Label(
            frame,
            text=message,
            bootstyle="success"
        )
        success_label.pack(pady=10)
        success_label.bind("<Button-1>", close_on_click)

        # رسالة إرشادية
        info_label = ttk_bs.Label(
            frame,
            text="اضغط في أي مكان للإغلاق",
            bootstyle="secondary"
        )
        info_label.pack(pady=5)
        info_label.bind("<Button-1>", close_on_click)

        # إغلاق تلقائي بعد 3 ثوان
        success_window.after(3000, close_on_click)

    def show_error_message(self, message="حدث خطأ"):
        """عرض رسالة خطأ تختفي خلال 3 ثوان أو بالضغط"""
        # إنشاء نافذة رسالة مخصصة
        error_window = tk.Toplevel(self.window)
        error_window.title("❌ خطأ")
        error_window.geometry("450x250")
        error_window.resizable(False, False)
        error_window.transient(self.window)
        error_window.grab_set()
        error_window.configure(bg='#f8d7da')

        # توسيط النافذة
        error_window.update_idletasks()
        x = (error_window.winfo_screenwidth() - 450) // 2
        y = (error_window.winfo_screenheight() - 250) // 2
        error_window.geometry(f"450x250+{x}+{y}")

        # ربط الضغط في أي مكان لإغلاق النافذة
        def close_on_click(event=None):
            error_window.destroy()

        error_window.bind("<Button-1>", close_on_click)
        error_window.bind("<Key>", close_on_click)
        error_window.focus_set()

        # محتوى الرسالة
        frame = ttk_bs.Frame(error_window)
        frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        frame.bind("<Button-1>", close_on_click)

        # أيقونة الخطأ
        error_icon = ttk_bs.Label(
            frame,
            text="❌",
            font=("Arial", 24),
            bootstyle="danger"
        )
        error_icon.pack(pady=10)
        error_icon.bind("<Button-1>", close_on_click)

        # رسالة الخطأ
        error_label = ttk_bs.Label(
            frame,
            text=message,
            font=("Arial", 11),
            bootstyle="danger",
            justify=CENTER
        )
        error_label.pack(pady=10)
        error_label.bind("<Button-1>", close_on_click)

        # رسالة إرشادية
        hint_label = ttk_bs.Label(
            frame,
            text="(اضغط في أي مكان للإغلاق)",
            font=("Arial", 9),
            bootstyle="secondary"
        )
        hint_label.pack(pady=5)
        hint_label.bind("<Button-1>", close_on_click)

        # إغلاق تلقائي بعد 3 ثوان
        error_window.after(3000, close_on_click)

    def setup_shortcuts(self):
        """إعداد مفاتيح الاختصار العامة"""
        try:
            # إنشاء معالج السياق
            self.context_handler = ContextHandler()
            
            # تعيين دوال العمليات (يمكن تخصيصها حسب النافذة)
            self.context_handler.set_save_callback(self.shortcut_save)
            self.context_handler.set_delete_callback(self.shortcut_delete)
            self.context_handler.set_copy_callback(self.shortcut_copy)
            self.context_handler.set_paste_callback(self.shortcut_paste)
            
            # تفعيل مفاتيح الاختصار
            window = getattr(self, 'window', None) or getattr(self, 'parent', None)
            if window:
                self.global_shortcuts = GlobalShortcuts(window, self.context_handler)
        except Exception as e:
            print(f"خطأ في إعداد مفاتيح الاختصار: {e}")
    
    def shortcut_save(self):
        """عملية الحفظ (F1)"""
        try:
            if hasattr(self, 'save_data'):
                self.save_data()
            elif hasattr(self, 'save_changes'):
                self.save_changes()
            elif hasattr(self, 'save'):
                self.save()
            else:
                print("عملية الحفظ غير متاحة")
        except Exception as e:
            print(f"خطأ في عملية الحفظ: {e}")
    
    def shortcut_delete(self):
        """عملية الحذف (F2)"""
        try:
            if hasattr(self, 'delete_selected'):
                self.delete_selected()
            elif hasattr(self, 'delete_item'):
                self.delete_item()
            elif hasattr(self, 'delete'):
                self.delete()
            else:
                print("عملية الحذف غير متاحة")
        except Exception as e:
            print(f"خطأ في عملية الحذف: {e}")
    
    def shortcut_copy(self):
        """عملية النسخ (F3)"""
        try:
            if hasattr(self, 'copy_data'):
                self.copy_data()
            else:
                # نسخ عامة للبيانات المحددة
                import pyperclip
                pyperclip.copy("تم النسخ من النافذة")
                print("تم نسخ البيانات")
        except Exception as e:
            print(f"خطأ في عملية النسخ: {e}")
    
    def shortcut_paste(self):
        """عملية اللصق (F4)"""
        try:
            if hasattr(self, 'paste_data'):
                self.paste_data()
            else:
                import pyperclip
                clipboard_text = pyperclip.paste()
                if clipboard_text:
                    print(f"تم لصق: {clipboard_text[:50]}")
                else:
                    print("لا توجد بيانات في الحافظة")
        except Exception as e:
            print(f"خطأ في عملية اللصق: {e}")

class UnitEditDialog:
    """نافذة حوار تعديل الوحدة"""

    def __init__(self, parent, title, unit):
        self.result = None
        self.unit = unit

        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("400x200")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # توسيط النافذة
        self.center_window()

        # إعداد المحتوى
        self.setup_dialog()

        # انتظار إغلاق النافذة
        self.dialog.wait_window()

    def center_window(self):
        """توسيط النافذة"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() - 400) // 2
        y = (self.dialog.winfo_screenheight() - 200) // 2
        self.dialog.geometry(f"400x200+{x}+{y}")

    def setup_dialog(self):
        """إعداد محتوى النافذة"""
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.dialog)
        main_frame.pack(fill=BOTH, expand=True, padx=20, pady=20)

        # متغيرات النموذج
        self.name_var = tk.StringVar(value=self.unit.name)
        self.is_active_var = tk.BooleanVar(value=self.unit.is_active)

        # اسم الوحدة
        ttk_bs.Label(main_frame, text="اسم الوحدة:").pack(anchor=E, pady=(0, 5))
        ttk_bs.Entry(main_frame, textvariable=self.name_var, width=40).pack(fill=X, pady=(0, 15))

        # خانة اختيار الحالة
        ttk_bs.Checkbutton(
            main_frame,
            text="الوحدة نشطة",
            variable=self.is_active_var,
            bootstyle="success"
        ).pack(anchor=E, pady=(0, 20))

        # الأزرار
        buttons_frame = ttk_bs.Frame(main_frame)
        buttons_frame.pack()

        ttk_bs.Button(
            buttons_frame,
            text="💾 حفظ",
            command=self.save_data,
            bootstyle="success",
            width=15
        ).pack(side=LEFT, padx=10)

        ttk_bs.Button(
            buttons_frame,
            text="❌ إلغاء",
            command=self.cancel,
            bootstyle="secondary",
            width=15
        ).pack(side=LEFT, padx=10)

    def save_data(self):
        """حفظ البيانات"""
        if not self.name_var.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال اسم الوحدة")
            return

        self.result = {
            'name': self.name_var.get().strip(),
            'is_active': self.is_active_var.get()
        }

        self.dialog.destroy()

    def cancel(self):
        """إلغاء العملية"""
        self.dialog.destroy()
