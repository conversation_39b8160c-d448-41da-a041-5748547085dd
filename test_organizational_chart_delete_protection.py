#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار حماية حذف الأصناف من الجدول التنظيمي
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models import OrganizationalChart, AddedItem
from database import db_manager

def test_delete_protection():
    """اختبار حماية حذف الأصناف المسجلة في المخزون"""
    print("🧪 اختبار حماية حذف الأصناف من الجدول التنظيمي...")
    
    try:
        # إنشاء صنف تجريبي في الجدول التنظيمي
        test_org_item = OrganizationalChart(
            sequence_number=999,
            item_code="TEST999",
            item_name="صنف تجريبي للحذف",
            unit="قطعة",
            quantity=50.0,
            notes="اختبار حماية الحذف",
            is_active=True
        )
        
        if test_org_item.save():
            print("✅ تم إنشاء صنف تجريبي في الجدول التنظيمي")
            org_item_id = test_org_item.id
        else:
            print("❌ فشل في إنشاء صنف تجريبي")
            return False
        
        # اختبار 1: حذف صنف غير مسجل في المخزون (يجب أن ينجح)
        print("\n📋 اختبار 1: حذف صنف غير مسجل في المخزون")
        check_result = test_org_item.check_inventory_before_delete()
        print(f"نتيجة التحقق: {check_result}")
        
        if check_result['can_delete']:
            print("✅ يمكن حذف الصنف (غير مسجل في المخزون)")
        else:
            print("❌ لا يمكن حذف الصنف (خطأ غير متوقع)")
            return False
        
        # إضافة الصنف للمخزون بكمية صفر
        print("\n📋 اختبار 2: إضافة الصنف للمخزون بكمية صفر")
        test_inventory_item = AddedItem(
            item_number="TEST999",
            item_name="صنف تجريبي للحذف",
            custody_type="عهدة",
            classification="تجريبي",
            unit="قطعة",
            current_quantity=0,
            entered_quantity=0,
            data_entry_user="نظام الاختبار",
            is_active=True
        )
        
        if test_inventory_item.save():
            print("✅ تم إضافة الصنف للمخزون بكمية صفر")
        else:
            print("❌ فشل في إضافة الصنف للمخزون")
            return False
        
        # اختبار حذف صنف مسجل في المخزون بكمية صفر (يجب أن يفشل)
        check_result = test_org_item.check_inventory_before_delete()
        print(f"نتيجة التحقق: {check_result}")
        
        if not check_result['can_delete']:
            print("✅ لا يمكن حذف الصنف (مسجل في المخزون)")
        else:
            print("❌ يمكن حذف الصنف (خطأ - يجب منع الحذف)")
            return False
        
        # تحديث كمية الصنف في المخزون
        print("\n📋 اختبار 3: تحديث كمية الصنف في المخزون")
        test_inventory_item.current_quantity = 25
        test_inventory_item.entered_quantity = 25
        
        if test_inventory_item.save():
            print("✅ تم تحديث كمية الصنف في المخزون إلى 25")
        else:
            print("❌ فشل في تحديث كمية الصنف")
            return False
        
        # اختبار حذف صنف مسجل في المخزون بكمية أكبر من صفر (يجب أن يفشل)
        check_result = test_org_item.check_inventory_before_delete()
        print(f"نتيجة التحقق: {check_result}")
        
        if not check_result['can_delete'] and "بكمية 25" in check_result['message']:
            print("✅ لا يمكن حذف الصنف (يحتوي على كمية في المخزون)")
        else:
            print("❌ يمكن حذف الصنف (خطأ - يجب منع الحذف)")
            return False
        
        # اختبار 4: محاولة حذف الصنف فعلياً
        print("\n📋 اختبار 4: محاولة حذف الصنف فعلياً")
        delete_result = test_org_item.delete()
        
        if not delete_result:
            print("✅ فشل حذف الصنف كما هو متوقع (محمي)")
        else:
            print("❌ تم حذف الصنف (خطأ - يجب منع الحذف)")
            return False
        
        # تنظيف البيانات التجريبية
        print("\n🧹 تنظيف البيانات التجريبية...")
        
        # حذف من المخزون أولاً
        db_manager.execute_query("DELETE FROM added_items WHERE item_number = ?", ("TEST999",))
        print("✅ تم حذف الصنف من المخزون")
        
        # الآن يجب أن يكون بإمكان حذف الصنف من الجدول التنظيمي
        check_result = test_org_item.check_inventory_before_delete()
        if check_result['can_delete']:
            if test_org_item.delete():
                print("✅ تم حذف الصنف من الجدول التنظيمي بعد إزالته من المخزون")
            else:
                print("⚠️ فشل في حذف الصنف من الجدول التنظيمي")
        else:
            # حذف مباشر من قاعدة البيانات للتنظيف
            db_manager.execute_query("DELETE FROM organizational_chart WHERE id = ?", (org_item_id,))
            print("✅ تم تنظيف الصنف من الجدول التنظيمي")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار حماية الحذف: {e}")
        return False

def test_delete_all_protection():
    """اختبار حماية حذف جميع الأصناف"""
    print("\n🧪 اختبار حماية حذف جميع الأصناف...")
    
    try:
        # إنشاء عدة أصناف تجريبية
        test_items = []
        for i in range(3):
            item = OrganizationalChart(
                sequence_number=1000 + i,
                item_code=f"TESTALL{i}",
                item_name=f"صنف تجريبي {i}",
                unit="قطعة",
                quantity=10.0,
                notes="اختبار حذف جميع الأصناف",
                is_active=True
            )
            if item.save():
                test_items.append(item)
                print(f"✅ تم إنشاء صنف تجريبي {i}")
        
        # إضافة أحد الأصناف للمخزون
        inventory_item = AddedItem(
            item_number="TESTALL1",
            item_name="صنف تجريبي 1",
            custody_type="عهدة",
            classification="تجريبي",
            unit="قطعة",
            current_quantity=15,
            entered_quantity=15,
            data_entry_user="نظام الاختبار",
            is_active=True
        )
        
        if inventory_item.save():
            print("✅ تم إضافة صنف واحد للمخزون")
        
        # اختبار حذف جميع الأصناف
        print("\n📋 اختبار حذف جميع الأصناف...")
        initial_count = len(OrganizationalChart.get_all(active_only=True))
        print(f"عدد الأصناف قبل الحذف: {initial_count}")
        
        result = OrganizationalChart.delete_all()
        
        final_count = len(OrganizationalChart.get_all(active_only=True))
        print(f"عدد الأصناف بعد الحذف: {final_count}")
        
        # يجب أن يبقى صنف واحد على الأقل (المحمي)
        if final_count < initial_count:
            print("✅ تم حذف بعض الأصناف وحماية الأصناف المسجلة في المخزون")
        else:
            print("⚠️ لم يتم حذف أي أصناف")
        
        # تنظيف البيانات التجريبية
        print("\n🧹 تنظيف البيانات التجريبية...")
        db_manager.execute_query("DELETE FROM added_items WHERE item_number LIKE 'TESTALL%'")
        db_manager.execute_query("DELETE FROM organizational_chart WHERE item_code LIKE 'TESTALL%'")
        print("✅ تم تنظيف البيانات التجريبية")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار حذف جميع الأصناف: {e}")
        return False

def main():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء اختبارات حماية حذف الأصناف من الجدول التنظيمي")
    print("=" * 70)
    
    tests = [
        ("اختبار حماية حذف الأصناف المفردة", test_delete_protection),
        ("اختبار حماية حذف جميع الأصناف", test_delete_all_protection),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                print(f"✅ {test_name}: نجح")
                passed += 1
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {e}")
    
    print("\n" + "="*70)
    print(f"📊 النتائج النهائية: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت!")
        print("✅ حماية حذف الأصناف من الجدول التنظيمي تعمل بشكل صحيح")
        print("\n📋 ملخص الحماية:")
        print("   • لا يمكن حذف الأصناف المسجلة في المخزون")
        print("   • لا يمكن حذف الأصناف التي تحتوي على كميات")
        print("   • رسائل تحذيرية واضحة للمستخدم")
        print("   • حماية شاملة للحذف الفردي والجماعي")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت")
        return False

if __name__ == "__main__":
    main()
