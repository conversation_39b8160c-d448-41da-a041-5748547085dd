"""
مولد ملفات PDF - تطبيق إدارة المخازن
PDF Generator - Desktop Stores Management System
"""

from reportlab.lib.pagesizes import A4, letter
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch, cm
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
from reportlab.platypus.tableofcontents import TableOfContents
from reportlab.pdfgen import canvas
from reportlab.lib.enums import TA_CENTER, TA_RIGHT, TA_LEFT
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any, Optional
import logging

from config import REPORTS_DIR, APP_CONFIG, REPORTS_CONFIG
from utils.logger import setup_logger

logger = setup_logger("PDFGenerator")

class PDFGenerator:
    """مولد ملفات PDF"""
    
    def __init__(self):
        self.reports_dir = REPORTS_DIR
        self.reports_dir.mkdir(exist_ok=True)
        
        # إعداد الخطوط العربية
        self.setup_arabic_fonts()
        
        # إعداد الأنماط
        self.setup_styles()
    
    def setup_arabic_fonts(self):
        """إعداد الخطوط العربية"""
        try:
            # محاولة تحميل خط عربي
            # يمكن إضافة ملفات خطوط عربية في مجلد assets/fonts
            fonts_dir = Path(__file__).parent.parent / "assets" / "fonts"
            
            if fonts_dir.exists():
                for font_file in fonts_dir.glob("*.ttf"):
                    try:
                        font_name = font_file.stem
                        pdfmetrics.registerFont(TTFont(font_name, str(font_file)))
                        logger.info(f"تم تحميل الخط: {font_name}")
                    except Exception as e:
                        logger.warning(f"تعذر تحميل الخط {font_file}: {e}")
            
            # استخدام خط افتراضي إذا لم تتوفر خطوط عربية
            self.arabic_font = "Helvetica"  # يمكن تغييره إلى خط عربي
            
        except Exception as e:
            logger.warning(f"تعذر إعداد الخطوط العربية: {e}")
            self.arabic_font = "Helvetica"
    
    def setup_styles(self):
        """إعداد أنماط النص"""
        self.styles = getSampleStyleSheet()
        
        # نمط العنوان الرئيسي
        self.styles.add(ParagraphStyle(
            name='ArabicTitle',
            parent=self.styles['Title'],
            fontName=self.arabic_font,
            fontSize=18,
            alignment=TA_CENTER,
            spaceAfter=20,
            textColor=colors.HexColor('#0a3d62')
        ))
        
        # نمط العنوان الفرعي
        self.styles.add(ParagraphStyle(
            name='ArabicHeading',
            parent=self.styles['Heading1'],
            fontName=self.arabic_font,
            fontSize=14,
            alignment=TA_RIGHT,
            spaceAfter=12,
            textColor=colors.HexColor('#40739e')
        ))
        
        # نمط النص العادي
        self.styles.add(ParagraphStyle(
            name='ArabicNormal',
            parent=self.styles['Normal'],
            fontName=self.arabic_font,
            fontSize=10,
            alignment=TA_RIGHT,
            spaceAfter=6
        ))
        
        # نمط النص المتوسط
        self.styles.add(ParagraphStyle(
            name='ArabicCenter',
            parent=self.styles['Normal'],
            fontName=self.arabic_font,
            fontSize=10,
            alignment=TA_CENTER
        ))
    
    def create_report(self, title: str, data: List[Dict], headers: Dict[str, str],
                     filename: str, subtitle: str = None) -> tuple[bool, str]:
        """
        إنشاء تقرير PDF
        
        Args:
            title: عنوان التقرير
            data: بيانات التقرير
            headers: ترجمة أسماء الأعمدة
            filename: اسم الملف
            subtitle: عنوان فرعي
        
        Returns:
            (success, file_path)
        """
        try:
            # إنشاء مسار الملف
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            file_path = self.reports_dir / f"{filename}_{timestamp}.pdf"
            
            # إنشاء المستند
            doc = SimpleDocTemplate(
                str(file_path),
                pagesize=A4,
                rightMargin=2*cm,
                leftMargin=2*cm,
                topMargin=2*cm,
                bottomMargin=2*cm
            )
            
            # محتوى التقرير
            story = []
            
            # إضافة رأس التقرير
            self._add_header(story, title, subtitle)
            
            # إضافة معلومات التقرير
            self._add_report_info(story)
            
            # إضافة الجدول
            if data:
                self._add_table(story, data, headers)
            else:
                story.append(Paragraph("لا توجد بيانات للعرض", self.styles['ArabicCenter']))
            
            # إضافة تذييل
            self._add_footer(story)
            
            # بناء المستند
            doc.build(story, onFirstPage=self._add_page_header, onLaterPages=self._add_page_header)
            
            logger.info(f"تم إنشاء تقرير PDF: {file_path}")
            return True, str(file_path)
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء تقرير PDF: {e}")
            return False, f"خطأ في إنشاء التقرير: {e}"
    
    def _add_header(self, story: List, title: str, subtitle: str = None):
        """إضافة رأس التقرير"""
        # العنوان الرئيسي
        story.append(Paragraph(title, self.styles['ArabicTitle']))
        
        # العنوان الفرعي
        if subtitle:
            story.append(Paragraph(subtitle, self.styles['ArabicHeading']))
        
        story.append(Spacer(1, 20))
    
    def _add_report_info(self, story: List):
        """إضافة معلومات التقرير"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        info_data = [
            ["تاريخ التقرير:", current_time],
            ["النظام:", APP_CONFIG['app_name']],
            ["الإصدار:", APP_CONFIG['app_version']]
        ]
        
        info_table = Table(info_data, colWidths=[3*cm, 5*cm])
        info_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            ('ALIGN', (0, 0), (0, -1), 'RIGHT'),
            ('ALIGN', (1, 0), (1, -1), 'LEFT'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
            ('BACKGROUND', (0, 0), (0, -1), colors.HexColor('#f8f9fa'))
        ]))
        
        story.append(info_table)
        story.append(Spacer(1, 20))
    
    def _add_table(self, story: List, data: List[Dict], headers: Dict[str, str]):
        """إضافة جدول البيانات"""
        if not data:
            return
        
        # إعداد البيانات
        table_data = []
        
        # إضافة رأس الجدول
        first_row = data[0]
        header_row = []
        for key in first_row.keys():
            header_text = headers.get(key, key)
            header_row.append(header_text)
        table_data.append(header_row)
        
        # إضافة بيانات الجدول
        for row in data:
            data_row = []
            for key in first_row.keys():
                value = row.get(key, "")
                data_row.append(str(value) if value is not None else "")
            table_data.append(data_row)
        
        # إنشاء الجدول
        table = Table(table_data)
        
        # تنسيق الجدول
        table_style = [
            # تنسيق رأس الجدول
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#40739e')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('FONTNAME', (0, 0), (-1, 0), self.arabic_font),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
            ('VALIGN', (0, 0), (-1, 0), 'MIDDLE'),
            
            # تنسيق بيانات الجدول
            ('FONTNAME', (0, 1), (-1, -1), self.arabic_font),
            ('FONTSIZE', (0, 1), (-1, -1), 9),
            ('ALIGN', (0, 1), (-1, -1), 'CENTER'),
            ('VALIGN', (0, 1), (-1, -1), 'MIDDLE'),
            
            # حدود الجدول
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            
            # تلوين الصفوف بالتناوب
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.HexColor('#f8f9fa')])
        ]
        
        table.setStyle(TableStyle(table_style))
        
        story.append(table)
        story.append(Spacer(1, 20))
    
    def _add_footer(self, story: List):
        """إضافة تذييل التقرير"""
        footer_text = f"تم إنشاء هذا التقرير بواسطة {APP_CONFIG['app_name']}"
        story.append(Spacer(1, 30))
        story.append(Paragraph(footer_text, self.styles['ArabicCenter']))
        
        developer_text = f"تطوير: {APP_CONFIG['app_author']} | {APP_CONFIG['app_contact']}"
        story.append(Paragraph(developer_text, self.styles['ArabicCenter']))
    
    def _add_page_header(self, canvas, doc):
        """إضافة رأس الصفحة"""
        canvas.saveState()
        
        # رقم الصفحة
        page_num = canvas.getPageNumber()
        canvas.setFont(self.arabic_font, 9)
        canvas.drawRightString(A4[0] - 2*cm, A4[1] - 1*cm, f"صفحة {page_num}")
        
        # خط فاصل
        canvas.setStrokeColor(colors.HexColor('#40739e'))
        canvas.setLineWidth(1)
        canvas.line(2*cm, A4[1] - 1.5*cm, A4[0] - 2*cm, A4[1] - 1.5*cm)
        
        canvas.restoreState()
    
    def create_invoice(self, transaction_data: Dict, items: List[Dict], 
                      beneficiary_data: Dict, filename: str) -> tuple[bool, str]:
        """
        إنشاء فاتورة صرف
        
        Args:
            transaction_data: بيانات العملية
            items: قائمة الأصناف
            beneficiary_data: بيانات المستفيد
            filename: اسم الملف
        
        Returns:
            (success, file_path)
        """
        try:
            # إنشاء مسار الملف
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            file_path = self.reports_dir / f"invoice_{filename}_{timestamp}.pdf"
            
            # إنشاء المستند
            doc = SimpleDocTemplate(
                str(file_path),
                pagesize=A4,
                rightMargin=2*cm,
                leftMargin=2*cm,
                topMargin=3*cm,
                bottomMargin=2*cm
            )
            
            story = []
            
            # عنوان الفاتورة
            story.append(Paragraph("إيصال صرف", self.styles['ArabicTitle']))
            story.append(Spacer(1, 20))
            
            # معلومات العملية
            self._add_transaction_info(story, transaction_data, beneficiary_data)
            
            # جدول الأصناف
            self._add_items_table(story, items)
            
            # إجماليات
            self._add_totals(story, items)
            
            # توقيعات
            self._add_signatures(story)
            
            # بناء المستند
            doc.build(story, onFirstPage=self._add_invoice_header, onLaterPages=self._add_invoice_header)
            
            logger.info(f"تم إنشاء فاتورة: {file_path}")
            return True, str(file_path)
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء الفاتورة: {e}")
            return False, f"خطأ في إنشاء الفاتورة: {e}"
    
    def _add_transaction_info(self, story: List, transaction_data: Dict, beneficiary_data: Dict):
        """إضافة معلومات العملية"""
        info_data = [
            ["رقم العملية:", transaction_data.get('transaction_number', '')],
            ["التاريخ:", transaction_data.get('transaction_date', '')],
            ["المستفيد:", beneficiary_data.get('name', '')],
            ["الرقم:", beneficiary_data.get('number', '')],
            ["الرتبة:", beneficiary_data.get('rank', '')],
            ["الإدارة:", beneficiary_data.get('department', '')]
        ]
        
        info_table = Table(info_data, colWidths=[4*cm, 6*cm])
        info_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('ALIGN', (0, 0), (0, -1), 'RIGHT'),
            ('ALIGN', (1, 0), (1, -1), 'LEFT'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('BACKGROUND', (0, 0), (0, -1), colors.HexColor('#f8f9fa'))
        ]))
        
        story.append(info_table)
        story.append(Spacer(1, 20))
    
    def _add_items_table(self, story: List, items: List[Dict]):
        """إضافة جدول الأصناف"""
        headers = ["م", "الصنف", "الكمية", "الوحدة", "ملاحظات"]
        table_data = [headers]
        
        for i, item in enumerate(items, 1):
            row = [
                str(i),
                item.get('name', ''),
                str(item.get('quantity', '')),
                item.get('unit', ''),
                item.get('notes', '')
            ]
            table_data.append(row)
        
        table = Table(table_data, colWidths=[1*cm, 5*cm, 2*cm, 2*cm, 4*cm])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#40739e')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('FONTSIZE', (0, 1), (-1, -1), 9),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.HexColor('#f8f9fa')])
        ]))
        
        story.append(table)
        story.append(Spacer(1, 20))
    
    def _add_totals(self, story: List, items: List[Dict]):
        """إضافة الإجماليات"""
        total_items = len(items)
        total_quantity = sum(item.get('quantity', 0) for item in items)
        
        totals_data = [
            ["إجمالي الأصناف:", str(total_items)],
            ["إجمالي الكمية:", str(total_quantity)]
        ]
        
        totals_table = Table(totals_data, colWidths=[4*cm, 3*cm])
        totals_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('ALIGN', (0, 0), (0, -1), 'RIGHT'),
            ('ALIGN', (1, 0), (1, -1), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('BACKGROUND', (0, 0), (0, -1), colors.HexColor('#f8f9fa'))
        ]))
        
        story.append(totals_table)
        story.append(Spacer(1, 30))
    
    def _add_signatures(self, story: List):
        """إضافة التوقيعات"""
        signatures_data = [
            ["المستلم", "أمين المخزن", "المدير"],
            ["", "", ""],
            ["التوقيع: ___________", "التوقيع: ___________", "التوقيع: ___________"],
            ["التاريخ: ___________", "التاريخ: ___________", "التاريخ: ___________"]
        ]
        
        signatures_table = Table(signatures_data, colWidths=[5*cm, 5*cm, 5*cm])
        signatures_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('GRID', (0, 0), (-1, 0), 1, colors.black),
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#f8f9fa'))
        ]))
        
        story.append(signatures_table)
    
    def _add_invoice_header(self, canvas, doc):
        """إضافة رأس الفاتورة"""
        canvas.saveState()
        
        # شعار المؤسسة (إذا كان متوفراً)
        logo_path = Path(__file__).parent.parent / "assets" / "icons" / "logo.png"
        if logo_path.exists():
            try:
                canvas.drawImage(str(logo_path), 2*cm, A4[1] - 3*cm, width=2*cm, height=2*cm)
            except:
                pass
        
        # معلومات المؤسسة
        canvas.setFont(self.arabic_font, 12)
        canvas.drawRightString(A4[0] - 2*cm, A4[1] - 2*cm, APP_CONFIG['app_name'])
        
        # خط فاصل
        canvas.setStrokeColor(colors.HexColor('#40739e'))
        canvas.setLineWidth(2)
        canvas.line(2*cm, A4[1] - 3.5*cm, A4[0] - 2*cm, A4[1] - 3.5*cm)
        
        canvas.restoreState()
