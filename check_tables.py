#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import sys
import os

# إضافة مسار المشروع
sys.path.append('.')

try:
    from database import db_manager
    
    print('🔍 فحص الجداول الموجودة في قاعدة البيانات...')
    print('=' * 50)

    # عرض جميع الجداول
    tables = db_manager.fetch_all('''
        SELECT name FROM sqlite_master 
        WHERE type='table' 
        ORDER BY name
    ''')

    print(f'📋 الجداول الموجودة ({len(tables)}):')
    for table in tables:
        print(f'  📄 {table["name"]}')

    print('\n🔍 البحث عن عمليات الصرف في الجداول المختلفة...')
    print('=' * 50)

    # البحث في جدول dispensing_operations
    try:
        dispensing_ops = db_manager.fetch_all('''
            SELECT id, operation_number, beneficiary_name, operation_date, created_at
            FROM dispensing_operations
            WHERE beneficiary_name LIKE '%خالد%' OR beneficiary_name LIKE '%تجرب%'
            ORDER BY created_at DESC
        ''')
        
        if dispensing_ops:
            print(f'📦 تم العثور على {len(dispensing_ops)} عملية صرف:')
            for op in dispensing_ops:
                print(f'  🆔 معرف العملية: {op["id"]}')
                print(f'  📄 رقم العملية: {op["operation_number"]}')
                print(f'  👤 اسم المستفيد: {op["beneficiary_name"]}')
                print(f'  📅 تاريخ العملية: {op["operation_date"]}')
                print(f'  ⏰ تاريخ الإنشاء: {op["created_at"]}')
                print('-' * 30)
        else:
            print('❌ لم يتم العثور على عمليات صرف تجريبية في dispensing_operations')
    except Exception as e:
        print(f'❌ خطأ في البحث في dispensing_operations: {e}')

    # البحث في جدول حركات المخزون
    try:
        movements = db_manager.fetch_all('''
            SELECT im.id, im.movement_date, im.item_number, im.movement_type, 
                   im.quantity, im.organization_name, im.notes,
                   ai.item_name
            FROM inventory_movements_new im
            LEFT JOIN added_items ai ON im.item_number = ai.item_number
            WHERE im.organization_name LIKE '%خالد%' OR im.notes LIKE '%تجرب%'
               OR im.notes LIKE '%test%'
            ORDER BY im.movement_date DESC
        ''')

        if movements:
            print(f'\n📦 تم العثور على {len(movements)} حركة مخزون تجريبية:')
            for mov in movements:
                print(f'  🆔 معرف الحركة: {mov["id"]}')
                print(f'  📅 التاريخ: {mov["movement_date"]}')
                print(f'  📦 رقم الصنف: {mov["item_number"]} - {mov["item_name"]}')
                print(f'  🔄 نوع الحركة: {mov["movement_type"]}')
                print(f'  📊 الكمية: {mov["quantity"]}')
                print(f'  🏢 الجهة: {mov["organization_name"]}')
                print(f'  📝 الملاحظات: {mov["notes"]}')
                print('-' * 30)
        else:
            print('\n❌ لم يتم العثور على حركات مخزون تجريبية')
    except Exception as e:
        print(f'❌ خطأ في البحث في inventory_movements_new: {e}')

    # عرض آخر العمليات من dispensing_operations
    try:
        print('\n🕐 آخر 5 عمليات صرف:')
        print('=' * 50)
        recent_operations = db_manager.fetch_all('''
            SELECT id, operation_number, beneficiary_name, operation_date, created_at
            FROM dispensing_operations
            ORDER BY created_at DESC
            LIMIT 5
        ''')
        
        for op in recent_operations:
            print(f'  🆔 {op["id"]} | 📄 {op["operation_number"]} | 👤 {op["beneficiary_name"]} | 📅 {op["operation_date"]}')
    except Exception as e:
        print(f'❌ خطأ في عرض آخر العمليات: {e}')

except Exception as e:
    print(f'❌ خطأ عام: {e}')
    import traceback
    traceback.print_exc()
