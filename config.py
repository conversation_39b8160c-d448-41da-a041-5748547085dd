"""
إعدادات تطبيق إدارة المخازن - سطح المكتب
Desktop Stores Management System Configuration
"""

import os
import json
import sys
from pathlib import Path

def get_safe_app_directory():
    """الحصول على مجلد آمن للتطبيق"""
    if getattr(sys, 'frozen', False):
        # البرنامج يعمل كملف exe - استخدم مجلد المستندات
        documents_path = Path.home() / "Documents"
        app_data_dir = documents_path / "Desktop_Stores_Data"
        app_data_dir.mkdir(exist_ok=True)
        return app_data_dir
    else:
        # البرنامج يعمل من الكود المصدري
        return Path(__file__).parent

# مسارات التطبيق - Application Paths
BASE_DIR = get_safe_app_directory()
DATA_DIR = BASE_DIR / "data"
REPORTS_DIR = BASE_DIR / "reports"
BACKUPS_DIR = BASE_DIR / "backups"
LOGS_DIR = BASE_DIR / "logs"
ASSETS_DIR = BASE_DIR / "assets"
ICONS_DIR = ASSETS_DIR / "icons"

# إنشاء المجلدات إذا لم تكن موجودة
for directory in [DATA_DIR, REPORTS_DIR, BACKUPS_DIR, LOGS_DIR, ASSETS_DIR, ICONS_DIR]:
    directory.mkdir(exist_ok=True)

# قاعدة البيانات - Database Configuration
DATABASE_PATH = BASE_DIR / "stores_management.db"
BACKUP_DATABASE_PATH = BACKUPS_DIR / "stores_backup.db"

# إعدادات التطبيق - Application Settings
APP_CONFIG = {
    "app_name": "نظام إدارة المخازن والمستودعات",
    "app_version": "2.0.0",
    "app_author": "خالد بن سعيد آل عضرس",
    "app_contact": "+966509070400",
    "window_width": 1200,
    "window_height": 900,
    "min_width": 1200,
    "min_height": 700,
    "theme": "cosmo",  # ttkbootstrap theme
    "language": "ar",
    "rtl_support": True,
    "auto_backup": False,
    "backup_interval_hours": 24,
    "session_timeout_minutes": 60,
    "max_login_attempts": 3,
    "password_min_length": 6,
    "low_stock_threshold": 5,
    "items_per_page": 50,
    "recent_items_count": 10,
    "date_format": "%Y-%m-%d",
    "datetime_format": "%Y-%m-%d %H:%M:%S",
    "currency": "ريال سعودي",
    "decimal_places": 2,
}

# إعدادات قاعدة البيانات - Database Settings
DB_CONFIG = {
    "timeout": 30,
    "check_same_thread": False,
    "isolation_level": None,
    "detect_types": 1,  # PARSE_DECLTYPES
}

# إعدادات التقارير - Reports Settings
REPORTS_CONFIG = {
    "default_format": "PDF",
    "page_size": "A4",
    "orientation": "portrait",
    "margin_top": 2.5,
    "margin_bottom": 2.5,
    "margin_left": 2.0,
    "margin_right": 2.0,
    "font_name": "Arial",
    "font_size": 12,
    "header_font_size": 16,
    "title_font_size": 20,
    "watermark": True,
    "page_numbers": True,
    "company_logo": True,
}

# إعدادات الأمان - Security Settings
SECURITY_CONFIG = {
    "encryption_key": "stores_management_2024_key",
    "salt_rounds": 12,
    "session_secret": "stores_session_secret_key",
    "admin_username": "admin",
    "admin_default_password": "admin",
    "password_reset_enabled": True,
    "audit_log_enabled": True,
    "auto_logout_minutes": 0,  # تعطيل الإغلاق التلقائي (0 = معطل)
    "show_warning_before_logout": False,  # تعطيل تحذير الإغلاق
    "warning_time_seconds": 60  # عرض التحذير قبل 60 ثانية من الإغلاق
}

# إعدادات واجهة المستخدم - UI Settings
UI_CONFIG = {
    "primary_color": "#0a3d62",
    "secondary_color": "#40739e",
    "success_color": "#27ae60",
    "warning_color": "#f39c12",
    "danger_color": "#e74c3c",
    "info_color": "#3498db",
    "light_color": "#ecf0f1",
    "dark_color": "#2c3e50",
    "font_family": "Arial",  # خط ثابت - لا يتغير
    "font_size_small": 10,
    "font_size_normal": 12,
    "font_size_large": 14,
    "font_size_xlarge": 16,
    "button_padding": 10,
    "widget_padding": 5,
    "frame_padding": 15,
    "icon_size": 16,
    "large_icon_size": 32,
    "button_width": 15,
    "entry_width": 30,
}

# رسائل النظام - System Messages
MESSAGES = {
    "ar": {
        "success": "تم بنجاح",
        "error": "حدث خطأ",
        "warning": "تحذير",
        "info": "معلومات",
        "confirm": "تأكيد",
        "cancel": "إلغاء",
        "save": "حفظ",
        "delete": "حذف",
        "edit": "تعديل",
        "add": "إضافة",
        "search": "بحث",
        "print": "طباعة",
        "export": "تصدير",
        "import": "استيراد",
        "login_required": "يرجى تسجيل الدخول",
        "access_denied": "ليس لديك صلاحية للوصول",
        "invalid_credentials": "بيانات الدخول غير صحيحة",
        "session_expired": "انتهت صلاحية الجلسة",
        "data_saved": "تم حفظ البيانات بنجاح",
        "data_deleted": "تم حذف البيانات بنجاح",
        "confirm_delete": "هل أنت متأكد من الحذف؟",
        "no_data_found": "لا توجد بيانات",
        "invalid_input": "البيانات المدخلة غير صحيحة",
        "required_field": "هذا الحقل مطلوب",
        "duplicate_entry": "هذا الإدخال موجود مسبقاً",
        "low_stock_alert": "تحذير: كمية منخفضة في المخزون",
        "backup_created": "تم إنشاء نسخة احتياطية",
        "backup_restored": "تم استعادة النسخة الاحتياطية",
        "report_generated": "تم إنشاء التقرير بنجاح",
        "export_completed": "تم التصدير بنجاح",
        "import_completed": "تم الاستيراد بنجاح",
    }
}



# إعدادات التسجيل - Logging Configuration
LOGGING_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file_path": LOGS_DIR / "app.log",
    "max_file_size": 10 * 1024 * 1024,  # 10 MB
    "backup_count": 5,
    "console_output": True,
}

# إعدادات النسخ الاحتياطي - Backup Configuration
BACKUP_CONFIG = {
    "auto_backup": False,
    "backup_interval": 24,  # hours
    "max_backups": 30,
    "compress_backups": True,
    "backup_on_exit": False,
}

def get_config(section=None):
    """الحصول على إعدادات معينة"""
    if section:
        return globals().get(f"{section.upper()}_CONFIG", {})
    return APP_CONFIG

def get_message(key, language="ar"):
    """الحصول على رسالة بلغة معينة"""
    return MESSAGES.get(language, {}).get(key, key)

def get_static_data(key):
    """الحصول على البيانات الثابتة"""
    return STATIC_DATA.get(key, [])

def load_settings_from_file():
    """تحميل الإعدادات من ملف settings.json"""
    global APP_CONFIG, UI_CONFIG

    try:
        settings_file = BASE_DIR / "settings.json"
        if settings_file.exists():
            with open(settings_file, 'r', encoding='utf-8') as f:
                settings_data = json.load(f)

            # تحديث إعدادات التطبيق
            for key, value in settings_data.items():
                if key in APP_CONFIG:
                    APP_CONFIG[key] = value
                elif key in ['font_family', 'font_size', 'button_width', 'entry_width']:
                    # تحديث إعدادات واجهة المستخدم
                    if key == 'font_family':
                        # خط Arial ثابت - لا يتغير
                        UI_CONFIG['font_family'] = "Arial"
                    elif key == 'font_size':
                        UI_CONFIG['font_size_normal'] = value
                    elif key == 'button_width':
                        UI_CONFIG['button_width'] = value
                    elif key == 'entry_width':
                        UI_CONFIG['entry_width'] = value

            print(f"[نجح] تم تحميل الإعدادات من {settings_file}")
            return True

    except Exception as e:
        print(f"[تحذير] تعذر تحميل الإعدادات: {e}")
        return False

    return False

# تحميل الإعدادات عند استيراد الوحدة
load_settings_from_file()
