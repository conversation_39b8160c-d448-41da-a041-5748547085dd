# نظام إدارة المخازن والمستودعات
## Desktop Stores Management System

### 📋 وصف التطبيق
نظام شامل لإدارة المخازن والمستودعات يوفر جميع الأدوات اللازمة لتتبع المخزون وإدارة العمليات.

### ✨ المميزات الرئيسية
- 📦 إدارة شاملة للأصناف والمخزون
- 👥 إدارة المستفيدين والإدارات
- 📊 تقارير مفصلة ولوحة تحكم
- 🔒 نظام أمان متقدم مع صلاحيات المستخدمين
- 📈 تتبع حركات المخزون
- 💾 نسخ احتياطي تلقائي
- 📄 تصدير التقارير بصيغ مختلفة
- 🎨 واجهة مستخدم حديثة وسهلة الاستخدام

### 🚀 متطلبات التشغيل
- نظام التشغيل: Windows 10 أو أحدث
- الذاكرة: 4 GB RAM أو أكثر
- مساحة القرص: 500 MB مساحة فارغة
- دقة الشاشة: 1024x768 أو أعلى

### 📥 التثبيت والتشغيل

#### الطريقة الأولى: تشغيل مباشر
1. فك ضغط الملف المضغوط
2. انقر مرتين على `نظام_إدارة_المخازن.exe`
3. أو انقر على `تشغيل_البرنامج.bat`

#### الطريقة الثانية: من الكود المصدري
```bash
# تثبيت المتطلبات
pip install -r requirements.txt

# تشغيل التطبيق
python main.py
```

### 👤 بيانات الدخول الافتراضية
- **اسم المستخدم:** admin
- **كلمة المرور:** admin

⚠️ **مهم:** يرجى تغيير كلمة المرور الافتراضية بعد أول تسجيل دخول

### 📁 هيكل المجلدات
```
Desktop_Stores_Management/
├── نظام_إدارة_المخازن.exe    # الملف التنفيذي الرئيسي
├── تشغيل_البرنامج.bat        # ملف تشغيل سريع
├── data/                      # بيانات التطبيق
├── reports/                   # التقارير المُصدرة
├── backups/                   # النسخ الاحتياطية
├── logs/                      # ملفات السجل
└── assets/                    # الموارد والأيقونات
```

### 🔧 الإعدادات
- يتم حفظ الإعدادات تلقائياً في ملف `settings.json`
- قاعدة البيانات محفوظة في مجلد `data/`
- يمكن تخصيص الإعدادات من داخل التطبيق

### 📊 الوظائف الرئيسية

#### إدارة المخزون
- إضافة وتعديل الأصناف
- تتبع الكميات والحركات
- تنبيهات المخزون المنخفض
- تقارير حالة المخزون

#### إدارة العمليات
- عمليات الصرف والاستلام
- تسجيل المعاملات
- طباعة إيصالات العمليات
- تتبع تاريخ العمليات

#### التقارير
- تقارير المخزون
- تقارير العمليات
- تقارير المستفيدين
- تصدير بصيغ Excel و PDF

#### الأمان
- نظام مستخدمين متعدد
- صلاحيات مختلفة
- تسجيل العمليات
- نسخ احتياطي آمن

### 🛠️ استكشاف الأخطاء

#### مشاكل شائعة وحلولها

**المشكلة:** التطبيق لا يبدأ
**الحل:** 
- تأكد من وجود جميع الملفات
- شغل كمسؤول إذا لزم الأمر
- تحقق من ملفات السجل في مجلد `logs/`

**المشكلة:** بطء في الأداء
**الحل:**
- أغلق التطبيقات الأخرى
- تأكد من وجود مساحة كافية على القرص
- قم بإعادة تشغيل التطبيق

**المشكلة:** خطأ في قاعدة البيانات
**الحل:**
- استخدم النسخة الاحتياطية من مجلد `backups/`
- أو احذف ملف قاعدة البيانات لإنشاء قاعدة جديدة

### 📞 الدعم الفني
- تحقق من ملفات السجل في مجلد `logs/`
- احتفظ بنسخة احتياطية من بياناتك دورياً
- استخدم وظيفة النسخ الاحتياطي المدمجة

### 📝 ملاحظات مهمة
- يُنصح بعمل نسخة احتياطية دورية من البيانات
- لا تحذف مجلد `data/` إلا إذا كنت تريد مسح جميع البيانات
- يمكن نقل التطبيق بالكامل إلى جهاز آخر بنسخ المجلد كاملاً

### 🔄 التحديثات
- يتم إشعارك بالتحديثات الجديدة تلقائياً
- احتفظ بنسخة احتياطية قبل التحديث
- اتبع تعليمات التحديث المرفقة

---

**© 2025 Desktop Stores Team - جميع الحقوق محفوظة**