# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['run_app.py'],
    pathex=[],
    binaries=[],
    datas=[('assets', 'assets'), ('ui', 'ui'), ('utils', 'utils'), ('config.py', '.'), ('models.py', '.'), ('database.py', '.'), ('permissions_manager.py', '.'), ('activity_monitor.py', '.')],
    hiddenimports=['tkinter', 'tkinter.ttk', 'ttkbootstrap', 'PIL', 'PIL.Image', 'PIL.ImageTk', 'pandas', 'sqlite3', 'bcrypt', 'reportlab', 'matplotlib', 'openpyxl', 'xlsxwriter'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='نظام_إدارة_المخازن_والمستودعات',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=['assets\\app_icon.ico'],
)
coll = COLLECT(
    exe,
    a.binaries,
    a.datas,
    strip=False,
    upx=False,
    upx_exclude=[],
    name='نظام_إدارة_المخازن_والمستودعات',
)
