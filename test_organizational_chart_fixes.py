#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاحات الجدول التنظيمي
- اختبار إصلاح مشكلة اختفاء النافذة عند حذف جميع الأصناف
- اختبار إصلاح الترقيم التسلسلي ليبدأ من 1 عند الاستيراد
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models import OrganizationalChart
from database import db_manager
import pandas as pd
import tempfile

def test_sequence_numbering_reset():
    """اختبار إعادة ترقيم التسلسل"""
    print("🧪 اختبار إعادة ترقيم التسلسل...")
    
    try:
        # إنشاء بيانات اختبار
        test_items = [
            {"item_name": "صنف اختبار 1", "item_code": "T001", "quantity": 10},
            {"item_name": "صنف اختبار 2", "item_code": "T002", "quantity": 20},
            {"item_name": "صنف اختبار 3", "item_code": "T003", "quantity": 30},
        ]
        
        # حذف البيانات الموجودة
        print("🗑️ حذف البيانات الموجودة...")
        db_manager.execute_query("UPDATE organizational_chart SET is_active = 0")
        
        # إضافة بيانات اختبار بأرقام تسلسل عشوائية
        print("➕ إضافة بيانات اختبار...")
        for i, item in enumerate(test_items):
            # استخدام أرقام تسلسل عشوائية (100, 200, 300)
            sequence_number = (i + 1) * 100
            
            new_item = OrganizationalChart(
                sequence_number=sequence_number,
                item_name=item["item_name"],
                item_code=item["item_code"],
                quantity=item["quantity"],
                is_active=1
            )
            new_item.save()
            print(f"   ✅ تم إضافة: {item['item_name']} برقم تسلسل {sequence_number}")
        
        # عرض الأرقام التسلسلية قبل الإصلاح
        print("\n📊 الأرقام التسلسلية قبل الإصلاح:")
        items_before = db_manager.fetch_all("""
            SELECT sequence_number, item_name 
            FROM organizational_chart 
            WHERE is_active = 1 
            ORDER BY sequence_number
        """)
        for item in items_before:
            print(f"   {item[0]}: {item[1]}")
        
        # تطبيق إعادة الترقيم
        print("\n🔄 تطبيق إعادة الترقيم...")
        success = OrganizationalChart.reset_sequence_numbering_after_import()
        
        if success:
            print("✅ تم تطبيق إعادة الترقيم بنجاح")
            
            # عرض الأرقام التسلسلية بعد الإصلاح
            print("\n📊 الأرقام التسلسلية بعد الإصلاح:")
            items_after = db_manager.fetch_all("""
                SELECT sequence_number, item_name 
                FROM organizational_chart 
                WHERE is_active = 1 
                ORDER BY sequence_number
            """)
            for item in items_after:
                print(f"   {item[0]}: {item[1]}")
            
            # التحقق من صحة الترقيم
            expected_sequences = [1, 2, 3]
            actual_sequences = [item[0] for item in items_after]
            
            if actual_sequences == expected_sequences:
                print("✅ الترقيم التسلسلي صحيح - يبدأ من 1")
                return True
            else:
                print(f"❌ الترقيم التسلسلي خاطئ - متوقع: {expected_sequences}, فعلي: {actual_sequences}")
                return False
        else:
            print("❌ فشل في تطبيق إعادة الترقيم")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار إعادة الترقيم: {e}")
        return False

def test_get_next_sequence_number():
    """اختبار دالة الحصول على الرقم التسلسلي التالي"""
    print("\n🧪 اختبار دالة الحصول على الرقم التسلسلي التالي...")
    
    try:
        # حالة 1: لا توجد عناصر نشطة
        print("🔍 حالة 1: لا توجد عناصر نشطة...")
        db_manager.execute_query("UPDATE organizational_chart SET is_active = 0")
        
        next_number = OrganizationalChart.get_next_sequence_number()
        if next_number == 1:
            print("✅ الرقم التالي صحيح عند عدم وجود عناصر: 1")
        else:
            print(f"❌ الرقم التالي خاطئ عند عدم وجود عناصر: {next_number} (متوقع: 1)")
            return False
        
        # حالة 2: توجد عناصر نشطة
        print("\n🔍 حالة 2: توجد عناصر نشطة...")
        
        # إضافة عنصر واحد برقم تسلسل 5
        test_item = OrganizationalChart(
            sequence_number=5,
            item_name="صنف اختبار",
            item_code="T999",
            quantity=1,
            is_active=1
        )
        test_item.save()
        
        next_number = OrganizationalChart.get_next_sequence_number()
        if next_number == 6:
            print("✅ الرقم التالي صحيح عند وجود عناصر: 6")
            return True
        else:
            print(f"❌ الرقم التالي خاطئ عند وجود عناصر: {next_number} (متوقع: 6)")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار دالة الرقم التسلسلي: {e}")
        return False

def test_excel_import_with_sequence_reset():
    """اختبار استيراد Excel مع إعادة ترقيم التسلسل"""
    print("\n🧪 اختبار استيراد Excel مع إعادة ترقيم التسلسل...")
    
    try:
        # إنشاء ملف Excel اختبار
        test_data = {
            'اسم الصنف': ['صنف مستورد 1', 'صنف مستورد 2', 'صنف مستورد 3'],
            'رقم الصنف': ['IMP001', 'IMP002', 'IMP003'],
            'الكمية': [100, 200, 300],
            'اسم المعدة': ['معدة 1', 'معدة 2', 'معدة 3'],
            'ملاحظات': ['ملاحظة 1', 'ملاحظة 2', 'ملاحظة 3']
        }
        
        df = pd.DataFrame(test_data)
        
        # حفظ في ملف مؤقت
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
            df.to_excel(tmp_file.name, index=False)
            temp_file_path = tmp_file.name
        
        print(f"📁 تم إنشاء ملف Excel اختبار: {temp_file_path}")
        
        # حذف البيانات الموجودة
        db_manager.execute_query("UPDATE organizational_chart SET is_active = 0")
        
        # استيراد البيانات
        print("📥 بدء استيراد البيانات...")
        from utils.organizational_chart_import import import_organizational_chart_from_excel
        
        result = import_organizational_chart_from_excel(temp_file_path)
        
        if result.success_count > 0:
            print(f"✅ تم استيراد {result.success_count} عنصر بنجاح")
            
            # التحقق من الترقيم التسلسلي
            items = db_manager.fetch_all("""
                SELECT sequence_number, item_name 
                FROM organizational_chart 
                WHERE is_active = 1 
                ORDER BY sequence_number
            """)
            
            print("\n📊 الأرقام التسلسلية بعد الاستيراد:")
            for item in items:
                print(f"   {item[0]}: {item[1]}")
            
            # التحقق من أن الترقيم يبدأ من 1
            sequences = [item[0] for item in items]
            expected = list(range(1, len(items) + 1))
            
            if sequences == expected:
                print("✅ الترقيم التسلسلي صحيح بعد الاستيراد - يبدأ من 1")
                return True
            else:
                print(f"❌ الترقيم التسلسلي خاطئ - متوقع: {expected}, فعلي: {sequences}")
                return False
        else:
            print("❌ فشل في استيراد البيانات")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار استيراد Excel: {e}")
        return False
    finally:
        # حذف الملف المؤقت
        try:
            os.unlink(temp_file_path)
        except:
            pass

def main():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء اختبارات إصلاحات الجدول التنظيمي")
    print("=" * 60)
    
    tests = [
        ("اختبار إعادة ترقيم التسلسل", test_sequence_numbering_reset),
        ("اختبار دالة الرقم التسلسلي التالي", test_get_next_sequence_number),
        ("اختبار استيراد Excel مع إعادة الترقيم", test_excel_import_with_sequence_reset),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                print(f"✅ {test_name}: نجح")
                passed += 1
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {e}")
    
    print("\n" + "="*60)
    print(f"📊 النتائج النهائية: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت!")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت")
        return False

if __name__ == "__main__":
    main()
