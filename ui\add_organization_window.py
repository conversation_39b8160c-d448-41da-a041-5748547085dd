#!/usr/bin/env python3
"""
شاشة إضافة هيئة/إدارة/قسم جديد
Add Organization Window
"""

import tkinter as tk
from tkinter import ttk, messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *

from config import APP_CONFIG, UI_CONFIG, get_message
from models import Department, Section
from utils.window_utils import quick_center, setup_modal_window

class AddOrganizationWindow:
    """شاشة إضافة هيئة/إدارة/قسم جديد"""
    
    def __init__(self, parent, callback_window=None):
        self.parent = parent
        self.callback_window = callback_window  # النافذة التي استدعت هذه الشاشة
        self.org_window = None

        # متغيرات النموذج
        self.name_var = tk.StringVar()
        self.type_var = tk.StringVar()
        self.parent_unit_var = tk.StringVar()
        self.parent_dept_var = tk.StringVar()

        # تعيين القيم الافتراضية
        self.type_var.set("وحدة")

        self.setup_window()
    
    def setup_window(self):
        """إعداد النافذة"""
        self.org_window = tk.Toplevel(self.parent)

        # إعداد النافذة المنبثقة مع التوسيط
        setup_modal_window(
            self.org_window,
            self.parent,
            "➕ إضافة هيئة/إدارة/قسم جديد",
            400,
            300
        )

        # إعداد المحتوى
        self.setup_content()

        # توسيط نهائي مع جعل النافذة في المقدمة
        quick_center(self.org_window, 400, 300)
    


    def setup_content(self):
        """إعداد محتوى النافذة"""
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.org_window)
        main_frame.pack(fill=BOTH, expand=True, padx=20, pady=20)
        
        # العنوان
        title_label = ttk_bs.Label(
            main_frame,
            text="➕ إضافة هيئة/إدارة/قسم جديد",
            bootstyle="primary",
            anchor=CENTER
        )
        title_label.pack(pady=(0, 30))
        
        # إطار النموذج
        form_frame = ttk_bs.LabelFrame(main_frame, text="البيانات الأساسية", bootstyle="info")
        form_frame.pack(fill=BOTH, expand=True, pady=(0, 20))
        
        # الاسم
        name_frame = ttk_bs.Frame(form_frame)
        name_frame.pack(fill=X, padx=20, pady=20)
        
        ttk_bs.Label(name_frame, text="الاسم", width=15).pack(side=RIGHT, padx=(0, 10))
        
        name_entry = ttk_bs.Entry(
            name_frame,
            textvariable=self.name_var,
            width=30
        )
        name_entry.pack(side=RIGHT, fill=X, expand=True)
        name_entry.focus()
        
        # النوع
        type_frame = ttk_bs.Frame(form_frame)
        type_frame.pack(fill=X, padx=20, pady=20)

        ttk_bs.Label(type_frame, text="النوع:", width=10).pack(side=RIGHT, padx=(0, 10))

        type_combo = ttk_bs.Combobox(
            type_frame,
            textvariable=self.type_var,
            values=["إدارة", "قسم"],
            state="readonly",
            width=20
        )
        type_combo.pack(side=RIGHT)
        type_combo.bind('<<ComboboxSelected>>', self.on_type_change)

        # الوحدة الأصلية (للإدارات)
        self.unit_frame = ttk_bs.Frame(form_frame)
        self.unit_frame.pack(fill=X, padx=20, pady=10)

        ttk_bs.Label(self.unit_frame, text="الوحدة:", width=10).pack(side=RIGHT, padx=(0, 10))

        self.unit_combo = ttk_bs.Combobox(
            self.unit_frame,
            textvariable=self.parent_unit_var,
            state="readonly",
            width=20
        )
        self.unit_combo.pack(side=RIGHT)
        self.unit_combo.bind('<<ComboboxSelected>>', self.on_unit_change)

        # الإدارة الأصلية (للأقسام)
        self.dept_frame = ttk_bs.Frame(form_frame)
        self.dept_frame.pack(fill=X, padx=20, pady=10)

        ttk_bs.Label(self.dept_frame, text="الإدارة:", width=10).pack(side=RIGHT, padx=(0, 10))

        self.dept_combo = ttk_bs.Combobox(
            self.dept_frame,
            textvariable=self.parent_dept_var,
            state="readonly",
            width=20
        )
        self.dept_combo.pack(side=RIGHT)

        # إخفاء الإطارات في البداية
        self.unit_frame.pack_forget()
        self.dept_frame.pack_forget()

        # تحميل البيانات
        self.load_departments()
        
        # أزرار العمليات
        buttons_frame = ttk_bs.Frame(main_frame)
        buttons_frame.pack(fill=X, pady=(10, 0))
        
        # زر الحفظ
        save_btn = ttk_bs.Button(
            buttons_frame,
            text="💾 حفظ",
            command=self.save_organization,
            bootstyle="success",
            width=15
        )
        save_btn.pack(side=RIGHT, padx=(10, 0))
        
        # زر الإلغاء
        cancel_btn = ttk_bs.Button(
            buttons_frame,
            text="❌ إلغاء",
            command=self.close_window,
            bootstyle="secondary",
            width=15
        )
        cancel_btn.pack(side=RIGHT)
        
        # ربط مفتاح Enter بالحفظ
        self.org_window.bind('<Return>', lambda e: self.save_organization())
        self.org_window.bind('<Escape>', lambda e: self.close_window())

    def on_type_change(self, event=None):
        """معالج تغيير نوع الهيئة"""
        org_type = self.type_var.get()

        # إخفاء جميع الإطارات أولاً
        self.unit_frame.pack_forget()
        self.dept_frame.pack_forget()

        if org_type == "إدارة":
            # إظهار قائمة الوحدات
            self.unit_frame.pack(fill=X, padx=20, pady=10)
        elif org_type == "قسم":
            # إظهار قائمة الوحدات والإدارات
            self.unit_frame.pack(fill=X, padx=20, pady=10)
            self.dept_frame.pack(fill=X, padx=20, pady=10)




    def load_departments(self):
        """تحميل قائمة الإدارات"""
        try:
            departments = Department.get_all()
            dept_names = [dept.name for dept in departments]
            self.dept_combo['values'] = dept_names
        except Exception as e:
            print(f"خطأ في تحميل الإدارات: {e}")
    
    def save_organization(self):
        """حفظ الهيئة/الإدارة/القسم"""
        try:
            # التحقق من صحة البيانات
            name = self.name_var.get().strip()
            org_type = self.type_var.get()
            
            if not name:
                messagebox.showerror("خطأ", "يرجى إدخال الاسم")
                return
            
            if not org_type:
                messagebox.showerror("خطأ", "يرجى اختيار النوع")
                return
            
            # حفظ حسب النوع
            success = False

            if org_type == "إدارة":
                # حفظ كإدارة
                department = Department()
                department.name = name
                department.description = f"إدارة {name}"
                success = department.save()

            elif org_type == "قسم":
                # التحقق من اختيار الإدارة
                dept_name = self.parent_dept_var.get()
                if not dept_name:
                    messagebox.showerror("خطأ", "يرجى اختيار الإدارة")
                    return

                # البحث عن معرف الإدارة
                departments = Department.get_all()
                dept_id = None
                for dept in departments:
                    if dept.name == dept_name:
                        dept_id = dept.id
                        break

                if not dept_id:
                    messagebox.showerror("خطأ", "لم يتم العثور على الإدارة المختارة")
                    return

                # حفظ كقسم
                section = Section()
                section.name = name
                section.description = f"قسم {name}"
                section.department_id = dept_id
                success = section.save()
            
            if success:
                # رسالة نجاح
                messagebox.showinfo("نجح", f"تم إضافة {org_type} '{name}' بنجاح")
                
                # تحديث الشاشة الأصلية
                if self.callback_window and hasattr(self.callback_window, 'load_organizations'):
                    # البحث عن combobox الهيئات وتحديثه
                    for widget in self.callback_window.movement_window.winfo_children():
                        self.update_organization_combos(widget)
                
                # إغلاق النافذة
                self.close_window()
            else:
                messagebox.showerror("خطأ", f"فشل في حفظ {org_type}")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ البيانات: {e}")
            print(f"خطأ في الحفظ: {e}")
            import traceback
            traceback.print_exc()
    
    def update_organization_combos(self, widget):
        """تحديث قوائم الهيئات في النافذة الأصلية"""
        try:
            # البحث عن combobox الهيئات
            if isinstance(widget, ttk_bs.Combobox):
                # التحقق من أن هذا هو combobox الهيئات
                if hasattr(self.callback_window, 'load_organizations'):
                    self.callback_window.load_organizations(widget)

            # البحث في العناصر الفرعية
            for child in widget.winfo_children():
                self.update_organization_combos(child)

        except Exception as e:
            print(f"خطأ في تحديث القوائم: {e}")
    
    def close_window(self):
        """إغلاق النافذة"""
        self.org_window.destroy()

# اختبار النافذة
if __name__ == "__main__":
    root = tk.Tk()
    root.withdraw()  # إخفاء النافذة الرئيسية
    
    # إنشاء نافذة الاختبار
    window = AddOrganizationWindow(root, None)
    root.mainloop()
