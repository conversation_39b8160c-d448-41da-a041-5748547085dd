"""
شاشة إضافة مستخدم جديد - نسخة مبسطة
"""

import tkinter as tk
from tkinter import messagebox, ttk
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from datetime import datetime
import bcrypt

from models import User
from config import UI_CONFIG

class AddUserSimple:
    """شاشة إضافة مستخدم جديد - نسخة مبسطة"""
    
    def __init__(self, parent, main_window, user_id=None):
        self.parent = parent
        self.main_window = main_window
        self.user_id = user_id
        self.window = None
        
        # متغيرات النموذج
        self.username_var = tk.StringVar()
        self.full_name_var = tk.StringVar()
        self.password_var = tk.StringVar()
        self.confirm_password_var = tk.StringVar()
        self.is_admin_var = tk.BooleanVar()
        self.user_type_var = tk.StringVar(value="مستخدم")  # نوع المستخدم
        self.is_active_var = tk.BooleanVar(value=True)
        
        self.setup_window()
        self.load_user_data()
    
    def setup_window(self):
        """إعداد النافذة"""
        self.window = tk.Toplevel(self.parent)
        title = "إضافة مستخدم جديد" if self.user_id is None else "تعديل المستخدم"
        self.window.title(title)
        self.window.geometry("800x600")  # تكبير الشاشة للتخطيط الجديد
        self.window.resizable(True, True)  # السماح بتغيير الحجم
        
        # توسيط النافذة
        self.center_window()
        
        # إعداد المحتوى
        self.setup_content()
        
        # جعل النافذة في المقدمة
        self.window.lift()
        self.window.focus_force()
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() - 800) // 2
        y = (self.window.winfo_screenheight() - 600) // 2
        self.window.geometry(f"800x600+{x}+{y}")
    
    def setup_content(self):
        """إعداد محتوى النافذة"""
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.window)
        main_frame.pack(fill=BOTH, expand=True, padx=20, pady=20)
        
        # العنوان
        title_text = "إضافة مستخدم جديد" if self.user_id is None else "تعديل المستخدم"
        title_label = ttk_bs.Label(
            main_frame,
            text=title_text,
            bootstyle="primary"
        )
        title_label.pack(pady=(0, 20))

        # إطار النموذج
        form_frame = ttk_bs.LabelFrame(main_frame, text="بيانات المستخدم", padding=20)
        form_frame.pack(fill=BOTH, expand=True, pady=(0, 20))

        # الصف الأول: اسم المستخدم والاسم بالكامل
        row1_frame = ttk_bs.Frame(form_frame)
        row1_frame.pack(fill=X, pady=(0, 20))

        # اسم المستخدم (يسار)
        username_frame = ttk_bs.Frame(row1_frame)
        username_frame.pack(side=LEFT, fill=X, expand=True, padx=(0, 10))

        ttk_bs.Label(
            username_frame,
            text="اسم المستخدم"
        ).pack(pady=(0, 5))

        username_entry = ttk_bs.Entry(
            username_frame,
            textvariable=self.username_var,
            justify=CENTER
        )
        username_entry.pack(fill=X)

        # الاسم بالكامل (يمين)
        fullname_frame = ttk_bs.Frame(row1_frame)
        fullname_frame.pack(side=RIGHT, fill=X, expand=True, padx=(10, 0))

        ttk_bs.Label(
            fullname_frame,
            text="الاسم بالكامل"
        ).pack(pady=(0, 5))

        full_name_entry = ttk_bs.Entry(
            fullname_frame,
            textvariable=self.full_name_var,
            justify=CENTER
        )
        full_name_entry.pack(fill=X)

        # الصف الثاني: كلمة المرور (يسار فقط)
        row2_frame = ttk_bs.Frame(form_frame)
        row2_frame.pack(fill=X, pady=(0, 20))

        # كلمة المرور (يسار)
        password_frame = ttk_bs.Frame(row2_frame)
        password_frame.pack(side=LEFT, fill=X, expand=True, padx=(0, 10))

        password_text = "كلمة المرور الجديدة" if self.user_id else "كلمة المرور"
        ttk_bs.Label(
            password_frame,
            text=password_text
        ).pack(pady=(0, 5))

        password_entry = ttk_bs.Entry(
            password_frame,
            textvariable=self.password_var,
            show="*",
            justify=CENTER
        )
        password_entry.pack(fill=X)

        # الصف الثالث: الحقول الباقية في خط مستقيم
        row3_frame = ttk_bs.Frame(form_frame)
        row3_frame.pack(fill=X, pady=(0, 20))

        # تأكيد كلمة المرور
        confirm_frame = ttk_bs.Frame(row3_frame)
        confirm_frame.pack(side=LEFT, fill=X, expand=True, padx=(0, 10))

        ttk_bs.Label(
            confirm_frame,
            text="تأكيد كلمة المرور"
        ).pack(pady=(0, 5))

        confirm_password_entry = ttk_bs.Entry(
            confirm_frame,
            textvariable=self.confirm_password_var,
            show="*",
            justify=CENTER
        )
        confirm_password_entry.pack(fill=X)

        # نوع المستخدم
        usertype_frame = ttk_bs.Frame(row3_frame)
        usertype_frame.pack(side=LEFT, fill=X, expand=True, padx=(5, 5))

        ttk_bs.Label(
            usertype_frame,
            text="نوع المستخدم"
        ).pack(pady=(0, 5))

        user_type_combo = ttk_bs.Combobox(
            usertype_frame,
            textvariable=self.user_type_var,
            values=["مدير", "مدير قسم", "مستخدم", "مشاهد"],
            state="readonly"
        )
        user_type_combo.pack(fill=X)
        user_type_combo.bind("<<ComboboxSelected>>", self.on_user_type_change)
        
        # الصف الرابع: خانات الاختيار
        row4_frame = ttk_bs.Frame(form_frame)
        row4_frame.pack(fill=X, pady=(20, 0))

        # مدير النظام
        admin_frame = ttk_bs.Frame(row4_frame)
        admin_frame.pack(side=LEFT, fill=X, expand=True, padx=(0, 10))

        admin_check = ttk_bs.Checkbutton(
            admin_frame,
            text="مدير النظام",
            variable=self.is_admin_var,
            bootstyle="primary",
            command=self.on_admin_change
        )
        admin_check.pack(pady=10)

        # مستخدم نشط
        active_frame = ttk_bs.Frame(row4_frame)
        active_frame.pack(side=LEFT, fill=X, expand=True, padx=(5, 0))

        active_check = ttk_bs.Checkbutton(
            active_frame,
            text="نشط",
            variable=self.is_active_var,
            bootstyle="success"
        )
        active_check.pack(pady=10)
        
        # زر الحفظ
        save_btn = ttk_bs.Button(
            main_frame,
            text="حفظ",
            command=self.save_user,
            bootstyle="success",
            width=15
        )
        save_btn.pack(pady=20)
        
        # التركيز على أول حقل
        username_entry.focus()

    def on_admin_change(self):
        """تغيير نوع المستخدم عند تغيير خانة مدير النظام"""
        if self.is_admin_var.get():
            self.user_type_var.set("مدير")
        else:
            self.user_type_var.set("مستخدم")

    def on_user_type_change(self, event=None):
        """تغيير خانة مدير النظام عند تغيير نوع المستخدم"""
        user_type = self.user_type_var.get()
        if user_type == "مدير":
            self.is_admin_var.set(True)
        else:
            self.is_admin_var.set(False)
    
    def load_user_data(self):
        """تحميل بيانات المستخدم للتعديل"""
        if self.user_id:
            try:
                user = User.get_by_id(self.user_id)
                if user:
                    self.username_var.set(user.username)
                    self.full_name_var.set(user.full_name)
                    self.is_admin_var.set(user.is_admin)
                    self.is_active_var.set(user.is_active)

                    # تحميل نوع المستخدم من جدول الأدوار
                    from database import db_manager
                    role_query = """
                        SELECT r.description FROM roles r
                        JOIN user_roles ur ON r.id = ur.role_id
                        WHERE ur.user_id = ?
                        LIMIT 1
                    """
                    role_row = db_manager.fetch_one(role_query, (self.user_id,))

                    if role_row and role_row["description"]:
                        self.user_type_var.set(role_row["description"])
                    elif user.is_admin:
                        self.user_type_var.set("مدير")
                    else:
                        self.user_type_var.set("مستخدم")

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في تحميل بيانات المستخدم: {e}")
                self.window.destroy()
    
    def save_user(self):
        """حفظ المستخدم"""
        try:
            # التحقق من البيانات المطلوبة
            if not self.username_var.get().strip():
                messagebox.showerror("خطأ", "يجب إدخال اسم المستخدم")
                return
            
            if not self.full_name_var.get().strip():
                messagebox.showerror("خطأ", "يجب إدخال الاسم بالكامل")
                return
            
            # التحقق من كلمة المرور للمستخدم الجديد
            if not self.user_id:
                if not self.password_var.get():
                    messagebox.showerror("خطأ", "يجب إدخال كلمة المرور")
                    return
                
                if self.password_var.get() != self.confirm_password_var.get():
                    messagebox.showerror("خطأ", "كلمة المرور وتأكيدها غير متطابقتين")
                    return
            
            # التحقق من كلمة المرور للمستخدم الموجود (إذا تم إدخالها)
            if self.user_id and self.password_var.get():
                if self.password_var.get() != self.confirm_password_var.get():
                    messagebox.showerror("خطأ", "كلمة المرور وتأكيدها غير متطابقتين")
                    return
            
            # تحديد نوع المستخدم بناءً على الاختيار
            user_type = self.user_type_var.get()
            is_admin = self.is_admin_var.get() or user_type == "مدير"

            # حفظ المستخدم
            if self.user_id:
                # تحديث مستخدم موجود
                user = User.get_by_id(self.user_id)
                if user:
                    user.username = self.username_var.get().strip()
                    user.full_name = self.full_name_var.get().strip()
                    user.is_admin = is_admin
                    user.is_active = self.is_active_var.get()

                    # تحديث كلمة المرور إذا تم إدخالها
                    if self.password_var.get():
                        user.set_password(self.password_var.get())

                    user.updated_at = datetime.now()

                    if user.save():
                        # تحديث دور المستخدم
                        self.save_user_role(user.id, user_type)
                        messagebox.showinfo("نجح", "تم تحديث المستخدم بنجاح")
                        self.window.destroy()
                        if hasattr(self.main_window, 'load_users'):
                            self.main_window.load_users()
                    else:
                        messagebox.showerror("خطأ", "فشل في تحديث المستخدم")
            else:
                # إنشاء مستخدم جديد
                user = User()
                user.username = self.username_var.get().strip()
                user.full_name = self.full_name_var.get().strip()
                user.is_admin = is_admin
                user.is_active = self.is_active_var.get()
                user.set_password(self.password_var.get())
                user.created_at = datetime.now()
                user.updated_at = datetime.now()

                if user.save():
                    # حفظ دور المستخدم
                    self.save_user_role(user.id, user_type)
                    messagebox.showinfo("نجح", "تم إضافة المستخدم بنجاح")
                    self.window.destroy()
                    if hasattr(self.main_window, 'load_users'):
                        self.main_window.load_users()
                else:
                    messagebox.showerror("خطأ", "فشل في إضافة المستخدم")
                    
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حفظ المستخدم: {e}")

    def save_user_role(self, user_id, user_type):
        """حفظ دور المستخدم"""
        try:
            from database import db_manager

            # حذف الأدوار الحالية للمستخدم
            db_manager.execute_query("DELETE FROM user_roles WHERE user_id = ?", (user_id,))

            # تحديد معرف الدور بناءً على نوع المستخدم
            role_mapping = {
                "مدير": "admin",
                "مدير قسم": "manager",
                "مستخدم": "user",
                "مشاهد": "viewer"
            }

            role_name = role_mapping.get(user_type, "user")

            # الحصول على معرف الدور
            role_row = db_manager.fetch_one("SELECT id FROM roles WHERE name = ?", (role_name,))

            if role_row:
                # إضافة الدور الجديد
                db_manager.execute_query(
                    "INSERT INTO user_roles (user_id, role_id) VALUES (?, ?)",
                    (user_id, role_row["id"])
                )

        except Exception as e:
            print(f"خطأ في حفظ دور المستخدم: {e}")

# اختبار الشاشة
if __name__ == "__main__":
    import sys
    import os
    from pathlib import Path

    # إضافة مسار المشروع إلى sys.path
    project_root = Path(__file__).parent.parent
    sys.path.insert(0, str(project_root))

    try:
        from database import db_manager
        from models import User

        # إنشاء النافذة الرئيسية
        root = ttk_bs.Window(
            title="اختبار شاشة إضافة المستخدم",
            themename="cosmo",
            size=(600, 700),
            resizable=(True, True)
        )

        # توسيط النافذة
        root.update_idletasks()
        screen_width = root.winfo_screenwidth()
        screen_height = root.winfo_screenheight()
        x = (screen_width - 600) // 2
        y = (screen_height - 700) // 2
        root.geometry(f"600x700+{x}+{y}")

        # إنشاء شاشة إضافة المستخدم
        app = AddUserSimple(root, None)

        # تشغيل التطبيق
        root.mainloop()

    except Exception as e:
        print(f"خطأ في تشغيل الاختبار: {e}")
        import traceback
        traceback.print_exc()

        # إنشاء نافذة بسيطة للاختبار
        root = tk.Tk()
        root.title("اختبار شاشة إضافة المستخدم")
        root.geometry("600x700")

        # توسيط النافذة
        screen_width = root.winfo_screenwidth()
        screen_height = root.winfo_screenheight()
        x = (screen_width - 600) // 2
        y = (screen_height - 700) // 2
        root.geometry(f"600x700+{x}+{y}")

        try:
            app = AddUserSimple(root, None)
            root.mainloop()
        except Exception as e2:
            print(f"خطأ في الاختبار البديل: {e2}")
            tk.Label(root, text=f"خطأ في تحميل الشاشة:\n{e2}", wraplength=500).pack(pady=50)
            root.mainloop()
